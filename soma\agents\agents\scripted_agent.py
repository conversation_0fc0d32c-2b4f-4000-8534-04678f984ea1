import random
from core.action import Action
from agents.base_agent import BaseAgent

class ScriptedAgent(BaseAgent):
    def __init__(self, player_color: str, strategy: str = "balanced"):
        super().__init__(player_color)
        self.strategy = strategy
        self.memory = {}  # Store observations and decisions
        self.suspicion_threshold = 0.7

    def choose_action(self, observation: dict) -> Action:
        """Enhanced rule-based logic with different strategies"""

        # Store observation in memory
        self.memory[observation["tick"]] = observation

        # Handle dead bodies first (highest priority)
        if observation.get("dead_bodies"):
            return Action("report")

        # Handle crisis sabotages
        crisis_timers = observation.get("crisis_timers", {})
        if crisis_timers:
            return self._handle_crisis(observation, crisis_timers)

        # Strategy-specific behavior
        if self.strategy == "aggressive_impostor":
            return self._aggressive_impostor_strategy(observation)
        elif self.strategy == "sneaky_impostor":
            return self._sneaky_impostor_strategy(observation)
        elif self.strategy == "detective_crewmate":
            return self._detective_crewmate_strategy(observation)
        elif self.strategy == "task_focused_crewmate":
            return self._task_focused_crewmate_strategy(observation)
        else:
            return self._balanced_strategy(observation)

    def _handle_crisis(self, observation: dict, crisis_timers: dict) -> Action:
        """Handle oxygen/reactor crisis"""
        room = observation["room"]

        # Try to fix crisis if in correct room
        if "oxygen" in crisis_timers:
            if room in ["O2", "Admin"]:
                return Action("fix_oxygen")
            else:
                # Move towards O2 or Admin
                if "O2" in observation["connected_rooms"]:
                    return Action("move", target="O2")
                elif "Admin" in observation["connected_rooms"]:
                    return Action("move", target="Admin")

        if "reactor" in crisis_timers:
            if room == "Reactor":
                return Action("fix_reactor")
            else:
                # Move towards Reactor
                if "Reactor" in observation["connected_rooms"]:
                    return Action("move", target="Reactor")

        # Default movement towards crisis rooms
        return self._move_towards_crisis_room(observation, crisis_timers)

    def _aggressive_impostor_strategy(self, observation: dict) -> Action:
        """Aggressive impostor: prioritize kills and sabotages"""

        # Kill if possible
        if observation.get("can_kill") and observation.get("players_in_room"):
            target = random.choice(observation["players_in_room"])
            return Action("kill", target=target)

        # Sabotage if possible (double-check we're actually an impostor)
        if observation.get("can_sabotage"):
            sabotages = ["sabotage_lights", "sabotage_oxygen", "sabotage_reactor"]
            return Action(random.choice(sabotages))

        # Vent to escape or find victims
        if observation.get("can_vent") and observation.get("vents"):
            return Action("vent", target=random.choice(observation["vents"]))

        # Move to find victims
        return self._move_to_populated_room(observation)

    def _sneaky_impostor_strategy(self, observation: dict) -> Action:
        """Sneaky impostor: blend in, fake tasks, strategic kills"""

        # Only kill when alone with one crewmate
        players_in_room = observation.get("players_in_room", [])
        if observation.get("can_kill") and len(players_in_room) == 1:
            return Action("kill", target=players_in_room[0])

        # Fake tasks to blend in
        if observation.get("task_here") and len(players_in_room) > 0:
            return Action("task")  # Fake task

        # Strategic sabotages when alone
        if observation.get("can_sabotage") and len(players_in_room) == 0:
            return Action("sabotage_lights")  # Subtle sabotage

        # Move strategically
        return self._move_strategically(observation)

    def _detective_crewmate_strategy(self, observation: dict) -> Action:
        """Detective crewmate: use security systems, track suspicious behavior"""

        room_features = observation.get("room_features", {})

        # Use security systems when available
        if room_features.get("security_cameras"):
            return Action("use_security")
        if room_features.get("admin_table"):
            return Action("use_admin")
        if room_features.get("vitals"):
            return Action("use_vitals")

        # Fix sabotages
        active_sabotages = observation.get("active_sabotages", [])
        if "lights" in active_sabotages and observation["room"] == "Electrical":
            return Action("fix_lights")
        if "communications" in active_sabotages and observation["room"] == "Communications":
            return Action("fix_comms")

        # Do tasks when safe
        if observation.get("task_here") and len(observation.get("players_in_room", [])) <= 1:
            return Action("task")

        # Move to security rooms
        security_rooms = ["Security", "Admin", "MedBay"]
        for room in security_rooms:
            if room in observation["connected_rooms"]:
                return Action("move", target=room)

        return self._move_strategically(observation)

    def _task_focused_crewmate_strategy(self, observation: dict) -> Action:
        """Task-focused crewmate: prioritize task completion"""

        # Fix critical sabotages first
        active_sabotages = observation.get("active_sabotages", [])
        if "oxygen" in active_sabotages or "reactor" in active_sabotages:
            return self._handle_crisis(observation, observation.get("crisis_timers", {}))

        # Do tasks
        if observation.get("task_here"):
            return Action("task")

        # Move to task locations
        return self._move_to_task_room(observation)

    def _balanced_strategy(self, observation: dict) -> Action:
        """Balanced strategy for any role"""

        # Fix critical sabotages
        active_sabotages = observation.get("active_sabotages", [])
        if "lights" in active_sabotages and observation["room"] == "Electrical":
            return Action("fix_lights")

        # Call emergency meeting if suspicious and button available
        if observation.get("can_emergency") and random.random() < 0.1:
            return Action("button")

        # Impostor actions
        if observation.get("can_kill") and observation.get("players_in_room"):
            # Only kill if not too risky
            if len(observation["players_in_room"]) == 1:
                target = observation["players_in_room"][0]
                return Action("kill", target=target)

        # Only impostors can sabotage
        if (observation.get("can_sabotage") and
            self.strategy in ["aggressive_impostor", "sneaky_impostor"] and
            random.random() < 0.3):
            sabotages = ["sabotage_lights", "sabotage_comms"]
            return Action(random.choice(sabotages))

        # Crewmate actions
        if observation.get("task_here"):
            return Action("task")

        # Movement
        if observation.get("connected_rooms"):
            return Action("move", target=random.choice(observation["connected_rooms"]))

        return Action("idle")

    def _move_to_populated_room(self, observation: dict) -> Action:
        """Move towards rooms with other players"""
        connected_rooms = observation.get("connected_rooms", [])
        if connected_rooms:
            return Action("move", target=random.choice(connected_rooms))
        return Action("idle")

    def _move_strategically(self, observation: dict) -> Action:
        """Move based on game state and strategy"""
        connected_rooms = observation.get("connected_rooms", [])
        if not connected_rooms:
            return Action("idle")

        # Prefer moving to central rooms
        central_rooms = ["Cafeteria", "Admin", "Electrical"]
        for room in central_rooms:
            if room in connected_rooms:
                return Action("move", target=room)

        return Action("move", target=random.choice(connected_rooms))

    def _move_to_task_room(self, observation: dict) -> Action:
        """Move towards rooms that likely have tasks"""
        connected_rooms = observation.get("connected_rooms", [])
        if not connected_rooms:
            return Action("idle")

        # Common task rooms
        task_rooms = ["Electrical", "Admin", "Navigation", "Weapons", "MedBay"]
        for room in task_rooms:
            if room in connected_rooms:
                return Action("move", target=room)

        return Action("move", target=random.choice(connected_rooms))

    def _move_towards_crisis_room(self, observation: dict, crisis_timers: dict) -> Action:
        """Move towards rooms that can fix crisis"""
        connected_rooms = observation.get("connected_rooms", [])
        if not connected_rooms:
            return Action("idle")

        if "oxygen" in crisis_timers:
            for room in ["O2", "Admin"]:
                if room in connected_rooms:
                    return Action("move", target=room)

        if "reactor" in crisis_timers:
            if "Reactor" in connected_rooms:
                return Action("move", target="Reactor")

        return Action("move", target=random.choice(connected_rooms))
