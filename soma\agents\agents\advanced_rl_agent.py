import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import deque
import random
import json

from agents.base_agent import BaseAgent
from core.action import Action, VALID_ACTIONS
from core.communication import Message, MessageType, CommunicationStrategy
from core.llm_communication import LLMCommunicationStrategy, OllamaClient

class MultiModalAmongUsNet(nn.Module):
    """Advanced neural network with separate heads for actions and communication"""
    
    def __init__(self, game_state_dim: int, comm_state_dim: int, action_dim: int, 
                 vocab_size: int = 1000, hidden_dim: int = 512):
        super(MultiModalAmongUsNet, self).__init__()
        
        # Game state encoder
        self.game_encoder = nn.Sequential(
            nn.Linear(game_state_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Communication state encoder (for processing discussion history)
        self.comm_encoder = nn.Sequential(
            nn.Linear(comm_state_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
        )
        
        # Social deduction encoder (suspicion, trust, behavioral patterns)
        self.social_encoder = nn.Sequential(
            nn.Linear(20, hidden_dim // 4),  # Social features
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim // 8),
            nn.ReLU(),
        )
        
        # Combined feature processing
        combined_dim = hidden_dim // 2 + hidden_dim // 4 + hidden_dim // 8
        self.fusion_layer = nn.Sequential(
            nn.Linear(combined_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
        )
        
        # Action value head (for game actions)
        self.action_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim)
        )
        
        # Communication strategy head (for discussion behavior)
        self.comm_strategy_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 10)  # Communication strategy logits
        )
        
        # Suspicion prediction head (predict other players' roles)
        self.suspicion_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 8)  # Max 8 other players
        )
        
        # State value head
        self.value_head = nn.Linear(hidden_dim, 1)
        
    def forward(self, game_state: torch.Tensor, comm_state: torch.Tensor, 
                social_state: torch.Tensor) -> Dict[str, torch.Tensor]:
        
        # Encode different modalities
        game_features = self.game_encoder(game_state)
        comm_features = self.comm_encoder(comm_state)
        social_features = self.social_encoder(social_state)
        
        # Fuse features
        combined = torch.cat([game_features, comm_features, social_features], dim=-1)
        fused_features = self.fusion_layer(combined)
        
        # Generate outputs
        outputs = {
            'action_values': self.action_head(fused_features),
            'comm_strategy': self.comm_strategy_head(fused_features),
            'suspicion_logits': self.suspicion_head(fused_features),
            'state_value': self.value_head(fused_features)
        }
        
        return outputs

class AdvancedRLAgent(BaseAgent):
    """Advanced RL agent with LLM communication and social deduction"""
    
    def __init__(self, player_color: str, role_name: str, 
                 learning_rate: float = 1e-4, epsilon: float = 0.1,
                 device: str = "cpu", ollama_client: Optional[OllamaClient] = None):
        super().__init__(player_color)
        self.role_name = role_name
        self.device = torch.device(device)
        self.epsilon = epsilon
        self.epsilon_decay = 0.9995
        self.epsilon_min = 0.05
        
        # LLM integration
        self.ollama_client = ollama_client
        self.llm_comm_strategy = None
        if ollama_client:
            self.llm_comm_strategy = LLMCommunicationStrategy(
                player_color, role_name, ollama_client
            )
        
        # Action mapping
        self.action_list = list(VALID_ACTIONS)
        self.action_to_idx = {action: idx for idx, action in enumerate(self.action_list)}
        self.idx_to_action = {idx: action for action, idx in self.action_to_idx.items()}
        
        # Network dimensions
        self.game_state_dim = 60
        self.comm_state_dim = 100
        self.action_dim = len(self.action_list)
        
        # Neural networks
        self.q_network = MultiModalAmongUsNet(
            self.game_state_dim, self.comm_state_dim, self.action_dim
        ).to(self.device)
        self.target_network = MultiModalAmongUsNet(
            self.game_state_dim, self.comm_state_dim, self.action_dim
        ).to(self.device)
        
        self.optimizer = torch.optim.AdamW(
            self.q_network.parameters(), lr=learning_rate, weight_decay=1e-5
        )
        self.scheduler = torch.optim.lr_scheduler.StepLR(
            self.optimizer, step_size=1000, gamma=0.95
        )
        
        # Copy weights to target network
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Advanced experience replay with prioritization
        self.replay_buffer = PrioritizedReplayBuffer(capacity=50000)
        self.batch_size = 64
        self.gamma = 0.99
        self.target_update_freq = 200
        self.step_count = 0
        
        # Training state
        self.training = True
        self.last_state = None
        self.last_action = None
        self.episode_rewards = []
        self.current_episode_reward = 0
        
        # Social deduction tracking
        self.suspicion_levels = {}  # Track suspicion of other players
        self.trust_levels = {}      # Track trust of other players
        self.behavioral_patterns = {}  # Track behavioral patterns
        self.communication_history = []
        self.voting_history = []
        
        # Performance metrics
        self.win_rate_history = []
        self.role_performance = {"Impostor": [], "Crewmate": []}
        
    def encode_state(self, observation: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Enhanced state encoding with social features"""
        
        # Game state features (enhanced)
        game_features = self._encode_game_state(observation)
        
        # Communication state features
        comm_features = self._encode_communication_state(observation)
        
        # Social deduction features
        social_features = self._encode_social_state(observation)
        
        game_tensor = torch.FloatTensor(game_features).unsqueeze(0).to(self.device)
        comm_tensor = torch.FloatTensor(comm_features).unsqueeze(0).to(self.device)
        social_tensor = torch.FloatTensor(social_features).unsqueeze(0).to(self.device)
        
        return game_tensor, comm_tensor, social_tensor
    
    def _encode_game_state(self, observation: Dict) -> List[float]:
        """Encode game state with enhanced features"""
        features = []
        
        # Basic game info
        features.extend([
            observation.get("tick", 0) / 200.0,
            len(observation.get("players_in_room", [])) / 10.0,
            len(observation.get("connected_rooms", [])) / 15.0,
            len(observation.get("dead_bodies", [])) / 5.0,
            observation.get("task_progress", 0.0),
            observation.get("completed_tasks", 0) / 50.0,
            observation.get("total_tasks", 0) / 50.0,
            observation.get("living_players", 0) / 10.0,
            observation.get("living_impostors", 0) / 3.0,
        ])
        
        # Player abilities
        abilities = [
            "can_kill", "can_vent", "can_sabotage", "can_shapeshift", 
            "can_protect", "can_emergency"
        ]
        for ability in abilities:
            features.append(1.0 if observation.get(ability, False) else 0.0)
        
        # Room features
        room_features = observation.get("room_features", {})
        room_abilities = [
            "emergency_button", "admin_table", "security_cameras", 
            "vitals", "lights_on", "doors_open"
        ]
        for feature in room_abilities:
            features.append(1.0 if room_features.get(feature, False) else 0.0)
        
        # Sabotage state
        sabotage_types = ["lights", "oxygen", "reactor", "communications", "doors"]
        active_sabotages = observation.get("active_sabotages", [])
        for sabotage in sabotage_types:
            features.append(1.0 if sabotage in active_sabotages else 0.0)
        
        # Crisis timers
        crisis_timers = observation.get("crisis_timers", {})
        for sabotage in ["oxygen", "reactor"]:
            timer = crisis_timers.get(sabotage, 0)
            features.append(timer / 30.0 if timer else 0.0)
        
        # Game phase indicators
        features.extend([
            1.0 if observation.get("in_meeting", False) else 0.0,
            1.0 if observation.get("in_discussion", False) else 0.0,
            1.0 if observation.get("in_voting", False) else 0.0,
        ])
        
        # Recent events
        features.extend([
            1.0 if observation.get("recent_kill", False) else 0.0,
            1.0 if observation.get("recent_sabotage", False) else 0.0,
            1.0 if observation.get("recent_task", False) else 0.0,
        ])
        
        # Pad to fixed size
        while len(features) < self.game_state_dim:
            features.append(0.0)
        
        return features[:self.game_state_dim]
    
    def _encode_communication_state(self, observation: Dict) -> List[float]:
        """Encode communication and discussion state"""
        features = [0.0] * self.comm_state_dim
        
        # Recent communication patterns
        if len(self.communication_history) > 0:
            recent_messages = self.communication_history[-10:]  # Last 10 messages
            
            # Message type distribution
            message_types = {}
            for msg in recent_messages:
                msg_type = msg.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            # Encode message type frequencies
            for i, msg_type in enumerate(['accusation', 'defense', 'alibi', 'question']):
                if i < 20:  # First 20 features for message types
                    features[i] = message_types.get(msg_type, 0) / max(len(recent_messages), 1)
        
        # Voting patterns
        if len(self.voting_history) > 0:
            recent_votes = self.voting_history[-5:]  # Last 5 votes
            for i, vote in enumerate(recent_votes):
                if i < 10:  # Next 10 features for voting
                    features[20 + i] = 1.0 if vote.get('target') == self.player_color else 0.0
        
        return features
    
    def _encode_social_state(self, observation: Dict) -> List[float]:
        """Encode social deduction features"""
        features = []
        
        # Average suspicion level of others towards us
        avg_suspicion = np.mean(list(self.suspicion_levels.values())) if self.suspicion_levels else 0.0
        features.append(avg_suspicion)
        
        # Average trust level we have towards others
        avg_trust = np.mean(list(self.trust_levels.values())) if self.trust_levels else 0.5
        features.append(avg_trust)
        
        # Number of accusations made against us
        accusations_against = sum(1 for msg in self.communication_history[-20:] 
                                if msg.get('target') == self.player_color and 
                                msg.get('type') == 'accusation')
        features.append(accusations_against / 10.0)  # Normalize
        
        # Number of accusations we've made
        accusations_made = sum(1 for msg in self.communication_history[-20:] 
                             if msg.get('sender') == self.player_color and 
                             msg.get('type') == 'accusation')
        features.append(accusations_made / 10.0)
        
        # Social isolation metric (how often we're alone)
        isolation_score = observation.get("isolation_score", 0.0)
        features.append(isolation_score)
        
        # Pad to 20 features
        while len(features) < 20:
            features.append(0.0)
        
        return features[:20]
    
    def choose_action(self, observation: Dict) -> Action:
        """Choose action using advanced policy"""
        
        # Encode state
        game_state, comm_state, social_state = self.encode_state(observation)
        
        # Get network outputs
        with torch.no_grad():
            outputs = self.q_network(game_state, comm_state, social_state)
            action_values = outputs['action_values']
            suspicion_logits = outputs['suspicion_logits']
        
        # Update suspicion levels based on network predictions
        self._update_suspicion_from_network(suspicion_logits, observation)
        
        # Epsilon-greedy with advanced exploration
        if self.training and random.random() < self.epsilon:
            # Smart exploration: bias towards role-appropriate actions
            action_idx = self._smart_exploration(observation)
        else:
            # Greedy action selection
            action_idx = action_values.argmax().item()
        
        # Convert to action
        action_type = self.idx_to_action[action_idx]
        target = self._get_action_target(action_type, observation)
        
        # Store for learning
        if self.training:
            self.last_state = (game_state, comm_state, social_state)
            self.last_action = action_idx
        
        return Action(action_type, target=target)
    
    def _smart_exploration(self, observation: Dict) -> int:
        """Smart exploration biased towards role-appropriate actions"""
        
        # Get role-appropriate action weights
        if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
            # Impostor actions
            preferred_actions = ["kill", "sabotage_lights", "sabotage_oxygen", "vent"]
        else:
            # Crewmate actions
            preferred_actions = ["task", "use_admin", "use_security", "report"]
        
        # Create weighted distribution
        weights = []
        for action in self.action_list:
            if action in preferred_actions:
                weights.append(3.0)  # Higher weight for preferred actions
            elif observation.get(f"can_{action.split('_')[0]}", False):
                weights.append(1.0)  # Normal weight for available actions
            else:
                weights.append(0.1)  # Low weight for unavailable actions
        
        # Sample from weighted distribution
        action_idx = np.random.choice(len(self.action_list), p=np.array(weights)/np.sum(weights))
        return action_idx
    
    def generate_communication_strategy(self, observation: Dict, discussion_context: Dict) -> List[Message]:
        """Generate communication using LLM or learned strategy"""
        
        if self.llm_comm_strategy:
            # Use LLM for communication
            game_state = self._build_llm_game_state(observation)
            return self.llm_comm_strategy.generate_messages(game_state, discussion_context)
        else:
            # Use learned communication strategy
            return self._generate_learned_communication(observation, discussion_context)
    
    def _generate_learned_communication(self, observation: Dict, discussion_context: Dict) -> List[Message]:
        """Generate communication using learned neural strategy"""
        
        game_state, comm_state, social_state = self.encode_state(observation)
        
        with torch.no_grad():
            outputs = self.q_network(game_state, comm_state, social_state)
            comm_strategy = outputs['comm_strategy']
        
        # Convert strategy logits to messages (simplified)
        strategy_probs = F.softmax(comm_strategy, dim=-1)
        strategy_idx = strategy_probs.argmax().item()
        
        # Map strategy to message type
        strategy_map = {
            0: MessageType.ALIBI,
            1: MessageType.ACCUSATION,
            2: MessageType.QUESTION,
            3: MessageType.DEFENSE,
            4: MessageType.INFORMATION,
        }
        
        message_type = strategy_map.get(strategy_idx, MessageType.INFORMATION)
        
        # Generate appropriate message content (simplified)
        content = self._generate_message_content(message_type, observation)
        
        if content:
            return [Message(self.player_color, message_type, content, confidence=0.8)]
        
        return []
    
    def _generate_message_content(self, message_type: MessageType, observation: Dict) -> str:
        """Generate message content based on type and game state"""
        
        if message_type == MessageType.ALIBI:
            locations = ["Admin", "Electrical", "MedBay", "Navigation"]
            tasks = ["wires", "card swipe", "scan", "course"]
            return f"I was doing {random.choice(tasks)} in {random.choice(locations)}"
        
        elif message_type == MessageType.ACCUSATION:
            players = observation.get("living_players", [])
            if len(players) > 1:
                target = random.choice([p for p in players if p != self.player_color])
                return f"{target} is acting suspicious"
        
        elif message_type == MessageType.QUESTION:
            players = observation.get("living_players", [])
            if len(players) > 1:
                target = random.choice([p for p in players if p != self.player_color])
                return f"Where were you {target}?"
        
        return "I'm not sure what happened"

class PrioritizedReplayBuffer:
    """Prioritized experience replay buffer"""
    
    def __init__(self, capacity: int = 50000, alpha: float = 0.6):
        self.capacity = capacity
        self.alpha = alpha
        self.buffer = []
        self.priorities = []
        self.position = 0
        
    def push(self, state, action, reward, next_state, done, td_error=1.0):
        """Add experience with priority"""
        priority = (abs(td_error) + 1e-6) ** self.alpha
        
        if len(self.buffer) < self.capacity:
            self.buffer.append((state, action, reward, next_state, done))
            self.priorities.append(priority)
        else:
            self.buffer[self.position] = (state, action, reward, next_state, done)
            self.priorities[self.position] = priority
            
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: int, beta: float = 0.4):
        """Sample batch with importance sampling"""
        if len(self.buffer) < batch_size:
            return None
            
        priorities = np.array(self.priorities[:len(self.buffer)])
        probs = priorities / priorities.sum()
        
        indices = np.random.choice(len(self.buffer), batch_size, p=probs)
        samples = [self.buffer[idx] for idx in indices]
        
        # Importance sampling weights
        weights = (len(self.buffer) * probs[indices]) ** (-beta)
        weights = weights / weights.max()
        
        return samples, indices, weights
    
    def update_priorities(self, indices, td_errors):
        """Update priorities based on TD errors"""
        for idx, td_error in zip(indices, td_errors):
            self.priorities[idx] = (abs(td_error) + 1e-6) ** self.alpha
    
    def __len__(self):
        return len(self.buffer)
