#!/usr/bin/env python3
"""
Dynamic Among Us Game Observer.

Uses dynamic state detection to automatically discover new game states
and room locations without manual programming.
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.screen_capture import ScreenCapture
from soma.computer_vision.dynamic_state_detector import DynamicStateDetector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DynamicGameObserver:
    """Observes Among Us gameplay with dynamic state and room discovery."""
    
    def __init__(self, fps: int = 5):
        self.fps = fps
        
        # Initialize components
        self.screen_capture = ScreenCapture(target_fps=fps)
        self.dynamic_detector = DynamicStateDetector(similarity_threshold=0.85)
        
        # Data collection
        self.observations = []
        self.discovery_log = []
        
        # Statistics
        self.stats = {
            'total_observations': 0,
            'unique_states_discovered': 0,
            'unique_rooms_discovered': 0,
            'start_time': None,
            'end_time': None
        }
    
    def initialize(self) -> bool:
        """Initialize the observer."""
        try:
            if not self.screen_capture.find_among_us_window():
                print("❌ Among Us window not found")
                print("   Make sure Among Us is running and visible")
                return False
            
            print("✅ Among Us window found")
            print(f"   Window: {self.screen_capture.window_region}")
            print(f"   Scale factor: {self.screen_capture.scale_factor}")
            
            # Try to load previous discoveries
            if self.dynamic_detector.load_discoveries('data/dynamic_discoveries.json'):
                print("📚 Loaded previous discoveries")
            else:
                print("🆕 Starting fresh discovery session")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def start_observation(self, duration_minutes: int = 10):
        """Start observing the game with dynamic discovery."""
        try:
            print(f"🔍 Starting dynamic game observation for {duration_minutes} minutes...")
            print("   🧠 AI will automatically discover new game states and rooms")
            print("   📊 Watch for discovery notifications")
            print("   Press Ctrl+C to stop early")
            print()
            
            self.stats['start_time'] = time.time()
            end_time = self.stats['start_time'] + (duration_minutes * 60)
            
            last_state = None
            last_room = None
            discovery_count = 0
            
            while time.time() < end_time:
                try:
                    # Capture frame
                    frame = self.screen_capture.capture_frame()
                    if frame is None:
                        time.sleep(0.1)
                        continue
                    
                    # Analyze with dynamic detection
                    state_id, room_id, confidence = self.dynamic_detector.analyze_frame(frame)
                    
                    # Check for new discoveries
                    if state_id != last_state:
                        if last_state is not None:
                            print(f"   🔄 State change: {last_state} → {state_id} (conf: {confidence:.2f})")
                        else:
                            print(f"   🎯 Initial state: {state_id} (conf: {confidence:.2f})")
                        
                        last_state = state_id
                        
                        # Check if this is a new discovery
                        if state_id.startswith('dynamic_state_'):
                            discovery_count += 1
                            self.discovery_log.append({
                                'type': 'state',
                                'id': state_id,
                                'timestamp': time.time(),
                                'confidence': confidence
                            })
                            print(f"   🆕 NEW STATE DISCOVERED: {state_id}")
                    
                    if room_id != last_room and room_id != "unknown":
                        if last_room is not None:
                            print(f"   🏠 Room change: {last_room} → {room_id}")
                        else:
                            print(f"   🏠 Room detected: {room_id}")
                        
                        last_room = room_id
                        
                        # Check if this is a new room discovery
                        if room_id.startswith('room_'):
                            discovery_count += 1
                            self.discovery_log.append({
                                'type': 'room',
                                'id': room_id,
                                'timestamp': time.time()
                            })
                            print(f"   🆕 NEW ROOM DISCOVERED: {room_id}")
                    
                    # Create observation
                    observation = {
                        'timestamp': time.time(),
                        'state_id': state_id,
                        'room_id': room_id,
                        'confidence': float(confidence),
                        'frame_analysis': {
                            'has_frame': True,
                            'frame_size': frame.shape if frame is not None else None
                        }
                    }
                    
                    # Store observation
                    self.observations.append(observation)
                    
                    # Update statistics
                    self.stats['total_observations'] += 1
                    
                    # Show periodic updates
                    if self.stats['total_observations'] % 50 == 0:
                        remaining = (end_time - time.time()) / 60
                        discovered_states = len(self.dynamic_detector.discovered_states)
                        discovered_rooms = len(self.dynamic_detector.room_signatures)
                        
                        print(f"   📊 {self.stats['total_observations']} observations, "
                              f"{discovered_states} states, {discovered_rooms} rooms, "
                              f"{remaining:.1f}min remaining")
                    
                    # Wait for next frame
                    time.sleep(1.0 / self.fps)
                    
                except KeyboardInterrupt:
                    print("\n⏹️  Observation stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error during observation: {e}")
                    time.sleep(1.0)
            
            self.stats['end_time'] = time.time()
            self.stats['unique_states_discovered'] = len(self.dynamic_detector.discovered_states)
            self.stats['unique_rooms_discovered'] = len(self.dynamic_detector.room_signatures)
            
            print("\n✅ Dynamic observation completed!")
            self.show_discovery_summary()
            
        except Exception as e:
            print(f"❌ Observation failed: {e}")
    
    def show_discovery_summary(self):
        """Show summary of discoveries."""
        if self.stats['start_time'] is None:
            return
        
        total_time = self.stats['end_time'] - self.stats['start_time']
        
        print("\n🧠 Dynamic Discovery Summary")
        print("=" * 35)
        print(f"Total time: {total_time/60:.1f} minutes")
        print(f"Total observations: {self.stats['total_observations']}")
        print(f"Average FPS: {self.stats['total_observations']/total_time:.1f}")
        print(f"States discovered: {self.stats['unique_states_discovered']}")
        print(f"Rooms discovered: {self.stats['unique_rooms_discovered']}")
        print(f"New discoveries: {len(self.discovery_log)}")
        
        # Show discovered states
        discovered_states = self.dynamic_detector.get_discovered_states()
        if discovered_states:
            print(f"\n📋 Discovered States:")
            for state_id, info in discovered_states.items():
                print(f"  • {info['name']} (freq: {info['frequency']}, conf: {info['confidence']:.2f})")
        
        # Show discovered rooms
        discovered_rooms = self.dynamic_detector.get_discovered_rooms()
        if discovered_rooms:
            print(f"\n🏠 Discovered Rooms:")
            for room_id, info in discovered_rooms.items():
                print(f"  • {info['name']} (conf: {info['confidence']:.2f})")
        
        # Show recent discoveries
        if self.discovery_log:
            print(f"\n🆕 Recent Discoveries:")
            for discovery in self.discovery_log[-5:]:  # Last 5 discoveries
                discovery_time = datetime.fromtimestamp(discovery['timestamp']).strftime("%H:%M:%S")
                print(f"  • {discovery['type'].title()}: {discovery['id']} at {discovery_time}")
    
    def save_data(self, filepath: str = None):
        """Save collected data and discoveries."""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"dynamic_observations_{timestamp}.json"
        
        try:
            # Prepare data
            data = {
                'metadata': {
                    'observation_type': 'dynamic_discovery',
                    'fps': self.fps,
                    'total_observations': len(self.observations),
                    'observation_duration': self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] else 0,
                    'states_discovered': self.stats['unique_states_discovered'],
                    'rooms_discovered': self.stats['unique_rooms_discovered']
                },
                'statistics': self.stats,
                'observations': self.observations,
                'discoveries': self.discovery_log,
                'discovered_states': self.dynamic_detector.get_discovered_states(),
                'discovered_rooms': self.dynamic_detector.get_discovered_rooms()
            }
            
            # Save main data
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"💾 Data saved to: {filepath}")
            print(f"   File size: {os.path.getsize(filepath) / 1024:.1f} KB")
            
            # Save discoveries separately for reuse
            os.makedirs('data', exist_ok=True)
            self.dynamic_detector.save_discoveries('data/dynamic_discoveries.json')
            print(f"💾 Discoveries saved to: data/dynamic_discoveries.json")
            
        except Exception as e:
            print(f"❌ Failed to save data: {e}")
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.screen_capture.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Main observation function."""
    print("🧠 Among Us Dynamic Game Observer")
    print("=" * 40)
    print("This AI automatically discovers new game states and rooms!")
    print("No manual programming needed - it learns as it watches.")
    print()
    
    # Get settings
    duration_input = input("Observation duration in minutes [10]: ").strip()
    try:
        duration = int(duration_input) if duration_input else 10
    except ValueError:
        duration = 10
    
    fps_input = input("Observation FPS [5]: ").strip()
    try:
        fps = int(fps_input) if fps_input else 5
    except ValueError:
        fps = 5
    
    print(f"\nSettings:")
    print(f"  Duration: {duration} minutes")
    print(f"  FPS: {fps}")
    print()
    
    print("💡 Tips for best results:")
    print("  • Play normally - visit different rooms and game states")
    print("  • Go through complete games (lobby → game → meeting → voting)")
    print("  • Try different tasks and interactions")
    print("  • The AI learns more from variety!")
    print()
    
    input("Make sure Among Us is running and press Enter to start...")
    print()
    
    # Start observation
    observer = DynamicGameObserver(fps)
    
    try:
        if observer.initialize():
            observer.start_observation(duration)
            observer.save_data()
        else:
            print("❌ Failed to initialize observer")
    
    except KeyboardInterrupt:
        print("\n⏹️  Observation interrupted")
    except Exception as e:
        print(f"❌ Observation error: {e}")
    finally:
        observer.cleanup()


if __name__ == "__main__":
    main()
