# SOMA Training Modes & Features Guide

## 🎮 Available Training Modes

### 📚 **Curriculum Mode** (Default)
**Best for**: Beginners, structured learning
**Characteristics**:
- Gradual difficulty progression
- Stable learning curve
- Lower variance in performance
- Systematic skill building

```bash
# Basic curriculum training
python train.py --mode curriculum --episodes 200 --complexity basic

# Intermediate curriculum
python train.py --mode curriculum --episodes 300 --complexity intermediate
```

### 🤖 **Self-Play Mode**
**Best for**: Advanced agents, emergent strategies
**Characteristics**:
- Multiple RL agents learning together
- Faster improvement but higher variance
- More realistic opponent behavior
- Emergent strategic development

```bash
# Basic self-play
python train.py --mode self_play --episodes 200 --num-rl-agents 6

# Advanced self-play with LLM
python train.py --mode self_play --episodes 300 --num-rl-agents 8 --llm --complexity advanced
```

### 🔀 **Mixed Mode**
**Best for**: Balanced training, versatility
**Characteristics**:
- Combines curriculum structure with self-play dynamics
- Balanced improvement rate
- Moderate variance
- Best of both approaches

```bash
# Mixed training
python train.py --mode mixed --episodes 250 --complexity intermediate

# Advanced mixed training
python train.py --mode mixed --episodes 400 --complexity advanced --llm
```

## 🎭 Complexity Levels

### 🔰 **Basic Complexity**
**Roles**: Crewmate, Impostor
**Best for**: Learning fundamentals
**Target Win Rate**: 45-60%

```bash
python train.py --complexity basic --episodes 300
```

### 📈 **Intermediate Complexity**
**Roles**: Crewmate, Impostor, Engineer, Scientist
**Best for**: Expanding role knowledge
**Target Win Rate**: 50-65%

```bash
python train.py --complexity intermediate --episodes 400
```

### 🏆 **Advanced Complexity**
**Roles**: All 8+ roles including Shapeshifter, Guardian Angel, Tracker, Phantom
**Best for**: Mastery-level training
**Target Win Rate**: 60-80%

```bash
python train.py --complexity advanced --episodes 500
```

## 📋 Ruleset Options

### 🎯 **Standard Ruleset** (Default)
- Balanced game settings
- Default Among Us configuration
- Good for general training

### 🏆 **Competitive Ruleset**
- Tournament-style settings
- Challenging for experienced agents
- Shorter kill cooldowns, limited meetings

```bash
python train.py --ruleset competitive --mode self_play
```

### 😊 **Casual Ruleset**
- Easier settings for learning
- Longer discussions, more meetings
- Good for early training

```bash
python train.py --ruleset casual --complexity basic
```

### 🎲 **Random Ruleset**
- Randomized settings each game
- Builds adaptability
- Prepares for varied lobbies

```bash
python train.py --ruleset random --episodes 300
```

## 🗣️ LLM Communication

### 💬 **DeepSeek-R1 Integration**
- Natural language chat during games
- Role-appropriate dialogue
- 50-character limit (authentic Among Us)
- Context-aware responses

```bash
# Enable LLM communication
python train.py --llm --mode mixed --episodes 200

# Advanced LLM training
python train.py --llm --mode self_play --complexity advanced --episodes 400
```

## 📊 Training Mode Comparison

| Mode | Learning Speed | Variance | Best For | Opponent Quality |
|------|----------------|----------|----------|------------------|
| Curriculum | Moderate | Low | Beginners | Predictable |
| Self-Play | Fast | High | Advanced | Adaptive |
| Mixed | Balanced | Medium | All Levels | Varied |

## 🎯 Recommended Training Progressions

### 🌱 **Beginner Path** (0-45% Win Rate)
```bash
# Phase 1: Foundation (200 episodes)
python train.py --mode curriculum --complexity basic --episodes 200

# Phase 2: Expansion (300 episodes)  
python train.py --mode curriculum --complexity intermediate --episodes 300

# Phase 3: Challenge (200 episodes)
python train.py --mode mixed --complexity intermediate --episodes 200
```

### 📈 **Intermediate Path** (45-65% Win Rate)
```bash
# Phase 1: Mixed Training (300 episodes)
python train.py --mode mixed --complexity intermediate --episodes 300

# Phase 2: Self-Play Introduction (200 episodes)
python train.py --mode self_play --complexity intermediate --episodes 200

# Phase 3: Advanced Complexity (300 episodes)
python train.py --mode mixed --complexity advanced --episodes 300
```

### 🏆 **Advanced Path** (65%+ Win Rate)
```bash
# Phase 1: Advanced Self-Play (400 episodes)
python train.py --mode self_play --complexity advanced --episodes 400

# Phase 2: LLM Integration (300 episodes)
python train.py --mode self_play --complexity advanced --llm --episodes 300

# Phase 3: Competitive Training (500 episodes)
python train.py --mode self_play --complexity advanced --llm --ruleset competitive --episodes 500
```

## 🔧 Advanced Configuration

### 🤖 **Self-Play Agent Configuration**
```bash
# Small lobby (6 agents)
python train.py --mode self_play --num-rl-agents 6

# Medium lobby (10 agents)  
python train.py --mode self_play --num-rl-agents 10

# Large lobby (15 agents)
python train.py --mode self_play --num-rl-agents 15
```

### 💾 **Custom Analytics Directory**
```bash
# Save to custom directory
python train.py --save-dir my_training_data --episodes 200

# View custom analytics
python scripts/advanced_analytics_viewer.py --analytics-dir my_training_data
```

## 📊 Performance Monitoring

### 📈 **Real-Time Analytics**
```bash
# View current performance
python scripts/advanced_analytics_viewer.py

# Generate visual dashboard
python scripts/advanced_analytics_viewer.py --dashboard

# Export analysis
python scripts/advanced_analytics_viewer.py --export-json analysis.json
```

### 🎯 **Training Recommendations**
The analytics system automatically recommends:
- **Mode changes** based on performance plateaus
- **Complexity increases** when ready
- **Training duration** adjustments
- **Focus areas** for improvement

## 🚀 Example Training Sessions

### 🔰 **Quick Start Session**
```bash
# 50 episodes, basic training
python train.py --episodes 50 --complexity basic
```

### 📚 **Learning Session**
```bash
# 200 episodes, mixed mode, intermediate complexity
python train.py --mode mixed --episodes 200 --complexity intermediate
```

### 🏆 **Advanced Session**
```bash
# 500 episodes, self-play, all features
python train.py --mode self_play --episodes 500 --complexity advanced --llm --ruleset competitive --num-rl-agents 12
```

### 🧪 **Experimental Session**
```bash
# Random rules, large lobby, LLM enabled
python train.py --mode self_play --episodes 300 --complexity advanced --llm --ruleset random --num-rl-agents 15
```

## 🎮 Training Mode Selection Guide

### **Choose Curriculum When:**
- Win rate < 45%
- Learning basic mechanics
- Want stable, predictable progress
- First time training

### **Choose Mixed When:**
- Win rate 45-65%
- Want balanced training
- Ready for some challenge
- Transitioning to advanced training

### **Choose Self-Play When:**
- Win rate > 60%
- Want maximum challenge
- Ready for emergent strategies
- Preparing for human opponents

## 📋 Feature Matrix

| Feature | Curriculum | Mixed | Self-Play |
|---------|------------|-------|-----------|
| Structured Learning | ✅ | ✅ | ❌ |
| Adaptive Opponents | ❌ | ✅ | ✅ |
| Fast Improvement | ❌ | ✅ | ✅ |
| Stable Progress | ✅ | ✅ | ❌ |
| Emergent Strategies | ❌ | ✅ | ✅ |
| Beginner Friendly | ✅ | ✅ | ❌ |
| Advanced Challenge | ❌ | ✅ | ✅ |

## 🎯 Success Metrics

### **Training Mode Effectiveness**
- **Curriculum**: Consistent 2-3% win rate improvement per 100 episodes
- **Mixed**: Balanced improvement with occasional breakthroughs
- **Self-Play**: Rapid improvement with higher variance

### **Complexity Readiness**
- **Basic → Intermediate**: 50%+ win rate, stable for 50+ episodes
- **Intermediate → Advanced**: 60%+ win rate, multiple role competency
- **Advanced → Expert**: 70%+ win rate, ready for human opponents

---

**The enhanced training system now supports all major Among Us AI training approaches with intelligent analytics guiding your progression!** 🚀🎮🧠
