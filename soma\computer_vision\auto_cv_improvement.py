"""
Automated Computer Vision Improvement System.

This system automatically improves CV accuracy through:
- Self-supervised learning from temporal consistency
- Confidence-based filtering and refinement
- Adaptive threshold optimization
- Pattern recognition and validation
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import deque
import json
import os

from .screen_capture import ScreenCapture
from .game_detector import GameStateDetector, GameState
from .player_detector import PlayerDetector, PlayerColor

logger = logging.getLogger(__name__)


@dataclass
class ConsistencyCheck:
    """Temporal consistency validation."""
    detection_type: str
    current_value: Any
    previous_values: List[Any]
    confidence_scores: List[float]
    consistency_score: float


class AutoCVImprovement:
    """Automatically improves CV accuracy through self-learning."""
    
    def __init__(self, window_size: int = 10):
        self.window_size = window_size
        
        # CV components
        self.screen_capture = ScreenCapture(target_fps=10)
        self.game_detector = GameStateDetector()
        self.player_detector = PlayerDetector()
        
        # Temporal tracking
        self.detection_history = {
            'game_states': deque(maxlen=window_size),
            'player_counts': deque(maxlen=window_size),
            'player_positions': deque(maxlen=window_size),
            'ui_elements': deque(maxlen=window_size)
        }
        
        # Confidence tracking
        self.confidence_history = {
            'game_state': deque(maxlen=window_size),
            'players': deque(maxlen=window_size),
            'ui_elements': deque(maxlen=window_size)
        }
        
        # Learning parameters
        self.consistency_threshold = 0.8
        self.confidence_improvement_rate = 0.05
        self.threshold_adjustment_rate = 0.02
        
        # Performance tracking
        self.improvement_metrics = {
            'consistency_scores': [],
            'confidence_trends': [],
            'threshold_adjustments': [],
            'detection_stability': []
        }
        
        # Adaptive parameters
        self.adaptive_params = {
            'game_state_threshold': 0.7,
            'player_confidence_threshold': 0.6,
            'ui_confidence_threshold': 0.5,
            'temporal_weight': 0.3,
            'stability_bonus': 0.1
        }
    
    def start_continuous_improvement(self, duration_hours: float = 1.0):
        """Start continuous improvement process."""
        try:
            print(f"🔄 Starting continuous CV improvement for {duration_hours} hours...")
            
            if not self.screen_capture.find_among_us_window():
                print("❌ Among Us window not found")
                return False
            
            end_time = time.time() + (duration_hours * 3600)
            iteration = 0
            
            while time.time() < end_time:
                # Capture and analyze
                frame = self.screen_capture.capture_frame()
                if frame is not None:
                    self._process_frame_for_improvement(frame)
                    iteration += 1
                    
                    # Periodic improvement updates
                    if iteration % 50 == 0:
                        self._update_adaptive_parameters()
                        self._log_improvement_progress(iteration)
                    
                    # Save progress periodically
                    if iteration % 200 == 0:
                        self._save_improvement_progress()
                
                time.sleep(0.1)  # 10 FPS
            
            print("✅ Continuous improvement completed!")
            self._show_improvement_summary()
            return True
            
        except Exception as e:
            logger.error(f"Error in continuous improvement: {e}")
            return False
    
    def _process_frame_for_improvement(self, frame: np.ndarray):
        """Process a single frame for improvement learning."""
        try:
            # Get current detections
            game_state_info = self.game_detector.detect_game_state(frame)
            players = self.player_detector.detect_players(frame)
            ui_elements = self.player_detector.detect_ui_elements(frame)
            
            # Store detections
            self.detection_history['game_states'].append(game_state_info.state)
            self.detection_history['player_counts'].append(len(players))
            self.detection_history['player_positions'].append([p.position for p in players])
            self.detection_history['ui_elements'].append(len(ui_elements))
            
            # Store confidence scores
            self.confidence_history['game_state'].append(game_state_info.confidence)
            self.confidence_history['players'].append(
                np.mean([p.confidence for p in players]) if players else 0.0
            )
            self.confidence_history['ui_elements'].append(
                np.mean([ui.confidence for ui in ui_elements]) if ui_elements else 0.0
            )
            
            # Perform consistency checks
            if len(self.detection_history['game_states']) >= 5:
                self._check_temporal_consistency()
            
            # Update confidence estimates
            self._update_confidence_estimates()
            
        except Exception as e:
            logger.error(f"Error processing frame for improvement: {e}")
    
    def _check_temporal_consistency(self):
        """Check temporal consistency of detections."""
        try:
            consistency_checks = []
            
            # Game state consistency
            recent_states = list(self.detection_history['game_states'])[-5:]
            state_consistency = self._calculate_sequence_consistency(recent_states)
            
            consistency_checks.append(ConsistencyCheck(
                detection_type='game_state',
                current_value=recent_states[-1],
                previous_values=recent_states[:-1],
                confidence_scores=list(self.confidence_history['game_state'])[-5:],
                consistency_score=state_consistency
            ))
            
            # Player count consistency
            recent_counts = list(self.detection_history['player_counts'])[-5:]
            count_consistency = self._calculate_numeric_consistency(recent_counts)
            
            consistency_checks.append(ConsistencyCheck(
                detection_type='player_count',
                current_value=recent_counts[-1],
                previous_values=recent_counts[:-1],
                confidence_scores=list(self.confidence_history['players'])[-5:],
                consistency_score=count_consistency
            ))
            
            # Use consistency to adjust confidence
            for check in consistency_checks:
                self._apply_consistency_feedback(check)
            
        except Exception as e:
            logger.error(f"Error checking temporal consistency: {e}")
    
    def _calculate_sequence_consistency(self, sequence: List[Any]) -> float:
        """Calculate consistency score for a sequence of values."""
        if len(sequence) < 2:
            return 1.0
        
        # Count how many values match the most common value
        from collections import Counter
        counts = Counter(sequence)
        most_common_count = counts.most_common(1)[0][1]
        
        return most_common_count / len(sequence)
    
    def _calculate_numeric_consistency(self, sequence: List[float]) -> float:
        """Calculate consistency score for numeric sequence."""
        if len(sequence) < 2:
            return 1.0
        
        # Use coefficient of variation (std/mean) as inconsistency measure
        mean_val = np.mean(sequence)
        if mean_val == 0:
            return 1.0 if np.std(sequence) == 0 else 0.0
        
        cv = np.std(sequence) / mean_val
        return max(0.0, 1.0 - cv)  # Higher consistency = lower variation
    
    def _apply_consistency_feedback(self, check: ConsistencyCheck):
        """Apply consistency feedback to improve future detections."""
        try:
            # If consistency is high and confidence is low, boost confidence
            if check.consistency_score > self.consistency_threshold:
                avg_confidence = np.mean(check.confidence_scores)
                
                if avg_confidence < 0.8:  # Room for improvement
                    # Boost confidence for this detection type
                    if check.detection_type == 'game_state':
                        self.adaptive_params['game_state_threshold'] *= (1 - self.confidence_improvement_rate)
                    elif check.detection_type == 'player_count':
                        self.adaptive_params['player_confidence_threshold'] *= (1 - self.confidence_improvement_rate)
            
            # If consistency is low, be more conservative
            elif check.consistency_score < 0.5:
                if check.detection_type == 'game_state':
                    self.adaptive_params['game_state_threshold'] *= (1 + self.confidence_improvement_rate)
                elif check.detection_type == 'player_count':
                    self.adaptive_params['player_confidence_threshold'] *= (1 + self.confidence_improvement_rate)
            
            # Clamp thresholds to reasonable ranges
            self.adaptive_params['game_state_threshold'] = np.clip(
                self.adaptive_params['game_state_threshold'], 0.3, 0.9
            )
            self.adaptive_params['player_confidence_threshold'] = np.clip(
                self.adaptive_params['player_confidence_threshold'], 0.3, 0.8
            )
            
        except Exception as e:
            logger.error(f"Error applying consistency feedback: {e}")
    
    def _update_confidence_estimates(self):
        """Update confidence estimates based on temporal patterns."""
        try:
            # Calculate stability bonus for consistent detections
            if len(self.confidence_history['game_state']) >= 3:
                recent_confidences = list(self.confidence_history['game_state'])[-3:]
                confidence_stability = 1.0 - np.std(recent_confidences)
                
                # Apply stability bonus to thresholds
                stability_adjustment = confidence_stability * self.adaptive_params['stability_bonus']
                
                self.adaptive_params['game_state_threshold'] *= (1 - stability_adjustment * 0.1)
                self.adaptive_params['player_confidence_threshold'] *= (1 - stability_adjustment * 0.1)
            
        except Exception as e:
            logger.error(f"Error updating confidence estimates: {e}")
    
    def _update_adaptive_parameters(self):
        """Update adaptive parameters based on learning."""
        try:
            # Apply learned parameters to detectors
            self.game_detector.confidence_threshold = self.adaptive_params['game_state_threshold']
            self.player_detector.confidence_threshold = self.adaptive_params['player_confidence_threshold']
            
            # Track parameter changes
            self.improvement_metrics['threshold_adjustments'].append({
                'timestamp': time.time(),
                'game_state_threshold': self.adaptive_params['game_state_threshold'],
                'player_threshold': self.adaptive_params['player_confidence_threshold']
            })
            
        except Exception as e:
            logger.error(f"Error updating adaptive parameters: {e}")
    
    def _log_improvement_progress(self, iteration: int):
        """Log improvement progress."""
        try:
            if len(self.confidence_history['game_state']) > 0:
                avg_game_confidence = np.mean(list(self.confidence_history['game_state']))
                avg_player_confidence = np.mean(list(self.confidence_history['players']))
                
                print(f"   Iteration {iteration}: Game State Conf: {avg_game_confidence:.2f}, "
                      f"Player Conf: {avg_player_confidence:.2f}")
            
        except Exception as e:
            logger.error(f"Error logging progress: {e}")
    
    def _show_improvement_summary(self):
        """Show improvement summary."""
        try:
            print("\n📈 Improvement Summary")
            print("=" * 25)
            
            if self.improvement_metrics['threshold_adjustments']:
                initial = self.improvement_metrics['threshold_adjustments'][0]
                final = self.improvement_metrics['threshold_adjustments'][-1]
                
                print("Threshold Changes:")
                print(f"  Game State: {initial['game_state_threshold']:.3f} → {final['game_state_threshold']:.3f}")
                print(f"  Player Detection: {initial['player_threshold']:.3f} → {final['player_threshold']:.3f}")
            
            if len(self.confidence_history['game_state']) > 10:
                initial_conf = np.mean(list(self.confidence_history['game_state'])[:10])
                final_conf = np.mean(list(self.confidence_history['game_state'])[-10:])
                
                print(f"\nConfidence Improvement:")
                print(f"  Initial: {initial_conf:.2f}")
                print(f"  Final: {final_conf:.2f}")
                print(f"  Change: {final_conf - initial_conf:+.2f}")
            
        except Exception as e:
            logger.error(f"Error showing improvement summary: {e}")
    
    def _save_improvement_progress(self):
        """Save improvement progress to file."""
        try:
            progress_data = {
                'adaptive_params': self.adaptive_params,
                'improvement_metrics': self.improvement_metrics,
                'timestamp': time.time()
            }
            
            os.makedirs('data/cv_improvement', exist_ok=True)
            
            with open('data/cv_improvement/auto_improvement_progress.json', 'w') as f:
                json.dump(progress_data, f, indent=2)
            
        except Exception as e:
            logger.error(f"Error saving improvement progress: {e}")
    
    def load_improvement_progress(self) -> bool:
        """Load previous improvement progress."""
        try:
            progress_file = 'data/cv_improvement/auto_improvement_progress.json'
            
            if not os.path.exists(progress_file):
                return False
            
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
            
            self.adaptive_params.update(progress_data.get('adaptive_params', {}))
            self.improvement_metrics.update(progress_data.get('improvement_metrics', {}))
            
            # Apply loaded parameters
            self.game_detector.confidence_threshold = self.adaptive_params['game_state_threshold']
            self.player_detector.confidence_threshold = self.adaptive_params['player_confidence_threshold']
            
            print("✅ Loaded previous improvement progress")
            return True
            
        except Exception as e:
            logger.error(f"Error loading improvement progress: {e}")
            return False
    
    def get_current_performance_estimate(self) -> Dict[str, float]:
        """Get current performance estimates."""
        try:
            if not self.confidence_history['game_state']:
                return {}
            
            return {
                'avg_game_state_confidence': np.mean(list(self.confidence_history['game_state'])),
                'avg_player_confidence': np.mean(list(self.confidence_history['players'])),
                'avg_ui_confidence': np.mean(list(self.confidence_history['ui_elements'])),
                'detection_stability': self._calculate_overall_stability(),
                'current_thresholds': self.adaptive_params.copy()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance estimate: {e}")
            return {}
    
    def _calculate_overall_stability(self) -> float:
        """Calculate overall detection stability."""
        try:
            stabilities = []
            
            # Game state stability
            if len(self.detection_history['game_states']) > 1:
                recent_states = list(self.detection_history['game_states'])[-5:]
                stabilities.append(self._calculate_sequence_consistency(recent_states))
            
            # Player count stability
            if len(self.detection_history['player_counts']) > 1:
                recent_counts = list(self.detection_history['player_counts'])[-5:]
                stabilities.append(self._calculate_numeric_consistency(recent_counts))
            
            return np.mean(stabilities) if stabilities else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating stability: {e}")
            return 0.0
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.screen_capture.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
