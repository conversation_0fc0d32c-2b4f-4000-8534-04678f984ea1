#!/usr/bin/env python3
"""
Test script to verify emergency button restrictions work correctly.
"""

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from core.action import Action
from core.sabotage import SabotageType
from agents.base_agent import BaseAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

class TestAgent(BaseAgent):
    def __init__(self, player_color: str, test_action: str = "idle"):
        super().__init__(player_color)
        self.test_action = test_action
        
    def choose_action(self, observation: dict) -> Action:
        if self.test_action == "emergency":
            return Action("button")
        elif self.test_action == "sabotage":
            return Action("sabotage_lights")
        else:
            return Action("idle")

def test_emergency_button_restrictions():
    print("🧪 Testing Emergency Button Restrictions")
    print("=" * 50)
    
    # Setup game
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    # Create test players
    players = [
        Player("Red", CREWMATE, cafeteria, TestAgent("Red", "emergency")),
        Player("Blue", IMPOSTOR, cafeteria, TestAgent("Blue", "sabotage")),
    ]
    
    game = SimulatedGame(players, settings)
    
    # Test 1: Emergency button should work when no sabotages
    print("\n📋 Test 1: Emergency button with no active sabotages")
    red_obs = game._get_observation(players[0])
    print(f"Can use emergency button: {red_obs.get('can_emergency', False)}")
    print(f"Active sabotages: {red_obs.get('active_sabotages', [])}")
    
    # Test 2: Activate sabotage and check emergency button
    print("\n📋 Test 2: Emergency button with active sabotage")
    game.sabotage_manager.activate_sabotage(SabotageType.LIGHTS)
    red_obs = game._get_observation(players[0])
    print(f"Can use emergency button: {red_obs.get('can_emergency', False)}")
    print(f"Active sabotages: {red_obs.get('active_sabotages', [])}")

    # Test 3: Try to use emergency button during sabotage
    print("\n📋 Test 3: Attempting to use emergency button during sabotage")
    original_meetings = len([p for p in game.players if hasattr(p.current_room, 'reported_body')])
    game._handle_emergency_button_action(players[0])
    new_meetings = len([p for p in game.players if hasattr(p.current_room, 'reported_body')])
    print(f"Meeting triggered: {new_meetings > original_meetings}")

    # Test 4: Fix sabotage and check emergency button again
    print("\n📋 Test 4: Emergency button after fixing sabotage")
    game.sabotage_manager.fix_sabotage(SabotageType.LIGHTS, "Electrical")
    red_obs = game._get_observation(players[0])
    print(f"Can use emergency button: {red_obs.get('can_emergency', False)}")
    print(f"Active sabotages: {red_obs.get('active_sabotages', [])}")
    
    print("\n✅ Emergency button restriction tests completed!")

if __name__ == "__main__":
    test_emergency_button_restrictions()
