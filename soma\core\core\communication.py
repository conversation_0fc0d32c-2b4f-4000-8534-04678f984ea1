from typing import List, Dict, Optional, Tuple
from enum import Enum
import random

class MessageType(Enum):
    ACCUSATION = "accusation"
    DEFENSE = "defense"
    ALIBI = "alibi"
    SUSPICION = "suspicion"
    INFORMATION = "information"
    QUESTION = "question"
    AGREEMENT = "agreement"
    DISAGREEMENT = "disagreement"
    VOTE_INTENTION = "vote_intention"

class Message:
    def __init__(self, sender: str, message_type: MessageType, content: str, 
                 target: Optional[str] = None, confidence: float = 1.0):
        self.sender = sender
        self.message_type = message_type
        self.content = content
        self.target = target  # Who the message is about (for accusations, etc.)
        self.confidence = confidence  # How confident the sender is (0.0-1.0)
        self.timestamp = 0  # Will be set by the communication manager
        
    def __repr__(self):
        target_str = f" about {self.target}" if self.target else ""
        return f"{self.sender}: [{self.message_type.value}]{target_str} '{self.content}'"

class CommunicationManager:
    def __init__(self):
        self.messages: List[Message] = []
        self.current_discussion_round = 0
        self.discussion_active = False
        
    def start_discussion(self):
        """Start a new discussion phase"""
        self.discussion_active = True
        self.current_discussion_round += 1
        self.messages.clear()
        
    def end_discussion(self):
        """End the current discussion phase"""
        self.discussion_active = False
        
    def add_message(self, message: Message):
        """Add a message to the current discussion"""
        if self.discussion_active:
            message.timestamp = len(self.messages)
            self.messages.append(message)
            
    def get_messages(self) -> List[Message]:
        """Get all messages from current discussion"""
        return self.messages.copy()
        
    def get_messages_about(self, target: str) -> List[Message]:
        """Get all messages about a specific player"""
        return [msg for msg in self.messages if msg.target == target]
        
    def get_messages_from(self, sender: str) -> List[Message]:
        """Get all messages from a specific player"""
        return [msg for msg in self.messages if msg.sender == sender]
        
    def get_accusations(self) -> List[Message]:
        """Get all accusation messages"""
        return [msg for msg in self.messages if msg.message_type == MessageType.ACCUSATION]
        
    def get_alibis(self) -> List[Message]:
        """Get all alibi messages"""
        return [msg for msg in self.messages if msg.message_type == MessageType.ALIBI]

class CommunicationStrategy:
    """Base class for communication strategies"""
    
    def __init__(self, player_color: str, role_name: str):
        self.player_color = player_color
        self.role_name = role_name
        self.memory: Dict = {}
        
    def generate_messages(self, game_state: Dict, discussion_context: Dict) -> List[Message]:
        """Generate messages based on game state and discussion context"""
        raise NotImplementedError
        
    def respond_to_message(self, message: Message, game_state: Dict) -> Optional[Message]:
        """Generate a response to a specific message"""
        raise NotImplementedError

class CrewmateStrategy(CommunicationStrategy):
    """Communication strategy for crewmates"""
    
    def generate_messages(self, game_state: Dict, discussion_context: Dict) -> List[Message]:
        messages = []
        
        # Share information about what we saw
        if game_state.get("saw_kill"):
            target = game_state["saw_kill"]["killer"]
            messages.append(Message(
                self.player_color, MessageType.ACCUSATION,
                f"I saw {target} kill {game_state['saw_kill']['victim']} in {game_state['saw_kill']['location']}!",
                target=target, confidence=1.0
            ))
            
        # Share alibi information
        if game_state.get("was_doing_task"):
            task_info = game_state["was_doing_task"]
            messages.append(Message(
                self.player_color, MessageType.ALIBI,
                f"I was doing {task_info['task']} in {task_info['location']}",
                confidence=0.8
            ))
            
        # Share suspicions based on behavior
        if game_state.get("suspicious_behavior"):
            for suspect, reason in game_state["suspicious_behavior"].items():
                messages.append(Message(
                    self.player_color, MessageType.SUSPICION,
                    f"{suspect} was acting suspicious: {reason}",
                    target=suspect, confidence=0.6
                ))
                
        # Ask questions about others' whereabouts
        if random.random() < 0.3:  # 30% chance to ask a question
            living_players = game_state.get("living_players", [])
            if living_players:
                target = random.choice([p for p in living_players if p != self.player_color])
                messages.append(Message(
                    self.player_color, MessageType.QUESTION,
                    f"Where were you {target}? What were you doing?",
                    target=target, confidence=0.5
                ))
                
        return messages
        
    def respond_to_message(self, message: Message, game_state: Dict) -> Optional[Message]:
        # Defend against accusations
        if message.message_type == MessageType.ACCUSATION and message.target == self.player_color:
            return Message(
                self.player_color, MessageType.DEFENSE,
                f"That's not true! I was doing tasks in {game_state.get('last_location', 'somewhere')}",
                confidence=1.0
            )
            
        # Answer questions directed at us
        if message.message_type == MessageType.QUESTION and message.target == self.player_color:
            location = game_state.get("last_location", "around the ship")
            task = game_state.get("last_task", "tasks")
            return Message(
                self.player_color, MessageType.INFORMATION,
                f"I was in {location} doing {task}",
                confidence=0.8
            )
            
        # Agree with accusations against suspicious players
        if message.message_type == MessageType.ACCUSATION:
            suspicion_level = game_state.get("suspicion_levels", {}).get(message.target, 0.0)
            if suspicion_level > 0.7:
                return Message(
                    self.player_color, MessageType.AGREEMENT,
                    f"I agree, {message.target} has been acting suspicious too",
                    target=message.target, confidence=suspicion_level
                )
                
        return None

class ImpostorStrategy(CommunicationStrategy):
    """Communication strategy for impostors"""
    
    def generate_messages(self, game_state: Dict, discussion_context: Dict) -> List[Message]:
        messages = []
        
        # Create fake alibis
        fake_locations = ["Admin", "Electrical", "MedBay", "Navigation", "Weapons"]
        fake_tasks = ["wires", "card swipe", "scan", "course", "asteroids"]
        
        if random.random() < 0.7:  # 70% chance to give fake alibi
            location = random.choice(fake_locations)
            task = random.choice(fake_tasks)
            messages.append(Message(
                self.player_color, MessageType.ALIBI,
                f"I was doing {task} in {location}",
                confidence=0.9  # Act confident
            ))
            
        # Deflect suspicion onto innocent players
        if game_state.get("under_suspicion", False):
            living_players = game_state.get("living_players", [])
            innocent_players = [p for p in living_players 
                             if p != self.player_color and p not in game_state.get("impostor_team", [])]
            if innocent_players:
                target = random.choice(innocent_players)
                reasons = ["acting weird", "following me", "not doing tasks", "being quiet"]
                reason = random.choice(reasons)
                messages.append(Message(
                    self.player_color, MessageType.SUSPICION,
                    f"Actually, I think {target} is suspicious. They were {reason}",
                    target=target, confidence=0.8
                ))
                
        # Support accusations against crewmates
        accusations = discussion_context.get("accusations", [])
        for acc in accusations:
            if acc.target != self.player_color and acc.target not in game_state.get("impostor_team", []):
                if random.random() < 0.4:  # 40% chance to support
                    messages.append(Message(
                        self.player_color, MessageType.AGREEMENT,
                        f"Yeah, {acc.target} was acting suspicious earlier too",
                        target=acc.target, confidence=0.7
                    ))
                    
        return messages
        
    def respond_to_message(self, message: Message, game_state: Dict) -> Optional[Message]:
        # Strongly defend against accusations
        if message.message_type == MessageType.ACCUSATION and message.target == self.player_color:
            return Message(
                self.player_color, MessageType.DEFENSE,
                f"What?! No way! {message.sender} is probably the impostor trying to frame me!",
                target=message.sender, confidence=1.0
            )
            
        # Give fake answers to questions
        if message.message_type == MessageType.QUESTION and message.target == self.player_color:
            fake_locations = ["Admin", "Electrical", "MedBay", "Navigation"]
            fake_tasks = ["wires", "card swipe", "scan", "course"]
            location = random.choice(fake_locations)
            task = random.choice(fake_tasks)
            return Message(
                self.player_color, MessageType.INFORMATION,
                f"I was in {location} doing {task}, obviously",
                confidence=0.9
            )
            
        # Deflect accusations onto the accuser
        if message.message_type == MessageType.ACCUSATION and random.random() < 0.3:
            return Message(
                self.player_color, MessageType.SUSPICION,
                f"Why are you so quick to accuse, {message.sender}? That's pretty sus",
                target=message.sender, confidence=0.6
            )
            
        return None

def create_communication_strategy(player_color: str, role_name: str) -> CommunicationStrategy:
    """Factory function to create appropriate communication strategy"""
    if role_name in ["Impostor", "Shapeshifter", "Phantom"]:
        return ImpostorStrategy(player_color, role_name)
    else:
        return CrewmateStrategy(player_color, role_name)
