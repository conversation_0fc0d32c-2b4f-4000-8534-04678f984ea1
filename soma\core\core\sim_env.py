from typing import List, Dict
import random
from collections import Counter

from config.skeld import get_skeld_rooms, get_skeld_task_templates
from config.settings import DEFAULT_SETTINGS
from core.player import Player
from core.role import CREWMATE, IMPOSTOR
from core.task import Task
from core.room import Room
from core.action import Action
from core.sabotage import <PERSON>bot<PERSON><PERSON>ana<PERSON>, SabotageType
from core.communication import CommunicationManager, create_communication_strategy
from core.llm_communication import OllamaClient, create_llm_communication_strategy

class SimulatedGame:
    def __init__(self, players: List[Player] = None, settings: Dict = DEFAULT_SETTINGS):
        self.settings = settings
        self.rooms: Dict[str, Room] = get_skeld_rooms()
        self.task_templates = get_skeld_task_templates()
        self.tick_count = 0

        self.players: List[Player] = []
        self.crewmates: List[Player] = []
        self.impostors: List[Player] = []

        if not players:
            self._init_players()
        else:
            self.players = players
            for p in players:
                if p.role.name in ["<PERSON>mpostor", "Shapeshifter", "Phantom"]:
                    p.fake_tasks = random.sample(self.task_templates, 5)
                    self.impostors.append(p)
                else:
                    self._assign_tasks(p)
                    self.crewmates.append(p)

        self.living_players: List[Player] = self.players.copy()
        self.dead_players: List[Player] = []
        self.last_meeting_deaths: List[Player] = []
        self.last_meeting_caller: str = None

        # Enhanced game systems
        self.sabotage_manager = SabotageManager()
        self.communication_manager = CommunicationManager()
        self.game_over = False
        self.winner = None

        # Initialize LLM client if enabled
        self.ollama_client = None
        if self.settings.get("enable_llm_communication", False):
            self.ollama_client = OllamaClient(
                base_url=self.settings.get("ollama_base_url", "http://localhost:11434"),
                model=self.settings.get("ollama_model", "llama3.2")
            )

        # Initialize communication strategies for players
        for player in self.players:
            if self.ollama_client and self.settings.get("enable_llm_communication", False):
                player.communication_strategy = create_llm_communication_strategy(
                    player.color, player.role.name, self.ollama_client
                )
            else:
                player.communication_strategy = create_communication_strategy(
                    player.color, player.role.name
                )

        # Task progress tracking (calculate after task assignment)
        self.total_tasks = 0
        self.completed_tasks = 0
        self._calculate_total_tasks()

    def _init_players(self):
        num_players = self.settings["num_players"]
        num_impostors = self.settings["num_impostors"]
        colors = [
            "Red", "Blue", "Green", "Pink", "Orange", "Yellow", "Black", "White", "Purple", "Brown", "Cyan", "Lime", "Maroon", "Rose", "Banana", "Gray", "Tan", "Coral"
        ]

        chosen_colors = random.sample(colors, num_players)
        impostor_colors = set(random.sample(chosen_colors, num_impostors))
        cafeteria = self.rooms["Cafeteria"]

        for color in chosen_colors:
            role = IMPOSTOR if color in impostor_colors else CREWMATE
            player = Player(color=color, role=role, starting_room=cafeteria)

            if role == IMPOSTOR:
                player.fake_tasks = random.sample(self.task_templates, 5)
                self.impostors.append(player)
            else:
                self._assign_tasks(player)
                self.crewmates.append(player)

            self.players.append(player)

    def _assign_tasks(self, player: Player):
        commons = [t for t in self.task_templates if t.category == "common"]
        shorts = [t for t in self.task_templates if t.category == "short"]
        longs = [t for t in self.task_templates if t.category == "long"]

        chosen_common = random.sample(commons, self.settings["common_tasks"])
        chosen_short = random.sample(shorts, self.settings["short_tasks"])
        chosen_long = random.sample(longs, self.settings["long_tasks"])

        player.tasks = [t.create_instance() for t in chosen_common + chosen_short + chosen_long]

    def _calculate_total_tasks(self):
        """Calculate total number of tasks for victory condition"""
        self.total_tasks = sum(len(p.tasks) for p in self.crewmates if p.tasks)

    def show_players(self):
        print("\n--- Player Overview ---")
        for p in self.players:
            role = "Impostor" if p.role.name == "Impostor" else "Crewmate"
            print(f"{p.color:>7} | {role:<9} | In {p.current_room.name}")

    def reported_bodies(self):
        return [p for p in self.dead_players if getattr(p.current_room, "reported_body", False)]

    def _get_admin_data(self):
        """Get admin table data (player counts per room)"""
        room_counts = {}
        for room_name in self.rooms.keys():
            count = len([p for p in self.living_players if p.current_room.name == room_name])
            room_counts[room_name] = count
        return room_counts

    def _get_vitals_data(self):
        """Get vitals data (who's alive/dead)"""
        return {
            p.color: "alive" if p.alive else "dead"
            for p in self.players
        }

    def _get_security_camera_data(self):
        """Get security camera data for specific rooms"""
        camera_data = {}
        security_room = self.rooms.get("Security")
        if security_room and security_room.has_security_cameras:
            for feed_room in security_room.camera_feeds:
                players_in_feed = [
                    p.get_apparent_color() for p in self.living_players
                    if p.current_room.name == feed_room
                ]
                camera_data[feed_room] = players_in_feed
        return camera_data

    def _get_observation(self, player):
        room = player.current_room

        # Get visible players (accounting for shapeshifting and vision)
        visible_players = []
        for p in self.players:
            if (p.current_room == room and p.color != player.color and
                (player.alive and p.alive or not player.alive)):
                visible_players.append(p.get_apparent_color())

        # Check for dead bodies
        dead_bodies = [
            p.color for p in self.players
            if (not p.alive and p.current_room == room and
                p not in self.reported_bodies())
        ]

        # Get available actions based on room and role
        available_rooms = [r.name for r in room.connected_rooms if r.doors_open]
        available_vents = []
        if player.can_vent() or "vent" in player.role.special_abilities:
            available_vents = [r.name for r in room.vents_to]

        # Security system access
        security_info = {}
        if room.has_security_cameras and self.settings["enable_security_systems"]:
            security_info["cameras"] = room.camera_feeds
        if room.has_admin_table and self.settings["enable_security_systems"]:
            security_info["admin"] = self._get_admin_data()
        if room.has_vitals and self.settings["enable_security_systems"]:
            security_info["vitals"] = self._get_vitals_data()

        return {
            "tick": self.tick_count,
            "room": room.name,
            "players_in_room": visible_players,
            "connected_rooms": available_rooms,
            "vents": available_vents,
            "dead_bodies": dead_bodies,
            "task_here": bool(player.get_current_task()),
            "task_progress": player.get_current_task().get_progress(player.color) if player.get_current_task() else 0,

            # Player abilities
            "can_kill": player.can_kill(),
            "can_vent": player.can_vent(),
            "can_sabotage": (player.role.can_sabotage and
                           player.role.name in ["Impostor", "Shapeshifter", "Phantom"] and
                           self.sabotage_manager.can_sabotage()),
            "can_shapeshift": player.can_shapeshift(),
            "can_protect": player.can_protect(),
            "can_emergency": (player.can_call_meeting() and room.has_emergency_button and
                            len(self.sabotage_manager.get_active_sabotages()) == 0),

            # Room features
            "room_features": {
                "emergency_button": room.has_emergency_button,
                "admin_table": room.has_admin_table,
                "security_cameras": room.has_security_cameras,
                "vitals": room.has_vitals,
                "lights_on": room.lights_on,
                "doors_open": room.doors_open,
            },

            # Security information
            "security_info": security_info,

            # Sabotage status
            "active_sabotages": [s.value for s in self.sabotage_manager.get_active_sabotages()],
            "crisis_timers": {
                s.value: self.sabotage_manager.get_crisis_timer(s)
                for s in self.sabotage_manager.get_active_sabotages()
                if self.sabotage_manager.get_crisis_timer(s) is not None
            },

            # Game state
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "living_players": len(self.living_players),
            "living_impostors": len([p for p in self.impostors if p.alive]),
        }

    def _apply_action(self, player, action):
        room = player.current_room
        action_type = action.action_type
        target = action.target

        # Log movement for doorlog
        if action_type == "move":
            room.log_player_movement(player.color, self.tick_count, "exit")

        # Handle ghost actions
        if not player.alive:
            self._handle_ghost_action(player, action)
            return

        # Handle living player actions
        if action_type == "move":
            self._handle_move_action(player, target)
        elif action_type == "task":
            self._handle_task_action(player)
        elif action_type == "kill":
            self._handle_kill_action(player, target)
        elif action_type == "vent":
            self._handle_vent_action(player, target)
        elif action_type == "report":
            self._handle_report_action(player)
        elif action_type == "button":
            self._handle_emergency_button_action(player)
        elif action_type.startswith("sabotage_"):
            self._handle_sabotage_action(player, action_type, target)
        elif action_type.startswith("fix_"):
            self._handle_fix_action(player, action_type)
        elif action_type.startswith("use_"):
            self._handle_security_action(player, action_type)
        elif action_type == "shapeshift":
            self._handle_shapeshift_action(player, target)
        elif action_type == "protect":
            self._handle_protect_action(player, target)

    def _handle_ghost_action(self, player, action):
        """Handle actions for dead players"""
        room = player.current_room
        action_type = action.action_type
        target = action.target

        if action_type == "move" and target in [r.name for r in room.connected_rooms]:
            player.current_room = self.rooms[target]

        elif action_type == "task" and player.role.has_real_tasks:
            task = player.get_current_task()
            if task:
                task.complete(player.color)
                if task.is_complete(player.color):
                    self.completed_tasks += 1
                    print(f"{player.color} (ghost) completed {task.name}")

        elif action_type.startswith("sabotage_") and player.role.can_sabotage:
            self._handle_sabotage_action(player, action_type, target)

        elif action_type == "protect" and player.can_protect():
            self._handle_protect_action(player, target)

    def _handle_move_action(self, player, target):
        """Handle movement action"""
        room = player.current_room
        if target in [r.name for r in room.connected_rooms]:
            target_room = self.rooms[target]
            if target_room.doors_open:  # Check if doors are open
                player.current_room = target_room
                target_room.log_player_movement(player.color, self.tick_count, "enter")

    def _handle_task_action(self, player):
        """Handle task completion action"""
        if not player.role.has_real_tasks:
            return

        task = player.get_current_task()
        if task:
            old_progress = task.get_progress(player.color)
            task.complete(player.color)

            if task.is_complete(player.color) and old_progress < 1.0:
                self.completed_tasks += 1
                print(f"{player.color} completed {task.name}")

                # Visual task confirmation
                if task.is_visual:
                    print(f"  ✨ {player.color} performed a visual task!")

    def _handle_kill_action(self, player, target):
        """Handle kill action"""
        if not player.can_kill():
            return

        room = player.current_room
        victims = [
            p for p in self.players
            if (p.get_apparent_color() == target and p.current_room == room and
                p.alive and not p.role.can_sabotage)
        ]

        if victims:
            victim = victims[0]

            # Check for protection
            if victim.protected_player == victim.color:
                print(f"{victim.color} was protected from {player.color}'s attack!")
                return

            victim.alive = False
            self.dead_players.append(victim)
            self.living_players.remove(victim)
            player.kill_cooldown = self.settings["kill_cooldown"]
            print(f"{player.color} killed {victim.color} in {room.name}")

    def _handle_vent_action(self, player, target):
        """Handle vent action"""
        room = player.current_room
        if player.can_vent() and target in [r.name for r in room.vents_to]:
            player.current_room = self.rooms[target]
            print(f"{player.color} vented to {target}")

    def _handle_report_action(self, player):
        """Handle body report action"""
        room = player.current_room
        dead_bodies = [
            p for p in self.dead_players
            if p.current_room == room and p not in self.reported_bodies()
        ]
        if dead_bodies:
            room.reported_body = True
            self.last_meeting_caller = player.color
            print(f"{player.color} reported a body in {room.name}")

    def _handle_emergency_button_action(self, player):
        """Handle emergency meeting button action"""
        room = player.current_room

        # Check if emergency button can be used (no active sabotages)
        if not room.has_emergency_button:
            return
        if not player.can_call_meeting():
            return
        if len(self.sabotage_manager.get_active_sabotages()) > 0:
            print(f"{player.color} tried to call emergency meeting but sabotages are active!")
            return

        room.reported_body = True
        self.last_meeting_caller = player.color
        player.emergency_used = True
        print(f"{player.color} called an emergency meeting")

    def _handle_sabotage_action(self, player, action_type, target):
        """Handle sabotage actions"""
        # CRITICAL: Only impostors can sabotage
        if not player.role.can_sabotage or not self.settings["enable_sabotages"]:
            print(f"❌ {player.color} ({player.role.name}) tried to sabotage but cannot!")
            return

        # Double-check role (safety measure)
        if player.role.name not in ["Impostor", "Shapeshifter", "Phantom"]:
            print(f"❌ CRITICAL BUG: {player.color} ({player.role.name}) attempted sabotage!")
            return

        sabotage_map = {
            "sabotage_lights": SabotageType.LIGHTS,
            "sabotage_oxygen": SabotageType.OXYGEN,
            "sabotage_reactor": SabotageType.REACTOR,
            "sabotage_comms": SabotageType.COMMUNICATIONS,
            "sabotage_doors": SabotageType.DOORS,
        }

        sabotage_type = sabotage_map.get(action_type)
        if sabotage_type:
            kwargs = {}
            if sabotage_type == SabotageType.DOORS and target:
                kwargs['affected_rooms'] = [target]

            if self.sabotage_manager.activate_sabotage(sabotage_type, **kwargs):
                print(f"{player.color} sabotaged {sabotage_type.value}")
                self._apply_sabotage_effects(sabotage_type)

    def _handle_fix_action(self, player, action_type):
        """Handle sabotage fix actions"""
        room = player.current_room
        fix_map = {
            "fix_lights": (SabotageType.LIGHTS, room.can_fix_lights),
            "fix_oxygen": (SabotageType.OXYGEN, room.can_fix_oxygen),
            "fix_reactor": (SabotageType.REACTOR, room.can_fix_reactor),
            "fix_comms": (SabotageType.COMMUNICATIONS, room.can_fix_comms),
        }

        sabotage_type, can_fix = fix_map.get(action_type, (None, False))
        if sabotage_type and can_fix:
            # Use new fix system that handles multi-person requirements
            fixed = self.sabotage_manager.fix_sabotage(sabotage_type, player.color, room.name)
            if fixed:
                print(f"✅ {player.color} fixed {sabotage_type.value} in {room.name}")
                self._remove_sabotage_effects(sabotage_type)
            else:
                # Check if it's a multi-person fix in progress
                if sabotage_type in self.sabotage_manager.active_sabotages:
                    sabotage = self.sabotage_manager.active_sabotages[sabotage_type]
                    if sabotage.requires_multiple_fixers:
                        current_fixers = len(sabotage.current_fixers)
                        if sabotage_type == SabotageType.REACTOR:
                            print(f"🔧 {player.color} is fixing reactor ({current_fixers}/2 people needed)")
                        elif sabotage_type == SabotageType.OXYGEN:
                            o2_fixers = len(sabotage.fix_progress.get("O2", set()))
                            admin_fixers = len(sabotage.fix_progress.get("Admin", set()))
                            print(f"🔧 {player.color} is fixing oxygen (O2: {o2_fixers}/1, Admin: {admin_fixers}/1)")
                    else:
                        print(f"🔧 {player.color} started fixing {sabotage_type.value}")
                else:
                    print(f"❌ {player.color} cannot fix {sabotage_type.value} from {room.name}")

    def _handle_security_action(self, player, action_type):
        """Handle security system usage"""
        if not self.settings["enable_security_systems"]:
            return

        room = player.current_room
        if action_type == "use_security" and room.has_security_cameras:
            camera_data = self._get_security_camera_data()
            print(f"{player.color} checked security cameras: {camera_data}")
        elif action_type == "use_admin" and room.has_admin_table:
            admin_data = self._get_admin_data()
            print(f"{player.color} checked admin table: {admin_data}")
        elif action_type == "use_vitals" and room.has_vitals:
            vitals_data = self._get_vitals_data()
            print(f"{player.color} checked vitals: {vitals_data}")

    def _handle_shapeshift_action(self, player, target):
        """Handle shapeshifter ability"""
        if player.can_shapeshift() and target:
            player.shapeshift_into(target, self.settings["shapeshifter_duration"])
            print(f"{player.color} shapeshifted into {target}")

    def _handle_protect_action(self, player, target):
        """Handle guardian angel protection"""
        if player.can_protect() and target:
            player.protected_player = target
            player.protect_cooldown = self.settings["guardian_protect_cooldown"]
            print(f"{player.color} (ghost) is protecting {target}")

    def _apply_sabotage_effects(self, sabotage_type):
        """Apply visual/mechanical effects of sabotages"""
        if sabotage_type == SabotageType.LIGHTS:
            for room in self.rooms.values():
                room.turn_off_lights()
        elif sabotage_type == SabotageType.DOORS:
            # Close doors in affected rooms
            pass  # Implementation depends on specific door mechanics

    def _remove_sabotage_effects(self, sabotage_type):
        """Remove visual/mechanical effects of sabotages"""
        if sabotage_type == SabotageType.LIGHTS:
            for room in self.rooms.values():
                room.turn_on_lights()

    def _resolve_meetings(self):
        meeting_triggered = any(getattr(room, "reported_body", False) for room in self.rooms.values())
        if not meeting_triggered:
            return

        new_deaths = [p for p in self.dead_players if p not in self.last_meeting_deaths]

        print("\n📣 MEETING CALLED!")
        if self.last_meeting_caller:
            print(f"Meeting was called by: {self.last_meeting_caller}")
        if new_deaths:
            print("Players who died this round:", ", ".join(p.color for p in new_deaths))
        else:
            print("No new deaths this round.")

        # DISCUSSION PHASE
        self._run_discussion_phase()

        # VOTING PHASE
        votes = self._run_voting_phase()
        tally = Counter(votes)

        print("\n🗳️ Voting Results:")
        for option, cnt in tally.items():
            print(f"{option}: {cnt} votes")

        # Check for ties - if the top vote count is shared by multiple options, it's a tie
        most_common = tally.most_common()
        if len(most_common) == 0:
            print("\nNo votes cast. Nobody was ejected.")
            return

        top_vote_count = most_common[0][1]
        top_voted = [option for option, count in most_common if count == top_vote_count]

        # If there's a tie (multiple options with the same highest vote count), nobody gets ejected
        if len(top_voted) > 1:
            print(f"\nTie between {', '.join(top_voted)} with {top_vote_count} votes each. Nobody was ejected.")
        else:
            vote_target = top_voted[0]

            if vote_target != "Skip":
                voted_out = next((p for p in self.living_players if p.color == vote_target), None)
                if voted_out:
                    voted_out.alive = False
                    self.living_players.remove(voted_out)
                    self.dead_players.append(voted_out)
                    reveal = "(Impostor)" if voted_out.role.name in ["Impostor", "Shapeshifter", "Phantom"] else "(Crewmate)"
                    print(f"\n☠️ {voted_out.color} was ejected! {reveal if self.settings.get('confirm_ejects', True) else ''}")
            else:
                print("\nMajority voted to skip. Nobody was ejected.")

        # Reset reported bodies
        for room in self.rooms.values():
            if hasattr(room, "reported_body"):
                room.reported_body = False

        # Return all players to Cafeteria
        cafeteria = self.rooms.get("Cafeteria")
        if cafeteria:
            for player in self.players:
                if player.alive:
                    player.current_room = cafeteria

        self.last_meeting_deaths = self.dead_players.copy()
        self.last_meeting_caller = None

    def _run_discussion_phase(self):
        """Run the discussion phase of a meeting"""
        print("\n💬 DISCUSSION PHASE")
        print("-" * 30)

        self.communication_manager.start_discussion()

        # Generate game state for communication strategies
        game_state = self._get_communication_game_state()

        # Each living player gets to speak
        for player in self.living_players:
            if hasattr(player, 'communication_strategy'):
                # Generate initial messages
                discussion_context = {
                    'accusations': self.communication_manager.get_accusations(),
                    'alibis': self.communication_manager.get_alibis(),
                }

                messages = player.communication_strategy.generate_messages(
                    game_state, discussion_context
                )

                for message in messages:
                    self.communication_manager.add_message(message)
                    print(f"  {message}")

        # Response round - players can respond to what others said
        print("\n💭 RESPONSES:")
        all_messages = self.communication_manager.get_messages()

        for player in self.living_players:
            if hasattr(player, 'communication_strategy'):
                for message in all_messages:
                    if message.sender != player.color:  # Don't respond to own messages
                        response = player.communication_strategy.respond_to_message(
                            message, game_state
                        )
                        if response:
                            self.communication_manager.add_message(response)
                            print(f"  {response}")
                            break  # Only one response per player per round

        self.communication_manager.end_discussion()
        print("-" * 30)

    def _run_voting_phase(self) -> List[str]:
        """Run the voting phase and return votes"""
        print("\n🗳️ VOTING PHASE")

        # For now, use simple random voting influenced by discussion
        votes = []
        accusations = self.communication_manager.get_accusations()

        for player in self.living_players:
            # Weight votes based on accusations made during discussion
            vote_options = [p.color for p in self.living_players] + ["Skip"]
            weights = [1.0] * len(vote_options)  # Default equal weights

            # Increase weight for accused players
            for i, option in enumerate(vote_options[:-1]):  # Exclude "Skip"
                accused_count = len([acc for acc in accusations if acc.target == option])
                if accused_count > 0:
                    weights[i] *= (1.0 + accused_count * 0.5)  # Increase weight based on accusations

            # Weighted random choice
            vote = random.choices(vote_options, weights=weights)[0]
            votes.append(vote)

        return votes

    def _get_communication_game_state(self) -> Dict:
        """Get game state information for communication strategies"""
        # This would be enhanced with actual game observations
        # For now, return basic state
        return {
            'living_players': [p.color for p in self.living_players],
            'dead_players': [p.color for p in self.dead_players],
            'impostor_team': [p.color for p in self.impostors],  # Only impostors know this
            'last_location': 'Cafeteria',  # Simplified
            'last_task': 'wires',  # Simplified
            'suspicion_levels': {},  # Would be populated from player observations
            'saw_kill': None,  # Would be set if player witnessed a kill
            'was_doing_task': None,  # Would be set if player was doing a task
            'suspicious_behavior': {},  # Would be populated from observations
            'under_suspicion': False,  # Would be determined from previous discussions
        }

    def _update_cooldowns(self):
        """Update all player cooldowns and sabotage timers"""
        for player in self.players:
            player.update_cooldowns()

        # Update sabotage timers and check for crisis
        crisis_expired, crisis_sabotage = self.sabotage_manager.tick()
        if crisis_expired:
            crisis_name = crisis_sabotage.value.upper() if crisis_sabotage else "UNKNOWN"
            print(f"\n💀 CRISIS TIMER EXPIRED! {crisis_name} was not fixed in time!")
            print("🔴 IMPOSTORS WIN by sabotage!")
            self.game_over = True
            self.winner = "Impostors"

    def _check_crewmate_victory(self) -> bool:
        """Check if crewmates have won"""
        # Victory condition 1: All tasks completed
        if self.total_tasks > 0:
            all_tasks_done = self.completed_tasks >= self.total_tasks
        else:
            all_tasks_done = False

        # Victory condition 2: All impostors eliminated
        all_impostors_dead = all(not p.alive for p in self.impostors)

        return all_tasks_done or all_impostors_dead

    def _check_impostor_victory(self) -> bool:
        """Check if impostors have won"""
        alive_crewmates = [p for p in self.crewmates if p.alive]
        alive_impostors = [p for p in self.impostors if p.alive]

        # Impostors win if they equal or outnumber crewmates
        return len(alive_impostors) >= len(alive_crewmates) and len(alive_impostors) > 0
    
    def print_debug_state(self):
        print("\n[DEBUG] Alive Impostors:", [p.color for p in self.impostors if p.alive])
        print("[DEBUG] All impostors dead?", all(not p.alive for p in self.impostors))
        print("[DEBUG] Impostors list:", [p.color for p in self.impostors])
        print("[DEBUG] Roles:")
        for p in self.players:
            print(f"  {p.color}: {p.role.name}, Alive: {p.alive}")

    def run_tick(self):
        """Run one game tick"""
        if self.game_over:
            return

        self.tick_count += 1
        actions = {}

        # Collect actions from all players
        for player in self.players:
            observation = self._get_observation(player)
            player.agent.observe(observation)

            # Living players and crewmate ghosts can act
            if player.alive or player.role.has_real_tasks or player.role.can_sabotage:
                action = player.agent.choose_action(observation)
                actions[player.color] = action

        # Apply all actions
        for player in self.players:
            if player.color in actions:
                self._apply_action(player, actions[player.color])

        # Resolve meetings
        self._resolve_meetings()

        # Update cooldowns and timers
        self._update_cooldowns()

        # Check victory conditions
        if not self.game_over:
            if self._check_crewmate_victory():
                print("\n✅ Crewmate Victory! All tasks complete or all impostors eliminated.")
                print(f"Tasks completed: {self.completed_tasks}/{self.total_tasks}")
                self.game_over = True
                self.winner = "Crewmates"
                self.show_players()

            elif self._check_impostor_victory():
                print("\n❌ Impostor Victory! Impostors have outnumbered or matched living crewmates.")
                self.game_over = True
                self.winner = "Impostors"
                self.show_players()

    def is_game_over(self) -> bool:
        """Check if the game is over"""
        return self.game_over

    def get_winner(self) -> str:
        """Get the winning team"""
        return self.winner
