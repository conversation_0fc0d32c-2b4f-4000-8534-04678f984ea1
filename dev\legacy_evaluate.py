#!/usr/bin/env python3
"""
Evaluate trained RL agents in Among Us simulation.
"""

import torch
import argparse
import os
from typing import List

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.rl_agent import RLAgent
from agents.scripted_agent import ScriptedAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

def create_evaluation_game(model_paths: List[str], rl_agent_colors: List[str], 
                          enable_llm: bool = False) -> SimulatedGame:
    """Create a game with trained RL agents"""
    
    # Game settings
    settings = DEFAULT_SETTINGS.copy()
    settings["enable_llm_communication"] = enable_llm
    settings["enable_sabotages"] = True
    settings["enable_security_systems"] = True
    
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    players = []
    
    # Game parameters
    num_players = 6
    num_impostors = 2
    colors = ["Red", "Blue", "<PERSON>", "Yellow", "<PERSON>", "<PERSON>an"][:num_players]
    
    import random
    impostor_colors = random.sample(colors, num_impostors)
    
    # Create players
    model_idx = 0
    for color in colors:
        role = IMPOSTOR if color in impostor_colors else CREWMATE
        
        if color in rl_agent_colors and model_idx < len(model_paths):
            # Create RL agent and load model
            agent = RLAgent(
                player_color=color,
                role_name=role.name,
                device="cuda" if torch.cuda.is_available() else "cpu"
            )
            
            # Load trained model
            if os.path.exists(model_paths[model_idx]):
                agent.load_model(model_paths[model_idx])
                agent.set_training(False)  # Evaluation mode
                print(f"✅ Loaded model for {color}: {model_paths[model_idx]}")
            else:
                print(f"⚠️  Model not found for {color}: {model_paths[model_idx]}")
            
            model_idx += 1
        else:
            # Create scripted agent
            strategies = {
                "Impostor": ["aggressive_impostor", "sneaky_impostor"],
                "Crewmate": ["detective_crewmate", "task_focused_crewmate", "balanced"]
            }
            strategy = random.choice(strategies[role.name])
            agent = ScriptedAgent(color, strategy=strategy)
        
        player = Player(color, role, cafeteria, agent)
        players.append(player)
    
    return SimulatedGame(players, settings)

def evaluate_agents(model_paths: List[str], rl_agent_colors: List[str], 
                   num_games: int = 10, enable_llm: bool = False, verbose: bool = True):
    """Evaluate trained RL agents"""
    
    print(f"🎯 Evaluating RL Agents")
    print(f"   Models: {model_paths}")
    print(f"   RL Agents: {rl_agent_colors}")
    print(f"   Games: {num_games}")
    print(f"   LLM Communication: {enable_llm}")
    print("=" * 50)
    
    results = {
        "total_games": num_games,
        "rl_wins": 0,
        "crewmate_wins": 0,
        "impostor_wins": 0,
        "game_lengths": [],
        "rl_roles": []
    }
    
    for game_num in range(num_games):
        if verbose:
            print(f"\n🎮 Game {game_num + 1}/{num_games}")
        
        # Create game
        game = create_evaluation_game(model_paths, rl_agent_colors, enable_llm)
        
        # Track RL agent roles
        rl_roles = []
        for player in game.players:
            if player.color in rl_agent_colors:
                rl_roles.append(player.role.name)
        results["rl_roles"].append(rl_roles)
        
        # Run game
        step_count = 0
        max_steps = 100
        
        while not game.is_game_over() and step_count < max_steps:
            if verbose and step_count % 20 == 0:
                print(f"   Tick {step_count}: {len(game.living_players)} players alive")
            
            game.run_tick()
            step_count += 1
        
        # Record results
        winner = game.get_winner() if game.is_game_over() else "None"
        results["game_lengths"].append(step_count)
        
        if winner == "Crewmates":
            results["crewmate_wins"] += 1
            # Check if any RL agents were crewmates
            if any(role == "Crewmate" for role in rl_roles):
                results["rl_wins"] += 1
        elif winner == "Impostors":
            results["impostor_wins"] += 1
            # Check if any RL agents were impostors
            if any(role in ["Impostor", "Shapeshifter", "Phantom"] for role in rl_roles):
                results["rl_wins"] += 1
        
        if verbose:
            print(f"   Winner: {winner}")
            print(f"   Length: {step_count} ticks")
            print(f"   RL Roles: {rl_roles}")
    
    # Calculate statistics
    results["rl_win_rate"] = results["rl_wins"] / results["total_games"]
    results["crewmate_win_rate"] = results["crewmate_wins"] / results["total_games"]
    results["impostor_win_rate"] = results["impostor_wins"] / results["total_games"]
    results["avg_game_length"] = sum(results["game_lengths"]) / len(results["game_lengths"])
    
    return results

def print_results(results: dict):
    """Print evaluation results"""
    print("\n" + "=" * 50)
    print("📊 EVALUATION RESULTS")
    print("=" * 50)
    
    print(f"Total Games: {results['total_games']}")
    print(f"Average Game Length: {results['avg_game_length']:.1f} ticks")
    print()
    
    print("🏆 Win Rates:")
    print(f"   RL Agents: {results['rl_win_rate']:.3f} ({results['rl_wins']}/{results['total_games']})")
    print(f"   Crewmates: {results['crewmate_win_rate']:.3f} ({results['crewmate_wins']}/{results['total_games']})")
    print(f"   Impostors: {results['impostor_win_rate']:.3f} ({results['impostor_wins']}/{results['total_games']})")
    print()
    
    # Role distribution
    impostor_games = sum(1 for roles in results['rl_roles'] 
                        if any(role in ["Impostor", "Shapeshifter", "Phantom"] for role in roles))
    crewmate_games = results['total_games'] - impostor_games
    
    print("🎭 RL Agent Role Distribution:")
    print(f"   As Impostors: {impostor_games} games")
    print(f"   As Crewmates: {crewmate_games} games")

def main():
    parser = argparse.ArgumentParser(description="Evaluate Among Us RL agents")
    parser.add_argument("--models", nargs="+", required=True, help="Paths to trained model files")
    parser.add_argument("--agents", nargs="+", default=["Red", "Blue"], help="RL agent colors")
    parser.add_argument("--games", type=int, default=10, help="Number of evaluation games")
    parser.add_argument("--llm", action="store_true", help="Enable LLM communication")
    parser.add_argument("--quiet", action="store_true", help="Reduce output verbosity")
    
    args = parser.parse_args()
    
    # Check if models exist
    for model_path in args.models:
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            return
    
    # Run evaluation
    results = evaluate_agents(
        model_paths=args.models,
        rl_agent_colors=args.agents,
        num_games=args.games,
        enable_llm=args.llm,
        verbose=not args.quiet
    )
    
    # Print results
    print_results(results)

if __name__ == "__main__":
    main()
