"""
Specialized trainers for different aspects of Among Us gameplay.

These trainers focus on specific skills needed for real game play:
- Movement and pathfinding
- Task completion patterns
- UI interaction
- Social mechanics
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .input_controller import InputController
from .screen_capture import ScreenCapture

logger = logging.getLogger(__name__)


class MovementTrainer:
    """Trains movement and pathfinding skills."""
    
    def __init__(self, input_controller: InputController):
        self.input_controller = input_controller
        self.movement_history = []
        self.pathfinding_data = {}
        
        # Movement parameters
        self.movement_speed = 1.0
        self.click_precision = 5
        self.path_smoothing = True
        
    def test_movement(self, dx: int, dy: int) -> bool:
        """Test a specific movement pattern."""
        try:
            # Get current mouse position as reference
            current_pos = self.input_controller.get_mouse_position()
            target_pos = (current_pos[0] + dx, current_pos[1] + dy)
            
            # Perform movement
            self.input_controller.click(target_pos)
            
            # Wait for movement to complete
            time.sleep(1.5)
            
            # For now, assume success (would need visual verification)
            self.movement_history.append({
                'start': current_pos,
                'target': target_pos,
                'offset': (dx, dy),
                'timestamp': time.time(),
                'success': True
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error testing movement: {e}")
            return False
    
    def learn_pathfinding(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Learn optimal path between two points."""
        try:
            # Simple pathfinding - direct line with obstacle avoidance
            path = []
            
            # Calculate direct path
            steps = 10
            for i in range(steps + 1):
                t = i / steps
                x = int(start[0] + t * (end[0] - start[0]))
                y = int(start[1] + t * (end[1] - start[1]))
                path.append((x, y))
            
            return path
            
        except Exception as e:
            logger.error(f"Error learning pathfinding: {e}")
            return [start, end]
    
    def execute_path(self, path: List[Tuple[int, int]]) -> bool:
        """Execute a learned path."""
        try:
            for point in path:
                self.input_controller.click(point)
                time.sleep(0.5)  # Wait between waypoints
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing path: {e}")
            return False


class TaskTrainer:
    """Trains task completion patterns."""
    
    def __init__(self, input_controller: InputController):
        self.input_controller = input_controller
        self.task_patterns = {}
        self.task_success_rates = {}
        
        # Known task types and their interaction patterns
        self.task_templates = {
            'wires': {
                'type': 'drag_connect',
                'pattern': 'left_to_right',
                'precision': 'high'
            },
            'card_swipe': {
                'type': 'swipe',
                'pattern': 'smooth_right',
                'precision': 'medium'
            },
            'download': {
                'type': 'wait',
                'pattern': 'click_and_wait',
                'precision': 'low'
            },
            'fuel': {
                'type': 'hold_and_drag',
                'pattern': 'vertical_fill',
                'precision': 'medium'
            }
        }
    
    def learn_task_pattern(self, task_type: str, ui_elements: List[Tuple[int, int]]) -> bool:
        """Learn how to complete a specific task type."""
        try:
            if task_type not in self.task_templates:
                logger.warning(f"Unknown task type: {task_type}")
                return False
            
            template = self.task_templates[task_type]
            
            # Learn based on task type
            if template['type'] == 'drag_connect':
                return self._learn_wire_task(ui_elements)
            elif template['type'] == 'swipe':
                return self._learn_swipe_task(ui_elements)
            elif template['type'] == 'wait':
                return self._learn_wait_task(ui_elements)
            elif template['type'] == 'hold_and_drag':
                return self._learn_drag_task(ui_elements)
            
            return False
            
        except Exception as e:
            logger.error(f"Error learning task pattern: {e}")
            return False
    
    def _learn_wire_task(self, ui_elements: List[Tuple[int, int]]) -> bool:
        """Learn wire connection task."""
        try:
            # Wires task: connect left wires to right wires by color
            if len(ui_elements) < 6:  # Need at least 3 pairs
                return False
            
            # Sort elements by x-coordinate (left vs right)
            left_wires = sorted([elem for elem in ui_elements if elem[0] < 400], key=lambda x: x[1])
            right_wires = sorted([elem for elem in ui_elements if elem[0] >= 400], key=lambda x: x[1])
            
            # Connect wires (simplified - would need color matching)
            for i, (left_wire, right_wire) in enumerate(zip(left_wires, right_wires)):
                # Drag from left to right
                self.input_controller.click(left_wire)
                time.sleep(0.1)
                self.input_controller.click(right_wire)
                time.sleep(0.5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning wire task: {e}")
            return False
    
    def _learn_swipe_task(self, ui_elements: List[Tuple[int, int]]) -> bool:
        """Learn card swipe task."""
        try:
            if not ui_elements:
                return False
            
            # Card swipe: smooth horizontal movement
            start_pos = ui_elements[0]
            end_pos = (start_pos[0] + 200, start_pos[1])  # Swipe right
            
            # Perform smooth swipe
            self.input_controller.click(start_pos)
            time.sleep(0.1)
            
            # Simulate drag (would need proper drag implementation)
            steps = 20
            for i in range(steps):
                t = i / steps
                x = int(start_pos[0] + t * (end_pos[0] - start_pos[0]))
                y = start_pos[1]
                self.input_controller.move_to((x, y), duration=0.05)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning swipe task: {e}")
            return False
    
    def _learn_wait_task(self, ui_elements: List[Tuple[int, int]]) -> bool:
        """Learn download/upload task."""
        try:
            if not ui_elements:
                return False
            
            # Click to start download and wait
            self.input_controller.click(ui_elements[0])
            
            # Wait for completion (would need progress bar detection)
            time.sleep(3.0)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning wait task: {e}")
            return False
    
    def _learn_drag_task(self, ui_elements: List[Tuple[int, int]]) -> bool:
        """Learn fuel/garbage task."""
        try:
            if len(ui_elements) < 2:
                return False
            
            # Drag from source to destination
            source = ui_elements[0]
            destination = ui_elements[1]
            
            # Hold and drag
            self.input_controller.click(source)
            time.sleep(0.2)
            
            # Drag to destination
            self.input_controller.move_to(destination, duration=1.0)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning drag task: {e}")
            return False


class UITrainer:
    """Trains UI interaction skills."""
    
    def __init__(self, input_controller: InputController):
        self.input_controller = input_controller
        self.ui_map = {}
        self.interaction_patterns = {}
        
        # Common UI elements and their interaction patterns
        self.ui_elements = {
            'use_button': {'action': 'click', 'precision': 'medium'},
            'emergency_button': {'action': 'click', 'precision': 'high'},
            'vote_button': {'action': 'click', 'precision': 'high'},
            'chat_input': {'action': 'type', 'precision': 'high'},
            'settings_button': {'action': 'click', 'precision': 'low'},
            'map_button': {'action': 'click', 'precision': 'low'}
        }
    
    def learn_ui_element(self, element_type: str, position: Tuple[int, int]) -> bool:
        """Learn how to interact with a UI element."""
        try:
            if element_type not in self.ui_elements:
                logger.warning(f"Unknown UI element: {element_type}")
                return False
            
            element_info = self.ui_elements[element_type]
            
            # Store element location
            self.ui_map[element_type] = position
            
            # Test interaction
            if element_info['action'] == 'click':
                self.input_controller.click(position)
                time.sleep(0.5)
                return True
            elif element_info['action'] == 'type':
                self.input_controller.click(position)
                time.sleep(0.2)
                self.input_controller.type_text("test")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error learning UI element: {e}")
            return False
    
    def interact_with_element(self, element_type: str, text: Optional[str] = None) -> bool:
        """Interact with a learned UI element."""
        try:
            if element_type not in self.ui_map:
                logger.warning(f"UI element not learned: {element_type}")
                return False
            
            position = self.ui_map[element_type]
            element_info = self.ui_elements[element_type]
            
            if element_info['action'] == 'click':
                self.input_controller.click(position)
            elif element_info['action'] == 'type' and text:
                self.input_controller.click(position)
                time.sleep(0.2)
                self.input_controller.type_text(text)
            
            return True
            
        except Exception as e:
            logger.error(f"Error interacting with UI element: {e}")
            return False


class SocialTrainer:
    """Trains social interaction mechanics."""
    
    def __init__(self, input_controller: InputController):
        self.input_controller = input_controller
        self.chat_patterns = []
        self.voting_patterns = []
        self.meeting_behavior = {}
        
        # Common chat responses for different situations
        self.chat_responses = {
            'suspicious_behavior': ["sus", "I saw them vent", "they were acting weird"],
            'task_completion': ["doing tasks", "I was in electrical", "saw me scan"],
            'defense': ["I was with [player]", "I have visual task", "not me"],
            'voting': ["voting [player]", "skip", "not enough info"]
        }
    
    def learn_chat_interaction(self, situation: str) -> bool:
        """Learn appropriate chat responses for different situations."""
        try:
            if situation not in self.chat_responses:
                return False
            
            # This would analyze chat patterns and learn appropriate responses
            # For now, just store the situation
            self.chat_patterns.append({
                'situation': situation,
                'responses': self.chat_responses[situation],
                'timestamp': time.time()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning chat interaction: {e}")
            return False
    
    def learn_voting_behavior(self, players_present: List[str], vote_target: Optional[str]) -> bool:
        """Learn voting patterns and behavior."""
        try:
            voting_data = {
                'players_present': players_present,
                'vote_target': vote_target,
                'timestamp': time.time()
            }
            
            self.voting_patterns.append(voting_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning voting behavior: {e}")
            return False
    
    def execute_chat_response(self, situation: str) -> bool:
        """Execute appropriate chat response for a situation."""
        try:
            if situation not in self.chat_responses:
                return False
            
            # Select appropriate response
            import random
            response = random.choice(self.chat_responses[situation])
            
            # Type response (would need chat UI detection)
            # For now, just simulate
            logger.info(f"Would send chat: {response}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing chat response: {e}")
            return False


class GameMechanicsTrainer:
    """Trains understanding of game mechanics and rules."""
    
    def __init__(self):
        self.game_rules = {}
        self.role_abilities = {}
        self.map_knowledge = {}
        self.timing_patterns = {}
        
    def learn_role_abilities(self, role: str) -> bool:
        """Learn abilities and restrictions for a specific role."""
        try:
            role_data = {
                'crewmate': {
                    'can_kill': False,
                    'can_vent': False,
                    'can_sabotage': False,
                    'has_tasks': True,
                    'win_condition': 'complete_tasks_or_vote_impostors'
                },
                'impostor': {
                    'can_kill': True,
                    'can_vent': True,
                    'can_sabotage': True,
                    'has_tasks': False,
                    'win_condition': 'kill_or_sabotage'
                }
            }
            
            if role in role_data:
                self.role_abilities[role] = role_data[role]
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error learning role abilities: {e}")
            return False
    
    def learn_map_layout(self, room_connections: Dict[str, List[str]]) -> bool:
        """Learn map layout and room connections."""
        try:
            self.map_knowledge.update(room_connections)
            return True
            
        except Exception as e:
            logger.error(f"Error learning map layout: {e}")
            return False
    
    def learn_timing_patterns(self, action_type: str, duration: float) -> bool:
        """Learn timing patterns for different actions."""
        try:
            if action_type not in self.timing_patterns:
                self.timing_patterns[action_type] = []
            
            self.timing_patterns[action_type].append(duration)
            
            return True
            
        except Exception as e:
            logger.error(f"Error learning timing patterns: {e}")
            return False
