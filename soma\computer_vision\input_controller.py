"""
Input automation system for Among Us computer vision.

Handles mouse and keyboard automation to control the game
based on AI decisions while respecting game timing and safety.
"""

import pyautogui
import pynput
from pynput import mouse, keyboard
import time
import logging
from typing import Tuple, Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum
import threading
import queue

logger = logging.getLogger(__name__)


class InputType(Enum):
    """Types of input actions."""
    CLICK = "click"
    MOVE = "move"
    KEY_PRESS = "key_press"
    KEY_HOLD = "key_hold"
    KEY_RELEASE = "key_release"
    DRAG = "drag"
    SCROLL = "scroll"


@dataclass
class InputAction:
    """Represents an input action to be performed."""
    action_type: InputType
    position: Optional[Tuple[int, int]] = None
    key: Optional[str] = None
    duration: float = 0.1
    delay_before: float = 0.0
    delay_after: float = 0.0
    button: str = 'left'  # For mouse clicks
    distance: int = 0  # For scrolling
    
    
class InputController:
    """Controls mouse and keyboard input for Among Us automation."""
    
    def __init__(self, safety_enabled: bool = True):
        """
        Initialize input controller.
        
        Args:
            safety_enabled: Enable safety features to prevent runaway automation
        """
        self.safety_enabled = safety_enabled
        self.is_active = False
        self.emergency_stop = False
        
        # Safety settings
        self.max_actions_per_second = 10
        self.min_action_delay = 0.05  # Minimum delay between actions
        self.safety_bounds = None  # Game window bounds for safety
        
        # Action queue for threaded execution
        self.action_queue = queue.Queue()
        self.execution_thread = None
        
        # Performance tracking
        self.action_stats = {
            'total_actions': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'avg_execution_time': 0.0
        }
        
        # Configure pyautogui safety
        pyautogui.FAILSAFE = True  # Move mouse to corner to stop
        pyautogui.PAUSE = 0.05  # Small pause between actions
        
        # Emergency stop hotkey setup
        if safety_enabled:
            self._setup_emergency_stop()
    
    def _setup_emergency_stop(self):
        """Set up emergency stop hotkey (Ctrl+Shift+Q)."""
        try:
            def on_hotkey():
                logger.warning("Emergency stop activated!")
                self.emergency_stop = True
                self.stop()
            
            # Set up hotkey listener in separate thread
            hotkey_thread = threading.Thread(
                target=self._hotkey_listener,
                args=(on_hotkey,),
                daemon=True
            )
            hotkey_thread.start()
            
        except Exception as e:
            logger.error(f"Error setting up emergency stop: {e}")
    
    def _hotkey_listener(self, callback):
        """Listen for emergency stop hotkey."""
        try:
            with keyboard.GlobalHotKeys({'<ctrl>+<shift>+q': callback}):
                while not self.emergency_stop:
                    time.sleep(0.1)
        except Exception as e:
            logger.error(f"Error in hotkey listener: {e}")
    
    def set_safety_bounds(self, bounds: Tuple[int, int, int, int]):
        """
        Set safety bounds for input actions.
        
        Args:
            bounds: (left, top, width, height) of allowed area
        """
        self.safety_bounds = bounds
        logger.info(f"Safety bounds set: {bounds}")
    
    def start(self):
        """Start the input controller."""
        if self.is_active:
            logger.warning("Input controller already active")
            return
        
        self.is_active = True
        self.emergency_stop = False
        
        # Start execution thread
        self.execution_thread = threading.Thread(
            target=self._execution_loop,
            daemon=True
        )
        self.execution_thread.start()
        
        logger.info("Input controller started")
    
    def stop(self):
        """Stop the input controller."""
        self.is_active = False
        
        # Clear action queue
        while not self.action_queue.empty():
            try:
                self.action_queue.get_nowait()
            except queue.Empty:
                break
        
        logger.info("Input controller stopped")
    
    def _execution_loop(self):
        """Main execution loop for processing input actions."""
        last_action_time = 0
        
        while self.is_active and not self.emergency_stop:
            try:
                # Get next action with timeout
                try:
                    action = self.action_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # Rate limiting
                current_time = time.time()
                time_since_last = current_time - last_action_time
                
                if time_since_last < self.min_action_delay:
                    time.sleep(self.min_action_delay - time_since_last)
                
                # Execute action
                success = self._execute_action(action)
                
                # Update stats
                self.action_stats['total_actions'] += 1
                if success:
                    self.action_stats['successful_actions'] += 1
                else:
                    self.action_stats['failed_actions'] += 1
                
                last_action_time = time.time()
                
            except Exception as e:
                logger.error(f"Error in execution loop: {e}")
                time.sleep(0.1)
    
    def _execute_action(self, action: InputAction) -> bool:
        """Execute a single input action."""
        try:
            start_time = time.time()
            
            # Pre-action delay
            if action.delay_before > 0:
                time.sleep(action.delay_before)
            
            # Safety check
            if not self._is_action_safe(action):
                logger.warning(f"Unsafe action blocked: {action}")
                return False
            
            # Execute based on action type
            success = False
            
            if action.action_type == InputType.CLICK:
                success = self._perform_click(action)
            elif action.action_type == InputType.MOVE:
                success = self._perform_move(action)
            elif action.action_type == InputType.KEY_PRESS:
                success = self._perform_key_press(action)
            elif action.action_type == InputType.KEY_HOLD:
                success = self._perform_key_hold(action)
            elif action.action_type == InputType.KEY_RELEASE:
                success = self._perform_key_release(action)
            elif action.action_type == InputType.DRAG:
                success = self._perform_drag(action)
            elif action.action_type == InputType.SCROLL:
                success = self._perform_scroll(action)
            
            # Post-action delay
            if action.delay_after > 0:
                time.sleep(action.delay_after)
            
            # Update execution time stats
            execution_time = time.time() - start_time
            total_actions = self.action_stats['total_actions']
            if total_actions > 0:
                self.action_stats['avg_execution_time'] = (
                    (self.action_stats['avg_execution_time'] * (total_actions - 1) + execution_time) / total_actions
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return False
    
    def _is_action_safe(self, action: InputAction) -> bool:
        """Check if an action is safe to execute."""
        if not self.safety_enabled:
            return True
        
        # Check emergency stop
        if self.emergency_stop:
            return False
        
        # Check bounds for position-based actions
        if action.position and self.safety_bounds:
            x, y = action.position
            left, top, width, height = self.safety_bounds
            
            if not (left <= x <= left + width and top <= y <= top + height):
                logger.warning(f"Action position {action.position} outside safety bounds")
                return False
        
        return True
    
    def _perform_click(self, action: InputAction) -> bool:
        """Perform mouse click."""
        try:
            if not action.position:
                return False
            
            x, y = action.position
            pyautogui.click(x, y, button=action.button, duration=action.duration)
            return True
            
        except Exception as e:
            logger.error(f"Error performing click: {e}")
            return False
    
    def _perform_move(self, action: InputAction) -> bool:
        """Perform mouse move."""
        try:
            if not action.position:
                return False
            
            x, y = action.position
            pyautogui.moveTo(x, y, duration=action.duration)
            return True
            
        except Exception as e:
            logger.error(f"Error performing move: {e}")
            return False
    
    def _perform_key_press(self, action: InputAction) -> bool:
        """Perform key press."""
        try:
            if not action.key:
                return False
            
            pyautogui.press(action.key)
            return True
            
        except Exception as e:
            logger.error(f"Error performing key press: {e}")
            return False
    
    def _perform_key_hold(self, action: InputAction) -> bool:
        """Perform key hold."""
        try:
            if not action.key:
                return False
            
            pyautogui.keyDown(action.key)
            if action.duration > 0:
                time.sleep(action.duration)
                pyautogui.keyUp(action.key)
            
            return True
            
        except Exception as e:
            logger.error(f"Error performing key hold: {e}")
            return False
    
    def _perform_key_release(self, action: InputAction) -> bool:
        """Perform key release."""
        try:
            if not action.key:
                return False
            
            pyautogui.keyUp(action.key)
            return True
            
        except Exception as e:
            logger.error(f"Error performing key release: {e}")
            return False
    
    def _perform_drag(self, action: InputAction) -> bool:
        """Perform mouse drag."""
        try:
            if not action.position:
                return False
            
            # This would need start and end positions
            # For now, just perform a click
            return self._perform_click(action)
            
        except Exception as e:
            logger.error(f"Error performing drag: {e}")
            return False
    
    def _perform_scroll(self, action: InputAction) -> bool:
        """Perform mouse scroll."""
        try:
            if action.position:
                x, y = action.position
                pyautogui.scroll(action.distance, x=x, y=y)
            else:
                pyautogui.scroll(action.distance)
            
            return True
            
        except Exception as e:
            logger.error(f"Error performing scroll: {e}")
            return False

    def queue_action(self, action: InputAction):
        """Queue an action for execution."""
        if not self.is_active:
            logger.warning("Cannot queue action: controller not active")
            return

        try:
            self.action_queue.put(action, timeout=1.0)
        except queue.Full:
            logger.warning("Action queue full, dropping action")

    def click(self, position: Tuple[int, int], button: str = 'left', delay: float = 0.1):
        """Queue a click action."""
        action = InputAction(
            action_type=InputType.CLICK,
            position=position,
            button=button,
            delay_after=delay
        )
        self.queue_action(action)

    def move_to(self, position: Tuple[int, int], duration: float = 0.2):
        """Queue a move action."""
        action = InputAction(
            action_type=InputType.MOVE,
            position=position,
            duration=duration
        )
        self.queue_action(action)

    def press_key(self, key: str, delay: float = 0.1):
        """Queue a key press action."""
        action = InputAction(
            action_type=InputType.KEY_PRESS,
            key=key,
            delay_after=delay
        )
        self.queue_action(action)

    # Among Us specific keyboard controls
    def move_up(self, duration: float = 0.5):
        """Move up using W key."""
        self.hold_key('w', duration)

    def move_down(self, duration: float = 0.5):
        """Move down using S key."""
        self.hold_key('s', duration)

    def move_left(self, duration: float = 0.5):
        """Move left using A key."""
        self.hold_key('a', duration)

    def move_right(self, duration: float = 0.5):
        """Move right using D key."""
        self.hold_key('d', duration)

    def interact(self):
        """Interact using E key."""
        self.press_key('e', delay=0.2)

    def kill(self):
        """Kill using Q key."""
        self.press_key('q', delay=0.2)

    def report(self):
        """Report body using R key."""
        self.press_key('r', delay=0.2)

    def vent(self):
        """Vent using V key."""
        self.press_key('v', delay=0.2)

    def use_ability(self):
        """Use role ability using F key."""
        self.press_key('f', delay=0.2)

    def open_map(self):
        """Open map using Tab key."""
        self.press_key('tab', delay=0.2)

    def emergency_meeting(self):
        """Call emergency meeting using Space key."""
        self.press_key('space', delay=0.2)

    def hold_key(self, key: str, duration: float = 0.5):
        """Queue a key hold action."""
        action = InputAction(
            action_type=InputType.KEY_HOLD,
            key=key,
            duration=duration
        )
        self.queue_action(action)

    def type_text(self, text: str, delay_between_chars: float = 0.05):
        """Queue actions to type text."""
        for char in text:
            action = InputAction(
                action_type=InputType.KEY_PRESS,
                key=char,
                delay_after=delay_between_chars
            )
            self.queue_action(action)

    def emergency_click(self, position: Tuple[int, int]):
        """Perform immediate click (bypasses queue for emergency actions)."""
        try:
            if self._is_action_safe(InputAction(action_type=InputType.CLICK, position=position)):
                pyautogui.click(position[0], position[1])
                return True
        except Exception as e:
            logger.error(f"Error in emergency click: {e}")
        return False

    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        return pyautogui.position()

    def is_queue_empty(self) -> bool:
        """Check if action queue is empty."""
        return self.action_queue.empty()

    def get_queue_size(self) -> int:
        """Get current action queue size."""
        return self.action_queue.qsize()

    def clear_queue(self):
        """Clear all queued actions."""
        while not self.action_queue.empty():
            try:
                self.action_queue.get_nowait()
            except queue.Empty:
                break

    def get_stats(self) -> Dict[str, Any]:
        """Get input controller statistics."""
        stats = self.action_stats.copy()
        stats['is_active'] = self.is_active
        stats['emergency_stop'] = self.emergency_stop
        stats['queue_size'] = self.get_queue_size()

        if stats['total_actions'] > 0:
            stats['success_rate'] = stats['successful_actions'] / stats['total_actions']
        else:
            stats['success_rate'] = 0.0

        return stats

    def reset_stats(self):
        """Reset statistics."""
        self.action_stats = {
            'total_actions': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'avg_execution_time': 0.0
        }

    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
