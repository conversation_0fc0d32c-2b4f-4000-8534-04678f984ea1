from typing import Dict, List, Optional, Tuple
from enum import Enum

class SabotageType(Enum):
    LIGHTS = "lights"
    OXYGEN = "oxygen"
    REACTOR = "reactor"
    COMMUNICATIONS = "communications"
    DOORS = "doors"

class Sabotage:
    def __init__(self, sabotage_type: SabotageType, duration: Optional[int] = None,
                 crisis_timer: Optional[int] = None, affected_rooms: List[str] = None,
                 requires_multiple_fixers: bool = False, fix_locations: List[str] = None):
        self.sabotage_type = sabotage_type
        self.duration = duration  # How long the sabotage lasts (None = until fixed)
        self.crisis_timer = crisis_timer  # Time limit for critical sabotages (oxygen/reactor)
        self.affected_rooms = affected_rooms or []
        self.is_active = True
        self.time_remaining = crisis_timer
        self.requires_multiple_fixers = requires_multiple_fixers  # Needs multiple steps/locations
        self.fix_locations = fix_locations or []  # Locations where this can be fixed
        self.current_fixers = set()  # Players currently fixing this sabotage
        self.fix_progress = {}  # Track fix progress at each location
        self.completed_locations = set()  # Locations that have been completed
        
    def tick(self):
        """Update sabotage state each game tick"""
        if self.time_remaining is not None:
            self.time_remaining -= 1
            if self.time_remaining <= 0:
                return True  # Crisis timer expired - impostors win
        
        if self.duration is not None:
            self.duration -= 1
            if self.duration <= 0:
                self.is_active = False
        
        return False  # No crisis
    
    def add_fixer(self, player_color: str, location: str):
        """Add a player as currently fixing this sabotage"""
        self.current_fixers.add(player_color)
        if location not in self.fix_progress:
            self.fix_progress[location] = set()
        self.fix_progress[location].add(player_color)

    def remove_fixer(self, player_color: str):
        """Remove a player from fixing this sabotage"""
        self.current_fixers.discard(player_color)
        for location_fixers in self.fix_progress.values():
            location_fixers.discard(player_color)

    def check_if_fixed(self) -> bool:
        """Check if sabotage is fixed based on CORRECT Among Us mechanics"""
        if not self.requires_multiple_fixers:
            # Single person fixes: Lights, Communications
            return len(self.current_fixers) > 0
        else:
            # Multi-step fixes based on actual game mechanics
            if self.sabotage_type == SabotageType.OXYGEN:
                # Oxygen: Requires PIN codes entered at BOTH O2 AND Admin
                # Can be same person or different people, doesn't need to be simultaneous
                return "O2" in self.completed_locations and "Admin" in self.completed_locations
            elif self.sabotage_type == SabotageType.REACTOR:
                # Reactor: Requires 2 people at handprint scanners SIMULTANEOUSLY
                reactor_fixers = len(self.fix_progress.get("Reactor", set()))
                return reactor_fixers >= 2
        return False

    def fix(self):
        """Fix the sabotage"""
        self.is_active = False
        self.time_remaining = None
        self.current_fixers.clear()
        self.fix_progress.clear()
        self.completed_locations.clear()

class SabotageManager:
    def __init__(self):
        self.active_sabotages: Dict[SabotageType, Sabotage] = {}
        self.sabotage_cooldown = 10  # 10 seconds initial cooldown (per wiki)
        
    def can_sabotage(self) -> bool:
        # Can't sabotage if on cooldown OR if any NON-DOOR sabotage is already active
        # Doors are "soft sabotages" and don't block other sabotages
        if self.sabotage_cooldown > 0:
            return False

        # Check if any non-door sabotages are active
        non_door_sabotages = [s for s in self.active_sabotages.keys()
                             if s != SabotageType.DOORS]
        return len(non_door_sabotages) == 0

    def can_sabotage_doors(self) -> bool:
        """Check if doors can be sabotaged (doors have separate rules)"""
        # According to wiki: On The Skeld, doors and non-door sabotages are mutually exclusive
        # Doors can be activated when no other sabotages are active
        # Multiple doors can be active simultaneously
        if self.sabotage_cooldown > 0:
            return False

        # Check if any non-door sabotages are active
        non_door_sabotages = [s for s in self.active_sabotages.keys()
                             if s != SabotageType.DOORS]
        return len(non_door_sabotages) == 0
    
    def activate_sabotage(self, sabotage_type: SabotageType, **kwargs) -> bool:
        """Activate a sabotage if possible"""

        # Special handling for doors (soft sabotage)
        if sabotage_type == SabotageType.DOORS:
            if not self.can_sabotage_doors():
                return False
            # Doors can coexist with other sabotages and multiple doors can be active
        else:
            # Regular sabotages
            if not self.can_sabotage():
                return False

            # Check if any non-door sabotages are active
            non_door_sabotages = [s for s in self.active_sabotages.keys()
                                 if s != SabotageType.DOORS]
            if len(non_door_sabotages) > 0:
                return False

        # Additional check: Can't have multiple critical sabotages active (redundant but safe)
        if sabotage_type in [SabotageType.OXYGEN, SabotageType.REACTOR]:
            if any(s.sabotage_type in [SabotageType.OXYGEN, SabotageType.REACTOR]
                   for s in self.active_sabotages.values()):
                return False
        
        # Create sabotage based on type with CORRECT Among Us mechanics
        if sabotage_type == SabotageType.LIGHTS:
            # Lights: Single person can fix at Electrical (flip switches)
            sabotage = Sabotage(sabotage_type, duration=None,
                              requires_multiple_fixers=False, fix_locations=["Electrical"])
        elif sabotage_type == SabotageType.OXYGEN:
            # Oxygen: Requires PIN codes at TWO DIFFERENT locations (O2 AND Admin)
            # Same code must be entered at BOTH locations (can be same person)
            sabotage = Sabotage(sabotage_type, crisis_timer=30,
                              requires_multiple_fixers=True, fix_locations=["O2", "Admin"])
        elif sabotage_type == SabotageType.REACTOR:
            # Reactor: Requires 2 people at handprint scanners in SAME location (Reactor)
            sabotage = Sabotage(sabotage_type, crisis_timer=30,
                              requires_multiple_fixers=True, fix_locations=["Reactor"])
        elif sabotage_type == SabotageType.COMMUNICATIONS:
            # Communications: Single person can fix at Communications (adjust wavelengths)
            sabotage = Sabotage(sabotage_type, duration=None,
                              requires_multiple_fixers=False, fix_locations=["Communications"])
        elif sabotage_type == SabotageType.DOORS:
            # Doors: Auto-expire after 10 seconds on The Skeld
            # Multiple doors can be active simultaneously
            affected_rooms = kwargs.get('affected_rooms', [])
            sabotage = Sabotage(sabotage_type, duration=10, affected_rooms=affected_rooms,
                              requires_multiple_fixers=False, fix_locations=[])
        else:
            return False

        # Set cooldown (doors have shorter cooldown)
        if sabotage_type == SabotageType.DOORS:
            self.sabotage_cooldown = 10  # Shorter cooldown for doors
        else:
            self.sabotage_cooldown = 30  # 30 seconds cooldown after regular sabotage

        # For doors, we can have multiple active, but for simplicity we'll track one
        # In a full implementation, we'd track multiple door sabotages separately
        self.active_sabotages[sabotage_type] = sabotage
        return True
    
    def start_fixing_sabotage(self, sabotage_type: SabotageType, player_color: str, room_name: str) -> bool:
        """Start fixing a sabotage (for multi-person fixes)"""
        if sabotage_type not in self.active_sabotages:
            return False

        sabotage = self.active_sabotages[sabotage_type]

        # Check if player is in correct room to fix
        if room_name not in sabotage.fix_locations:
            return False

        # Add this player as a fixer
        sabotage.add_fixer(player_color, room_name)

        # Check if sabotage is now fixed (all requirements met)
        if sabotage.check_if_fixed():
            sabotage.fix()
            del self.active_sabotages[sabotage_type]
            return True

        return False  # Started fixing but not complete yet

    def stop_fixing_sabotage(self, sabotage_type: SabotageType, player_color: str):
        """Stop fixing a sabotage (player moved away or stopped)"""
        if sabotage_type in self.active_sabotages:
            self.active_sabotages[sabotage_type].remove_fixer(player_color)

    def fix_sabotage(self, sabotage_type: SabotageType, player_color: str, room_name: str = None) -> bool:
        """Fix a sabotage if possible"""
        if sabotage_type not in self.active_sabotages:
            return False

        sabotage = self.active_sabotages[sabotage_type]

        # Check if player is in correct room to fix
        if room_name not in sabotage.fix_locations:
            return False

        if sabotage.requires_multiple_fixers:
            if sabotage_type == SabotageType.OXYGEN:
                # O2: Mark this location as completed (PIN code entered)
                sabotage.completed_locations.add(room_name)
                if sabotage.check_if_fixed():
                    sabotage.fix()
                    del self.active_sabotages[sabotage_type]
                    return True
                return False  # Partial progress made
            else:
                # Reactor: Use simultaneous fixing system
                return self.start_fixing_sabotage(sabotage_type, player_color, room_name)
        else:
            # Single person can fix immediately (Lights, Communications)
            sabotage.fix()
            del self.active_sabotages[sabotage_type]
            return True
    
    def tick(self) -> Tuple[bool, Optional[SabotageType]]:
        """Update all sabotages. Returns (crisis_expired, sabotage_type)"""
        if self.sabotage_cooldown > 0:
            self.sabotage_cooldown -= 1

        expired_sabotages = []
        crisis_expired = False
        crisis_sabotage = None

        for sabotage_type, sabotage in self.active_sabotages.items():
            if sabotage.tick():
                crisis_expired = True
                crisis_sabotage = sabotage_type
                print(f"💀 CRISIS TIMER EXPIRED: {sabotage_type.value.upper()} not fixed in time!")

            if not sabotage.is_active:
                expired_sabotages.append(sabotage_type)

        # Remove expired sabotages
        for sabotage_type in expired_sabotages:
            del self.active_sabotages[sabotage_type]

        return crisis_expired, crisis_sabotage
    
    def is_sabotaged(self, sabotage_type: SabotageType) -> bool:
        return sabotage_type in self.active_sabotages
    
    def get_active_sabotages(self) -> List[SabotageType]:
        return list(self.active_sabotages.keys())
    
    def get_crisis_timer(self, sabotage_type: SabotageType) -> Optional[int]:
        if sabotage_type in self.active_sabotages:
            return self.active_sabotages[sabotage_type].time_remaining
        return None
