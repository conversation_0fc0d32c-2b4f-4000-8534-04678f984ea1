#!/usr/bin/env python3
"""
Test script to verify sabotage fixes work correctly.
"""

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from core.action import Action
from core.sabotage import <PERSON><PERSON>age<PERSON>ana<PERSON>, SabotageType
from agents.base_agent import BaseAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

class TestAgent(BaseAgent):
    def __init__(self, player_color: str, test_actions: list = None):
        super().__init__(player_color)
        self.test_actions = test_actions or ["idle"]
        self.action_index = 0
        
    def choose_action(self, observation: dict) -> Action:
        if self.action_index < len(self.test_actions):
            action = self.test_actions[self.action_index]
            self.action_index += 1
            return Action(action)
        return Action("idle")

def test_crewmate_cannot_sabotage():
    """Test that crewmates cannot sabotage"""
    print("🧪 Test 1: Crewmates cannot sabotage")
    
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    # Create crewmate player
    crewmate_agent = TestAgent("Blue", ["sabotage_lights", "sabotage_oxygen"])
    crewmate_player = Player("Blue", CREWMATE, cafeteria, crewmate_agent)
    
    players = [crewmate_player]
    game = SimulatedGame(players, settings)
    
    # Check observation - crewmate should not be able to sabotage
    obs = game._get_observation(crewmate_player)
    can_sabotage = obs.get("can_sabotage", False)
    
    print(f"   Crewmate can_sabotage: {can_sabotage}")
    assert not can_sabotage, "❌ Crewmate should not be able to sabotage!"
    
    # Try to run actions - should not actually sabotage
    initial_sabotages = len(game.sabotage_manager.get_active_sabotages())
    game.run_tick()
    final_sabotages = len(game.sabotage_manager.get_active_sabotages())
    
    print(f"   Sabotages before: {initial_sabotages}, after: {final_sabotages}")
    assert initial_sabotages == final_sabotages, "❌ Crewmate should not have been able to sabotage!"
    
    print("   ✅ Test passed: Crewmates cannot sabotage")

def test_impostor_sabotage_cooldown():
    """Test that impostors have sabotage cooldown"""
    print("\n🧪 Test 2: Impostor sabotage cooldown")
    
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    # Create impostor player
    impostor_agent = TestAgent("Red", ["sabotage_lights", "sabotage_oxygen", "sabotage_reactor"])
    impostor_player = Player("Red", IMPOSTOR, cafeteria, impostor_agent)
    
    players = [impostor_player]
    game = SimulatedGame(players, settings)
    
    # First sabotage should work
    obs1 = game._get_observation(impostor_player)
    can_sabotage1 = obs1.get("can_sabotage", False)
    print(f"   Initially can_sabotage: {can_sabotage1}")
    
    game.run_tick()  # Should activate lights sabotage
    active_sabotages = game.sabotage_manager.get_active_sabotages()
    print(f"   Active sabotages after tick 1: {[s.value for s in active_sabotages]}")
    
    # Second sabotage should NOT work (sabotage already active)
    obs2 = game._get_observation(impostor_player)
    can_sabotage2 = obs2.get("can_sabotage", False)
    print(f"   After sabotage can_sabotage: {can_sabotage2}")
    assert not can_sabotage2, "❌ Should not be able to sabotage while another is active!"
    
    # Fix the sabotage
    game.sabotage_manager.fix_sabotage(SabotageType.LIGHTS, "Electrical")
    
    # Should still be on cooldown
    obs3 = game._get_observation(impostor_player)
    can_sabotage3 = obs3.get("can_sabotage", False)
    print(f"   After fix can_sabotage: {can_sabotage3}")
    
    # Wait for cooldown to expire
    for i in range(20):  # Wait 20 ticks
        game._update_cooldowns()
    
    obs4 = game._get_observation(impostor_player)
    can_sabotage4 = obs4.get("can_sabotage", False)
    print(f"   After cooldown can_sabotage: {can_sabotage4}")
    assert can_sabotage4, "❌ Should be able to sabotage after cooldown expires!"
    
    print("   ✅ Test passed: Sabotage cooldown works correctly")

def test_only_one_sabotage_active():
    """Test that only one sabotage can be active at a time"""
    print("\n🧪 Test 3: Only one sabotage active at a time")
    
    sabotage_manager = SabotageManager()
    
    # Activate first sabotage
    success1 = sabotage_manager.activate_sabotage(SabotageType.LIGHTS)
    print(f"   First sabotage (lights) success: {success1}")
    assert success1, "❌ First sabotage should succeed!"
    
    # Try to activate second sabotage - should fail
    success2 = sabotage_manager.activate_sabotage(SabotageType.OXYGEN)
    print(f"   Second sabotage (oxygen) success: {success2}")
    assert not success2, "❌ Second sabotage should fail when one is already active!"
    
    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   Active sabotages: {[s.value for s in active_sabotages]}")
    assert len(active_sabotages) == 1, "❌ Should only have one active sabotage!"
    
    # Fix the sabotage
    sabotage_manager.fix_sabotage(SabotageType.LIGHTS, "Electrical")
    
    # Wait for cooldown
    for i in range(20):
        sabotage_manager.tick()
    
    # Now should be able to sabotage again
    success3 = sabotage_manager.activate_sabotage(SabotageType.OXYGEN)
    print(f"   Third sabotage (oxygen) after fix: {success3}")
    assert success3, "❌ Should be able to sabotage after fixing and cooldown!"
    
    print("   ✅ Test passed: Only one sabotage can be active")

def test_integrated_game():
    """Test integrated game with proper sabotage rules"""
    print("\n🧪 Test 4: Integrated game test")
    
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    # Create mixed players
    impostor_agent = TestAgent("Red", ["sabotage_lights", "idle", "idle", "sabotage_oxygen"])
    crewmate_agent = TestAgent("Blue", ["sabotage_lights", "task", "idle"])  # Should fail to sabotage
    
    impostor_player = Player("Red", IMPOSTOR, cafeteria, impostor_agent)
    crewmate_player = Player("Blue", CREWMATE, cafeteria, crewmate_agent)
    
    players = [impostor_player, crewmate_player]
    game = SimulatedGame(players, settings)
    
    print("   Running integrated test...")
    
    # Run several ticks
    for tick in range(5):
        print(f"   Tick {tick + 1}:")
        
        # Check abilities before tick
        imp_obs = game._get_observation(impostor_player)
        crew_obs = game._get_observation(crewmate_player)
        
        print(f"     Impostor can_sabotage: {imp_obs.get('can_sabotage', False)}")
        print(f"     Crewmate can_sabotage: {crew_obs.get('can_sabotage', False)}")
        
        # Crewmate should never be able to sabotage
        assert not crew_obs.get('can_sabotage', False), "❌ Crewmate should never be able to sabotage!"
        
        game.run_tick()
        
        active_sabotages = game.sabotage_manager.get_active_sabotages()
        print(f"     Active sabotages: {[s.value for s in active_sabotages]}")
        print(f"     Sabotage cooldown: {game.sabotage_manager.sabotage_cooldown}")
    
    print("   ✅ Test passed: Integrated game follows sabotage rules")

def main():
    print("🔧 Testing Sabotage Fixes")
    print("=" * 50)
    
    try:
        test_crewmate_cannot_sabotage()
        test_impostor_sabotage_cooldown()
        test_only_one_sabotage_active()
        test_integrated_game()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! Sabotage system is working correctly.")
        print("✅ Crewmates cannot sabotage")
        print("✅ Impostors have sabotage cooldown")
        print("✅ Only one sabotage can be active at a time")
        print("✅ Integrated game follows all rules")
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
