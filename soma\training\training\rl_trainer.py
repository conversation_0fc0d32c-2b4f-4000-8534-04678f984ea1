import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, <PERSON><PERSON>
import os
from datetime import datetime
import json

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.rl_agent import RLAgent
from agents.scripted_agent import ScriptedAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

class AmongUsTrainer:
    """Trainer for Among Us RL agents"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device(config.get("device", "cpu"))
        
        # Training parameters
        self.num_episodes = config.get("num_episodes", 1000)
        self.max_steps_per_episode = config.get("max_steps_per_episode", 100)
        self.eval_frequency = config.get("eval_frequency", 50)
        self.save_frequency = config.get("save_frequency", 100)
        
        # Game settings
        self.game_settings = DEFAULT_SETTINGS.copy()
        self.game_settings.update(config.get("game_settings", {}))
        
        # Training statistics
        self.episode_rewards = []
        self.episode_lengths = []
        self.win_rates = {"crewmate": [], "impostor": []}
        self.training_stats = []
        
        # Create save directory
        self.save_dir = config.get("save_dir", f"models/run_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.save_dir, exist_ok=True)
        
        # Save config
        with open(os.path.join(self.save_dir, "config.json"), "w") as f:
            json.dump(config, f, indent=2)
    
    def create_training_game(self, rl_agent_colors: List[str]) -> SimulatedGame:
        """Create a game with RL agents and scripted opponents"""
        
        rooms = create_skeld_map()
        cafeteria = rooms["Cafeteria"]
        players = []
        
        # Determine roles
        num_players = self.config.get("num_players", 6)
        num_impostors = self.config.get("num_impostors", 2)
        colors = ["Red", "Blue", "Green", "Yellow", "Pink", "Cyan", "Orange", "Purple"][:num_players]
        
        import random
        impostor_colors = random.sample(colors, num_impostors)
        
        # Create players
        for color in colors:
            role = IMPOSTOR if color in impostor_colors else CREWMATE
            
            if color in rl_agent_colors:
                # Create RL agent
                agent = RLAgent(
                    player_color=color,
                    role_name=role.name,
                    learning_rate=self.config.get("learning_rate", 1e-4),
                    epsilon=self.config.get("epsilon", 0.1),
                    device=self.device
                )
            else:
                # Create scripted agent
                strategies = {
                    "Impostor": ["aggressive_impostor", "sneaky_impostor"],
                    "Crewmate": ["detective_crewmate", "task_focused_crewmate", "balanced"]
                }
                strategy = random.choice(strategies[role.name])
                agent = ScriptedAgent(color, strategy=strategy)
            
            player = Player(color, role, cafeteria, agent)
            players.append(player)
        
        return SimulatedGame(players, self.game_settings)
    
    def calculate_reward(self, player: Player, game: SimulatedGame, 
                        prev_state: Dict, action: str, new_state: Dict) -> float:
        """Calculate reward for an action"""
        
        reward = 0.0
        
        # Basic survival reward
        if player.alive:
            reward += 0.1
        
        # Role-specific rewards
        if player.role.name in ["Impostor", "Shapeshifter", "Phantom"]:
            reward += self._calculate_impostor_reward(player, game, prev_state, action, new_state)
        else:
            reward += self._calculate_crewmate_reward(player, game, prev_state, action, new_state)
        
        # Game outcome rewards
        if game.is_game_over():
            winner = game.get_winner()
            if player.role.name in ["Impostor", "Shapeshifter", "Phantom"]:
                reward += 10.0 if winner == "Impostors" else -10.0
            else:
                reward += 10.0 if winner == "Crewmates" else -10.0
        
        return reward
    
    def _calculate_impostor_reward(self, player: Player, game: SimulatedGame,
                                 prev_state: Dict, action: str, new_state: Dict) -> float:
        """Calculate impostor-specific rewards"""
        reward = 0.0
        
        # Kill rewards
        prev_living = prev_state.get("living_players", 0)
        new_living = new_state.get("living_players", 0)
        if new_living < prev_living:
            reward += 5.0  # Successful kill
        
        # Sabotage rewards
        prev_sabotages = len(prev_state.get("active_sabotages", []))
        new_sabotages = len(new_state.get("active_sabotages", []))
        if new_sabotages > prev_sabotages:
            reward += 2.0  # Successful sabotage
        
        # Avoid detection (negative reward for being voted out)
        if not player.alive and player in game.dead_players:
            # Check if player was voted out (not killed)
            reward -= 8.0
        
        return reward
    
    def _calculate_crewmate_reward(self, player: Player, game: SimulatedGame,
                                 prev_state: Dict, action: str, new_state: Dict) -> float:
        """Calculate crewmate-specific rewards"""
        reward = 0.0
        
        # Task completion rewards
        prev_tasks = prev_state.get("completed_tasks", 0)
        new_tasks = new_state.get("completed_tasks", 0)
        if new_tasks > prev_tasks:
            reward += 3.0  # Task completed
        
        # Sabotage fixing rewards
        prev_sabotages = len(prev_state.get("active_sabotages", []))
        new_sabotages = len(new_state.get("active_sabotages", []))
        if new_sabotages < prev_sabotages:
            reward += 2.0  # Fixed sabotage
        
        # Survival reward (staying alive is important)
        if player.alive:
            reward += 0.5
        
        return reward
    
    def train_episode(self, rl_agent_colors: List[str]) -> Dict:
        """Train for one episode"""
        
        game = self.create_training_game(rl_agent_colors)
        
        # Get RL agents
        rl_agents = [p.agent for p in game.players 
                    if isinstance(p.agent, RLAgent) and p.color in rl_agent_colors]
        
        # Store initial states
        prev_states = {}
        for agent in rl_agents:
            player = next(p for p in game.players if p.agent == agent)
            prev_states[agent] = game._get_observation(player)
        
        episode_rewards = {agent: 0.0 for agent in rl_agents}
        step_count = 0
        
        while not game.is_game_over() and step_count < self.max_steps_per_episode:
            step_count += 1
            
            # Run one game tick
            game.run_tick()
            
            # Calculate rewards and learn
            for agent in rl_agents:
                player = next(p for p in game.players if p.agent == agent)
                new_state = game._get_observation(player)
                
                # Calculate reward
                reward = self.calculate_reward(
                    player, game, prev_states[agent], "action", new_state
                )
                
                # Learn from experience
                agent.learn(reward, new_state, game.is_game_over())
                
                episode_rewards[agent] += reward
                prev_states[agent] = new_state
        
        # Episode statistics
        winner = game.get_winner() if game.is_game_over() else "None"
        
        return {
            "episode_length": step_count,
            "winner": winner,
            "rewards": episode_rewards,
            "game_over": game.is_game_over()
        }
    
    def evaluate_agents(self, rl_agent_colors: List[str], num_eval_games: int = 10) -> Dict:
        """Evaluate RL agents against scripted opponents"""
        
        # Set agents to evaluation mode
        eval_results = {"wins": 0, "total": num_eval_games, "rewards": []}
        
        for _ in range(num_eval_games):
            game = self.create_training_game(rl_agent_colors)
            
            # Set RL agents to evaluation mode
            for player in game.players:
                if isinstance(player.agent, RLAgent):
                    player.agent.set_training(False)
            
            step_count = 0
            while not game.is_game_over() and step_count < self.max_steps_per_episode:
                game.run_tick()
                step_count += 1
            
            # Check if RL agents won
            winner = game.get_winner() if game.is_game_over() else "None"
            rl_roles = [game.players[i].role.name for i, p in enumerate(game.players) 
                       if p.color in rl_agent_colors]
            
            if winner == "Impostors" and any(role in ["Impostor", "Shapeshifter", "Phantom"] for role in rl_roles):
                eval_results["wins"] += 1
            elif winner == "Crewmates" and any(role == "Crewmate" for role in rl_roles):
                eval_results["wins"] += 1
        
        # Reset agents to training mode
        for player in game.players:
            if isinstance(player.agent, RLAgent):
                player.agent.set_training(True)
        
        eval_results["win_rate"] = eval_results["wins"] / eval_results["total"]
        return eval_results
    
    def train(self):
        """Main training loop"""
        
        print(f"🚀 Starting Among Us RL Training")
        print(f"Episodes: {self.num_episodes}")
        print(f"Device: {self.device}")
        print(f"Save directory: {self.save_dir}")
        print("=" * 50)
        
        # Training configuration
        rl_agent_colors = self.config.get("rl_agent_colors", ["Red", "Blue"])
        
        for episode in range(self.num_episodes):
            # Train episode
            episode_result = self.train_episode(rl_agent_colors)
            
            # Log progress
            if episode % 10 == 0:
                avg_reward = np.mean([sum(r.values()) for r in 
                                    [ep["rewards"] for ep in [episode_result]]])
                print(f"Episode {episode:4d} | "
                      f"Length: {episode_result['episode_length']:3d} | "
                      f"Winner: {episode_result['winner']:10s} | "
                      f"Avg Reward: {avg_reward:6.2f}")
            
            # Evaluation
            if episode % self.eval_frequency == 0 and episode > 0:
                eval_results = self.evaluate_agents(rl_agent_colors)
                print(f"📊 Evaluation | Win Rate: {eval_results['win_rate']:.3f}")
                self.win_rates["overall"] = self.win_rates.get("overall", [])
                self.win_rates["overall"].append(eval_results['win_rate'])
            
            # Save models
            if episode % self.save_frequency == 0 and episode > 0:
                self.save_models(rl_agent_colors, episode)
        
        print("\n🎉 Training completed!")
        self.save_training_plots()
    
    def save_models(self, rl_agent_colors: List[str], episode: int):
        """Save RL agent models"""
        # This would save the models - simplified for now
        print(f"💾 Saved models at episode {episode}")
    
    def save_training_plots(self):
        """Save training progress plots"""
        # Create training plots
        if self.win_rates.get("overall"):
            plt.figure(figsize=(10, 6))
            plt.plot(self.win_rates["overall"])
            plt.title("RL Agent Win Rate Over Training")
            plt.xlabel("Evaluation Episode")
            plt.ylabel("Win Rate")
            plt.grid(True)
            plt.savefig(os.path.join(self.save_dir, "win_rate.png"))
            plt.close()
        
        print(f"📈 Training plots saved to {self.save_dir}")
