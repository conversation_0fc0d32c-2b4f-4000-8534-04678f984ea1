#!/usr/bin/env python3
"""
Quick training demo to show results
"""

import subprocess
import sys
import os
import time

def run_quick_training():
    """Run a quick training session"""
    
    print("🚀 Running Quick Training Demo")
    print("=" * 50)
    print("This will run 20 episodes to demonstrate the training system")
    print("Results will be saved and displayed automatically")
    print("=" * 50)
    
    # Run training
    print("🎮 Starting training...")
    try:
        result = subprocess.run([
            sys.executable, "scripts/train_cpu_safe.py",
            "--episodes", "20"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Training completed successfully!")
            print("\n📊 Training Output:")
            print("-" * 30)
            # Show last 20 lines of output
            output_lines = result.stdout.split('\n')
            for line in output_lines[-20:]:
                if line.strip():
                    print(line)
            print("-" * 30)
        else:
            print("❌ Training failed!")
            print("Error:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Training error: {e}")
        return False
    
    return True

def show_results():
    """Show training results"""
    
    print("\n🔍 Displaying Training Results...")
    print("=" * 40)
    
    try:
        # Run results viewer
        result = subprocess.run([
            sys.executable, "scripts/view_training_results.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("⚠️  Could not display detailed results")
            print("Error:", result.stderr)
            
    except Exception as e:
        print(f"❌ Results viewer error: {e}")

def main():
    print("🎯 Among Us AI Training Demo")
    print("This demo will:")
    print("1. Run 20 training episodes (~2-3 minutes)")
    print("2. Generate analytics and visualizations") 
    print("3. Show you where to find all results")
    print()
    
    response = input("Continue with demo? (y/n): ").lower().strip()
    if response != 'y':
        print("Demo cancelled.")
        return
    
    # Run training
    success = run_quick_training()
    
    if success:
        # Show results
        show_results()
        
        print("\n" + "=" * 60)
        print("🎉 DEMO COMPLETE!")
        print("=" * 60)
        print("📁 Your training results are saved in:")
        print("   • cpu_training_analytics/ - Detailed analytics")
        print("   • training_summary_chart.png - Quick overview chart")
        print()
        print("🚀 To continue training:")
        print("   python train_cpu_safe.py --episodes 1000")
        print()
        print("🤖 To train with LLM communication:")
        print("   python train_cpu_safe.py --episodes 500 --llm")
        print()
        print("📊 To view results anytime:")
        print("   python view_training_results.py")
        
    else:
        print("\n❌ Demo failed. Try running training manually:")
        print("   python train_cpu_safe.py --episodes 20")

if __name__ == "__main__":
    main()
