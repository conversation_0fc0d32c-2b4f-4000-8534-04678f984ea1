#!/usr/bin/env python3
"""
Computer Vision Accuracy Training Script.

Trains the computer vision system to improve detection accuracy
through data collection, human feedback, and iterative learning.
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.cv_training_system import CVTrainingSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cv_accuracy_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Computer Vision Accuracy Training')
    
    parser.add_argument('--mode', type=str, default='collect',
                       choices=['collect', 'evaluate', 'train', 'full'],
                       help='Training mode')
    
    parser.add_argument('--duration', type=int, default=30,
                       help='Data collection duration in minutes')
    
    parser.add_argument('--iterations', type=int, default=5,
                       help='Number of training iterations')
    
    parser.add_argument('--with-labels', action='store_true',
                       help='Include human labeling during data collection')
    
    parser.add_argument('--data-dir', type=str, default='data/cv_training',
                       help='Directory to store training data')
    
    return parser.parse_args()


def collect_data_mode(trainer: CVTrainingSystem, args):
    """Run data collection mode."""
    print("📊 Data Collection Mode")
    print("=" * 30)
    
    print(f"Duration: {args.duration} minutes")
    print(f"Human labeling: {'Yes' if args.with_labels else 'No'}")
    print()
    
    if args.with_labels:
        print("💡 Tips for human labeling:")
        print("  - You'll be asked to verify detections every 20 samples")
        print("  - Press Enter to accept current detection")
        print("  - Type corrections when detections are wrong")
        print("  - Press Ctrl+C during labeling to skip")
        print()
    
    input("Make sure Among Us is running and press Enter to start...")
    
    success = trainer.collect_training_data(
        duration_minutes=args.duration,
        with_labels=args.with_labels
    )
    
    if success:
        print("✅ Data collection completed successfully!")
    else:
        print("❌ Data collection failed!")


def evaluate_mode(trainer: CVTrainingSystem, args):
    """Run evaluation mode."""
    print("📈 Evaluation Mode")
    print("=" * 20)
    
    # Load previous progress
    trainer.load_training_progress()
    
    input("Make sure Among Us is running and press Enter to start evaluation...")
    
    # Collect some fresh data for evaluation
    print("Collecting evaluation data...")
    trainer.collect_training_data(duration_minutes=5, with_labels=False)
    
    # Evaluate performance
    performance = trainer.evaluate_current_performance()
    
    if performance:
        print("\n🎯 Evaluation completed!")
        
        # Show recommendations
        avg_accuracy = sum(metrics['accuracy'] for metrics in performance.values()) / len(performance)
        
        if avg_accuracy < 0.7:
            print("\n💡 Recommendations:")
            print("  - Collect more training data with human labels")
            print("  - Run iterative training to improve thresholds")
            print("  - Check game settings (resolution, graphics quality)")
        elif avg_accuracy < 0.85:
            print("\n💡 Recommendations:")
            print("  - Fine-tune with a few more training iterations")
            print("  - Collect data from different game scenarios")
        else:
            print("\n🎉 Excellent accuracy! System is ready for deployment.")
    else:
        print("❌ Evaluation failed!")


def train_mode(trainer: CVTrainingSystem, args):
    """Run training mode."""
    print("🎓 Training Mode")
    print("=" * 15)
    
    print(f"Iterations: {args.iterations}")
    print("This will collect data, get human feedback, and improve accuracy.")
    print()
    
    # Load previous progress
    trainer.load_training_progress()
    
    input("Make sure Among Us is running and press Enter to start training...")
    
    trainer.train_iteratively(iterations=args.iterations)


def full_mode(trainer: CVTrainingSystem, args):
    """Run full training pipeline."""
    print("🚀 Full Training Pipeline")
    print("=" * 25)
    
    print("This will run the complete training pipeline:")
    print("1. Collect initial training data")
    print("2. Evaluate baseline performance")
    print("3. Run iterative training")
    print("4. Final evaluation")
    print()
    
    input("Make sure Among Us is running and press Enter to start...")
    
    # Step 1: Collect initial data
    print("\n--- Step 1: Initial Data Collection ---")
    success = trainer.collect_training_data(
        duration_minutes=15,
        with_labels=True
    )
    
    if not success:
        print("❌ Initial data collection failed!")
        return
    
    # Step 2: Baseline evaluation
    print("\n--- Step 2: Baseline Evaluation ---")
    baseline_performance = trainer.evaluate_current_performance()
    
    # Step 3: Iterative training
    print("\n--- Step 3: Iterative Training ---")
    trainer.train_iteratively(iterations=3)
    
    # Step 4: Final evaluation
    print("\n--- Step 4: Final Evaluation ---")
    final_performance = trainer.evaluate_current_performance()
    
    # Show improvement summary
    if baseline_performance and final_performance:
        print("\n📊 Training Results Summary")
        print("=" * 30)
        
        for component in baseline_performance.keys():
            baseline_acc = baseline_performance[component]['accuracy']
            final_acc = final_performance[component]['accuracy']
            improvement = final_acc - baseline_acc
            
            print(f"{component}:")
            print(f"  Baseline: {baseline_acc:.2%}")
            print(f"  Final: {final_acc:.2%}")
            print(f"  Improvement: {improvement:+.2%}")
        
        avg_baseline = sum(m['accuracy'] for m in baseline_performance.values()) / len(baseline_performance)
        avg_final = sum(m['accuracy'] for m in final_performance.values()) / len(final_performance)
        total_improvement = avg_final - avg_baseline
        
        print(f"\nOverall Improvement: {total_improvement:+.2%}")
        
        if avg_final > 0.8:
            print("🎉 Excellent results! CV system is ready for deployment.")
        elif avg_final > 0.7:
            print("✅ Good results! Consider additional training for better accuracy.")
        else:
            print("⚠️  More training needed. Try collecting more labeled data.")


def main():
    """Main function."""
    try:
        print("🎯 Among Us Computer Vision Accuracy Training")
        print("=" * 50)
        
        # Parse arguments
        args = parse_arguments()
        
        # Create trainer
        trainer = CVTrainingSystem(data_dir=args.data_dir)
        
        print(f"Mode: {args.mode}")
        print(f"Data directory: {args.data_dir}")
        print()
        
        # Run appropriate mode
        if args.mode == 'collect':
            collect_data_mode(trainer, args)
        elif args.mode == 'evaluate':
            evaluate_mode(trainer, args)
        elif args.mode == 'train':
            train_mode(trainer, args)
        elif args.mode == 'full':
            full_mode(trainer, args)
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
    finally:
        if 'trainer' in locals():
            trainer.cleanup()


if __name__ == "__main__":
    main()
