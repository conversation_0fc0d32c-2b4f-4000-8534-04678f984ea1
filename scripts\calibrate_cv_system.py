#!/usr/bin/env python3
"""
Computer Vision System Calibration Script.

Calibrates the computer vision system for your specific Among Us setup.
This script helps optimize detection accuracy and input timing.
"""

import os
import sys
import time
import logging
import json
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.screen_capture import ScreenCapture
from soma.computer_vision.game_detector import GameStateDetector
from soma.computer_vision.player_detector import PlayerDetector, PlayerColor
from soma.computer_vision.input_controller import InputController

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CVSystemCalibrator:
    """Calibrates the computer vision system for optimal performance."""
    
    def __init__(self, player_color: PlayerColor = PlayerColor.RED):
        self.player_color = player_color
        self.calibration_data = {
            'screen_capture': {},
            'game_detection': {},
            'player_detection': {},
            'input_control': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # Initialize components
        self.screen_capture = ScreenCapture(target_fps=10)
        self.game_detector = GameStateDetector()
        self.player_detector = PlayerDetector()
        self.input_controller = InputController(safety_enabled=True)
    
    def calibrate_screen_capture(self) -> bool:
        """Calibrate screen capture settings."""
        print("🖥️  Calibrating Screen Capture...")
        
        try:
            # Find Among Us window
            if not self.screen_capture.find_among_us_window():
                print("❌ Among Us window not found")
                return False
            
            print("✅ Among Us window found")
            
            # Test different capture rates
            print("   Testing capture rates...")
            fps_tests = [5, 10, 15, 20]
            best_fps = 5
            best_success_rate = 0
            
            for fps in fps_tests:
                self.screen_capture.target_fps = fps
                self.screen_capture.frame_interval = 1.0 / fps
                
                successful_captures = 0
                total_attempts = 10
                
                for _ in range(total_attempts):
                    frame = self.screen_capture.capture_frame()
                    if frame is not None:
                        successful_captures += 1
                    time.sleep(0.1)
                
                success_rate = successful_captures / total_attempts
                print(f"   FPS {fps}: {success_rate:.1%} success rate")
                
                if success_rate > best_success_rate:
                    best_success_rate = success_rate
                    best_fps = fps
            
            print(f"✅ Optimal FPS: {best_fps} ({best_success_rate:.1%} success rate)")
            
            # Store calibration data
            self.calibration_data['screen_capture'] = {
                'optimal_fps': best_fps,
                'success_rate': best_success_rate,
                'window_region': self.screen_capture.window_region,
                'scale_factor': self.screen_capture.scale_factor
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Screen capture calibration failed: {e}")
            return False
    
    def calibrate_game_detection(self) -> bool:
        """Calibrate game state detection."""
        print("\n🎮 Calibrating Game Detection...")
        
        try:
            print("   Analyzing detection accuracy over time...")
            
            detections = []
            confidence_scores = []
            
            # Collect detection data
            for i in range(20):
                frame = self.screen_capture.capture_frame()
                if frame is not None:
                    state_info = self.game_detector.detect_game_state(frame)
                    detections.append(state_info.state.value)
                    confidence_scores.append(state_info.confidence)
                
                time.sleep(0.5)
            
            if not detections:
                print("❌ No detections captured")
                return False
            
            # Analyze stability
            most_common_state = max(set(detections), key=detections.count)
            stability = detections.count(most_common_state) / len(detections)
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            
            print(f"✅ Most common state: {most_common_state}")
            print(f"   Stability: {stability:.1%}")
            print(f"   Average confidence: {avg_confidence:.2f}")
            
            # Store calibration data
            self.calibration_data['game_detection'] = {
                'stability': stability,
                'avg_confidence': avg_confidence,
                'most_common_state': most_common_state,
                'total_detections': len(detections)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Game detection calibration failed: {e}")
            return False
    
    def calibrate_player_detection(self) -> bool:
        """Calibrate player detection."""
        print("\n👥 Calibrating Player Detection...")
        
        try:
            print("   Testing player detection accuracy...")
            
            total_players_detected = 0
            total_frames = 0
            confidence_scores = []
            
            for i in range(15):
                frame = self.screen_capture.capture_frame()
                if frame is not None:
                    players = self.player_detector.detect_players(frame)
                    total_players_detected += len(players)
                    total_frames += 1
                    
                    for player in players:
                        confidence_scores.append(player.confidence)
                
                time.sleep(0.5)
            
            if total_frames == 0:
                print("❌ No frames captured")
                return False
            
            avg_players_per_frame = total_players_detected / total_frames
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            
            print(f"✅ Average players per frame: {avg_players_per_frame:.1f}")
            print(f"   Average confidence: {avg_confidence:.2f}")
            print(f"   Total detections: {total_players_detected}")
            
            # Store calibration data
            self.calibration_data['player_detection'] = {
                'avg_players_per_frame': avg_players_per_frame,
                'avg_confidence': avg_confidence,
                'total_detections': total_players_detected,
                'total_frames': total_frames
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Player detection calibration failed: {e}")
            return False
    
    def calibrate_input_control(self) -> bool:
        """Calibrate input control timing."""
        print("\n🖱️  Calibrating Input Control...")
        
        try:
            self.input_controller.start()
            
            print("   Testing input responsiveness...")
            
            # Test different action delays
            delays = [0.1, 0.5, 1.0, 2.0]
            best_delay = 1.0
            
            for delay in delays:
                print(f"   Testing {delay}s delay...")
                
                # Queue some test actions
                pos = self.input_controller.get_mouse_position()
                
                start_time = time.time()
                self.input_controller.move_to((pos[0] + 5, pos[1] + 5), duration=0.1)
                time.sleep(delay)
                self.input_controller.move_to((pos[0] - 5, pos[1] - 5), duration=0.1)
                time.sleep(delay)
                end_time = time.time()
                
                actual_time = end_time - start_time
                expected_time = delay * 2 + 0.2  # 2 delays + 2 * 0.1s duration
                
                timing_accuracy = expected_time / actual_time if actual_time > 0 else 0
                print(f"     Timing accuracy: {timing_accuracy:.2f}")
                
                if 0.8 <= timing_accuracy <= 1.2:  # Within 20% of expected
                    best_delay = delay
                    break
            
            print(f"✅ Optimal action delay: {best_delay}s")
            
            # Get controller stats
            stats = self.input_controller.get_stats()
            
            # Store calibration data
            self.calibration_data['input_control'] = {
                'optimal_delay': best_delay,
                'controller_stats': stats
            }
            
            self.input_controller.stop()
            return True
            
        except Exception as e:
            print(f"❌ Input control calibration failed: {e}")
            return False
    
    def run_full_calibration(self) -> bool:
        """Run complete system calibration."""
        print("🔧 Starting Full System Calibration")
        print("=" * 40)
        
        calibration_steps = [
            ("Screen Capture", self.calibrate_screen_capture),
            ("Game Detection", self.calibrate_game_detection),
            ("Player Detection", self.calibrate_player_detection),
            ("Input Control", self.calibrate_input_control)
        ]
        
        results = []
        
        for step_name, step_func in calibration_steps:
            try:
                result = step_func()
                results.append((step_name, result))
                
                if not result:
                    print(f"⚠️  {step_name} calibration failed, continuing...")
                
            except Exception as e:
                print(f"❌ {step_name} calibration crashed: {e}")
                results.append((step_name, False))
        
        # Show results
        print("\n📊 Calibration Results")
        print("=" * 25)
        
        passed = 0
        total = len(results)
        
        for step_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {step_name}")
            if result:
                passed += 1
        
        success_rate = passed / total
        print(f"\nOverall: {passed}/{total} steps passed ({success_rate:.1%})")
        
        return success_rate >= 0.75  # At least 75% success rate
    
    def save_calibration(self, filepath: str = None):
        """Save calibration data to file."""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"cv_calibration_{timestamp}.json"
        
        try:
            with open(filepath, 'w') as f:
                json.dump(self.calibration_data, f, indent=2)
            
            print(f"💾 Calibration data saved to: {filepath}")
            
        except Exception as e:
            print(f"❌ Failed to save calibration data: {e}")
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.input_controller.stop()
            self.screen_capture.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Main calibration function."""
    print("🎯 Among Us Computer Vision System Calibration")
    print("=" * 50)
    
    print("This script will calibrate your computer vision system for optimal performance.")
    print("Make sure Among Us is running and visible before starting.")
    print()
    
    # Get player color
    color_input = input("Enter your player color (red/blue/green/pink/orange/yellow/black/white/purple/brown/cyan/lime) [red]: ").strip().lower()
    if not color_input:
        color_input = "red"
    
    color_map = {
        'red': PlayerColor.RED,
        'blue': PlayerColor.BLUE,
        'green': PlayerColor.GREEN,
        'pink': PlayerColor.PINK,
        'orange': PlayerColor.ORANGE,
        'yellow': PlayerColor.YELLOW,
        'black': PlayerColor.BLACK,
        'white': PlayerColor.WHITE,
        'purple': PlayerColor.PURPLE,
        'brown': PlayerColor.BROWN,
        'cyan': PlayerColor.CYAN,
        'lime': PlayerColor.LIME
    }
    
    player_color = color_map.get(color_input, PlayerColor.RED)
    print(f"Using player color: {player_color.value}")
    print()
    
    input("Press Enter when ready to start calibration...")
    print()
    
    # Run calibration
    calibrator = CVSystemCalibrator(player_color)
    
    try:
        success = calibrator.run_full_calibration()
        
        if success:
            print("\n🎉 Calibration completed successfully!")
            
            # Save calibration data
            calibrator.save_calibration()
            
            print("\nRecommended settings based on calibration:")
            screen_data = calibrator.calibration_data.get('screen_capture', {})
            input_data = calibrator.calibration_data.get('input_control', {})
            
            if screen_data:
                print(f"  --fps {screen_data.get('optimal_fps', 5)}")
            if input_data:
                print(f"  --action-delay {input_data.get('optimal_delay', 1.0)}")
            
            print("\nNext steps:")
            print("1. python scripts/train_computer_vision.py --mode observation_only")
            print("2. python scripts/train_computer_vision.py --mode full_automation")
            
        else:
            print("\n⚠️  Calibration completed with some issues.")
            print("The system may still work, but performance might not be optimal.")
            print("Check the failed steps above and ensure Among Us is running properly.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Calibration interrupted by user")
    except Exception as e:
        print(f"\n❌ Calibration failed: {e}")
    finally:
        calibrator.cleanup()


if __name__ == "__main__":
    main()
