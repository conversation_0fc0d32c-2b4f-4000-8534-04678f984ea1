DEFAULT_SETTINGS = {
    # --- Game Structure ---
    "num_players": 6,
    "num_impostors": 1,

    # --- Task Distribution ---
    "common_tasks": 1,
    "short_tasks": 2,
    "long_tasks": 1,

    # --- Game Rules ---
    "confirm_ejects": False,
    "visual_tasks": True,
    "anonymous_voting": True,

    # --- Timing ---
    "kill_cooldown": 20,     # turns
    "emergency_cooldown": 15,
    "discussion_time": 15,   # turns (or simulated seconds)
    "voting_time": 15,
    "sabotage_cooldown": 15, # turns between sabotages (realistic timing)

    # --- Player Restrictions ---
    "max_emergencies_per_player": 1,

    # --- Vision and Detection ---
    "vision_range": 1.0,     # Base vision range
    "lights_vision_modifier": 0.5,  # Vision reduction when lights are off
    "impostor_vision": 1.5,  # Impostor vision multiplier

    # --- Sabotage Settings ---
    "oxygen_crisis_time": 30,    # Time to fix oxygen before loss
    "reactor_crisis_time": 30,   # Time to fix reactor before loss
    "door_sabotage_duration": 10, # How long doors stay closed

    # --- Role Settings ---
    "engineer_vent_cooldown": 15,
    "scientist_vitals_cooldown": 5,
    "shapeshifter_cooldown": 30,
    "shapeshifter_duration": 10,
    "guardian_protect_cooldown": 20,

    # --- Simulation Toggles (for later AI tweaks) ---
    "taskbar_updates": "meetings",  # options: 'always', 'meetings', 'never'
    "enable_advanced_roles": False,  # Enable Engineer, Scientist, etc.
    "enable_sabotages": True,        # Enable sabotage system
    "enable_security_systems": True, # Enable cameras, admin, vitals

    # --- LLM Communication Settings ---
    "enable_llm_communication": False,  # Use LLM for discussion messages
    "ollama_base_url": "http://localhost:11434",
    "ollama_model": "natsumura:latest",  # Use available model
    "llm_max_tokens": 80,        # Max tokens for LLM responses
    "llm_fallback_to_scripted": True,  # Fall back to scripted if LLM fails
}
