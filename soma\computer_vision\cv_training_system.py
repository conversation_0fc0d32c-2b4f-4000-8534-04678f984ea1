"""
Computer Vision Training System for Among Us.

This system trains and improves the accuracy of computer vision detection
through supervised learning, data collection, and iterative refinement.
"""

import cv2
import numpy as np
import json
import os
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import pickle

from .screen_capture import ScreenCapture
from .game_detector import GameStateDetector, GameState
from .player_detector import PlayerDete<PERSON>, PlayerColor, PlayerInfo

logger = logging.getLogger(__name__)


@dataclass
class TrainingLabel:
    """Ground truth label for training data."""
    timestamp: float
    game_state: str
    players: List[Dict[str, Any]]
    ui_elements: List[Dict[str, Any]]
    confidence_scores: Dict[str, float]
    user_corrections: Dict[str, Any]


@dataclass
class DetectionMetrics:
    """Metrics for detection accuracy."""
    total_detections: int = 0
    correct_detections: int = 0
    false_positives: int = 0
    false_negatives: int = 0
    avg_confidence: float = 0.0
    
    @property
    def accuracy(self) -> float:
        if self.total_detections == 0:
            return 0.0
        return self.correct_detections / self.total_detections
    
    @property
    def precision(self) -> float:
        if (self.correct_detections + self.false_positives) == 0:
            return 0.0
        return self.correct_detections / (self.correct_detections + self.false_positives)
    
    @property
    def recall(self) -> float:
        if (self.correct_detections + self.false_negatives) == 0:
            return 0.0
        return self.correct_detections / (self.correct_detections + self.false_negatives)


class CVTrainingSystem:
    """Trains and improves computer vision accuracy."""
    
    def __init__(self, data_dir: str = "data/cv_training"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # CV components
        self.screen_capture = ScreenCapture(target_fps=5)
        self.game_detector = GameStateDetector()
        self.player_detector = PlayerDetector()
        
        # Training data
        self.training_data = []
        self.validation_data = []
        self.labels = []
        
        # Metrics tracking
        self.metrics = {
            'game_state': DetectionMetrics(),
            'player_detection': DetectionMetrics(),
            'ui_detection': DetectionMetrics()
        }
        
        # Adaptive thresholds
        self.adaptive_thresholds = {
            'game_state_confidence': 0.7,
            'player_confidence': 0.6,
            'ui_confidence': 0.5
        }
        
        # Learning parameters
        self.learning_rate = 0.1
        self.improvement_history = []
    
    def collect_training_data(self, duration_minutes: int = 30, with_labels: bool = True):
        """Collect training data with optional human labeling."""
        try:
            print(f"🎓 Collecting training data for {duration_minutes} minutes...")
            
            if not self.screen_capture.find_among_us_window():
                print("❌ Among Us window not found")
                return False
            
            end_time = time.time() + (duration_minutes * 60)
            sample_count = 0
            
            while time.time() < end_time:
                # Capture frame and detections
                frame = self.screen_capture.capture_frame()
                if frame is None:
                    continue
                
                # Get current detections
                game_state_info = self.game_detector.detect_game_state(frame)
                players = self.player_detector.detect_players(frame)
                ui_elements = self.player_detector.detect_ui_elements(frame)
                
                # Store frame and detections
                sample = {
                    'timestamp': time.time(),
                    'frame': frame.copy(),
                    'detections': {
                        'game_state': game_state_info.state.value,
                        'game_state_confidence': game_state_info.confidence,
                        'players': [
                            {
                                'color': p.color.value,
                                'position': p.position,
                                'confidence': p.confidence,
                                'is_dead': p.is_dead
                            }
                            for p in players
                        ],
                        'ui_elements': [
                            {
                                'type': ui.element_type,
                                'position': ui.position,
                                'confidence': ui.confidence
                            }
                            for ui in ui_elements
                        ]
                    }
                }
                
                self.training_data.append(sample)
                sample_count += 1
                
                # Optional human labeling
                if with_labels and sample_count % 20 == 0:  # Every 20th sample
                    self._request_human_label(sample)
                
                # Progress update
                if sample_count % 50 == 0:
                    remaining = (end_time - time.time()) / 60
                    print(f"   📊 Collected {sample_count} samples, {remaining:.1f}min remaining")
                
                time.sleep(1.0)  # 1 FPS for training data
            
            print(f"✅ Collected {sample_count} training samples")
            self._save_training_data()
            return True
            
        except Exception as e:
            logger.error(f"Error collecting training data: {e}")
            return False
    
    def _request_human_label(self, sample: Dict[str, Any]):
        """Request human labeling for a sample."""
        try:
            print("\n🏷️  Human Labeling Required")
            print("Current detections:")
            
            detections = sample['detections']
            print(f"  Game State: {detections['game_state']} (conf: {detections['game_state_confidence']:.2f})")
            print(f"  Players: {len(detections['players'])}")
            print(f"  UI Elements: {len(detections['ui_elements'])}")
            
            # Get corrections
            corrections = {}
            
            # Game state correction
            correct_state = input(f"Correct game state [{detections['game_state']}]: ").strip()
            if correct_state:
                corrections['game_state'] = correct_state
            
            # Player count correction
            correct_players = input(f"Correct player count [{len(detections['players'])}]: ").strip()
            if correct_players.isdigit():
                corrections['player_count'] = int(correct_players)
            
            # Store label
            label = TrainingLabel(
                timestamp=sample['timestamp'],
                game_state=corrections.get('game_state', detections['game_state']),
                players=detections['players'],
                ui_elements=detections['ui_elements'],
                confidence_scores={
                    'game_state': detections['game_state_confidence']
                },
                user_corrections=corrections
            )
            
            self.labels.append(label)
            
        except KeyboardInterrupt:
            print("\n⏹️  Labeling skipped")
        except Exception as e:
            logger.error(f"Error in human labeling: {e}")
    
    def evaluate_current_performance(self) -> Dict[str, float]:
        """Evaluate current detection performance."""
        try:
            print("📊 Evaluating current performance...")
            
            if not self.training_data:
                print("❌ No training data available")
                return {}
            
            # Reset metrics
            for metric in self.metrics.values():
                metric.total_detections = 0
                metric.correct_detections = 0
                metric.false_positives = 0
                metric.false_negatives = 0
                metric.avg_confidence = 0.0
            
            # Evaluate on recent samples
            recent_samples = self.training_data[-100:]  # Last 100 samples
            
            for sample in recent_samples:
                self._evaluate_sample(sample)
            
            # Calculate final metrics
            results = {}
            for name, metric in self.metrics.items():
                results[name] = {
                    'accuracy': metric.accuracy,
                    'precision': metric.precision,
                    'recall': metric.recall,
                    'avg_confidence': metric.avg_confidence
                }
            
            print("📈 Performance Results:")
            for name, result in results.items():
                print(f"  {name}:")
                print(f"    Accuracy: {result['accuracy']:.2%}")
                print(f"    Precision: {result['precision']:.2%}")
                print(f"    Recall: {result['recall']:.2%}")
                print(f"    Avg Confidence: {result['avg_confidence']:.2f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error evaluating performance: {e}")
            return {}
    
    def _evaluate_sample(self, sample: Dict[str, Any]):
        """Evaluate a single sample against ground truth."""
        try:
            detections = sample['detections']
            
            # Find corresponding label
            matching_label = None
            for label in self.labels:
                if abs(label.timestamp - sample['timestamp']) < 5.0:  # Within 5 seconds
                    matching_label = label
                    break
            
            if matching_label is None:
                return  # No ground truth available
            
            # Evaluate game state detection
            self.metrics['game_state'].total_detections += 1
            if detections['game_state'] == matching_label.game_state:
                self.metrics['game_state'].correct_detections += 1
            
            self.metrics['game_state'].avg_confidence += detections['game_state_confidence']
            
            # Evaluate player detection (simplified)
            detected_count = len(detections['players'])
            actual_count = matching_label.user_corrections.get('player_count', detected_count)
            
            self.metrics['player_detection'].total_detections += 1
            if abs(detected_count - actual_count) <= 1:  # Allow ±1 error
                self.metrics['player_detection'].correct_detections += 1
            
        except Exception as e:
            logger.error(f"Error evaluating sample: {e}")
    
    def adapt_thresholds(self):
        """Adapt detection thresholds based on performance."""
        try:
            print("🔧 Adapting detection thresholds...")
            
            # Adapt game state threshold
            game_metric = self.metrics['game_state']
            if game_metric.accuracy < 0.8:  # If accuracy is low
                # Lower threshold to be more permissive
                self.adaptive_thresholds['game_state_confidence'] *= 0.9
            elif game_metric.accuracy > 0.95:  # If accuracy is very high
                # Raise threshold to be more selective
                self.adaptive_thresholds['game_state_confidence'] *= 1.05
            
            # Adapt player detection threshold
            player_metric = self.metrics['player_detection']
            if player_metric.accuracy < 0.7:
                self.adaptive_thresholds['player_confidence'] *= 0.9
            elif player_metric.accuracy > 0.9:
                self.adaptive_thresholds['player_confidence'] *= 1.05
            
            # Clamp thresholds to reasonable ranges
            self.adaptive_thresholds['game_state_confidence'] = np.clip(
                self.adaptive_thresholds['game_state_confidence'], 0.3, 0.9
            )
            self.adaptive_thresholds['player_confidence'] = np.clip(
                self.adaptive_thresholds['player_confidence'], 0.3, 0.8
            )
            
            # Update detectors with new thresholds
            self.game_detector.confidence_threshold = self.adaptive_thresholds['game_state_confidence']
            self.player_detector.confidence_threshold = self.adaptive_thresholds['player_confidence']
            
            print(f"   Game State Threshold: {self.adaptive_thresholds['game_state_confidence']:.2f}")
            print(f"   Player Detection Threshold: {self.adaptive_thresholds['player_confidence']:.2f}")
            
        except Exception as e:
            logger.error(f"Error adapting thresholds: {e}")
    
    def train_iteratively(self, iterations: int = 5):
        """Run iterative training to improve accuracy."""
        try:
            print(f"🎯 Starting iterative training ({iterations} iterations)...")
            
            for i in range(iterations):
                print(f"\n--- Iteration {i+1}/{iterations} ---")
                
                # Collect new data
                print("1. Collecting training data...")
                self.collect_training_data(duration_minutes=10, with_labels=True)
                
                # Evaluate performance
                print("2. Evaluating performance...")
                performance = self.evaluate_current_performance()
                
                # Adapt thresholds
                print("3. Adapting thresholds...")
                self.adapt_thresholds()
                
                # Track improvement
                if performance:
                    avg_accuracy = np.mean([
                        metrics['accuracy'] for metrics in performance.values()
                    ])
                    self.improvement_history.append({
                        'iteration': i + 1,
                        'avg_accuracy': avg_accuracy,
                        'performance': performance,
                        'thresholds': self.adaptive_thresholds.copy()
                    })
                    
                    print(f"   Average Accuracy: {avg_accuracy:.2%}")
                
                # Save progress
                self._save_training_progress()
                
                print(f"✅ Iteration {i+1} completed")
            
            print("\n🎉 Iterative training completed!")
            self._show_improvement_summary()
            
        except Exception as e:
            logger.error(f"Error in iterative training: {e}")
    
    def _show_improvement_summary(self):
        """Show training improvement summary."""
        if not self.improvement_history:
            return
        
        print("\n📈 Training Improvement Summary:")
        print("=" * 40)
        
        for entry in self.improvement_history:
            print(f"Iteration {entry['iteration']}: {entry['avg_accuracy']:.2%} accuracy")
        
        if len(self.improvement_history) > 1:
            initial = self.improvement_history[0]['avg_accuracy']
            final = self.improvement_history[-1]['avg_accuracy']
            improvement = final - initial
            
            print(f"\nTotal Improvement: {improvement:+.2%}")
            print(f"Final Accuracy: {final:.2%}")
    
    def _save_training_data(self):
        """Save training data to disk."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save training samples (without frames to save space)
            samples_data = []
            for sample in self.training_data:
                sample_copy = sample.copy()
                sample_copy.pop('frame', None)  # Remove frame data
                samples_data.append(sample_copy)
            
            samples_file = os.path.join(self.data_dir, f"training_samples_{timestamp}.json")
            with open(samples_file, 'w') as f:
                json.dump(samples_data, f, indent=2)
            
            # Save labels
            if self.labels:
                labels_data = [asdict(label) for label in self.labels]
                labels_file = os.path.join(self.data_dir, f"training_labels_{timestamp}.json")
                with open(labels_file, 'w') as f:
                    json.dump(labels_data, f, indent=2)
            
            print(f"💾 Training data saved to {self.data_dir}")
            
        except Exception as e:
            logger.error(f"Error saving training data: {e}")
    
    def _save_training_progress(self):
        """Save training progress and metrics."""
        try:
            progress_file = os.path.join(self.data_dir, "training_progress.json")
            
            progress_data = {
                'improvement_history': self.improvement_history,
                'adaptive_thresholds': self.adaptive_thresholds,
                'metrics': {
                    name: {
                        'accuracy': metric.accuracy,
                        'precision': metric.precision,
                        'recall': metric.recall,
                        'total_detections': metric.total_detections
                    }
                    for name, metric in self.metrics.items()
                },
                'last_updated': datetime.now().isoformat()
            }
            
            with open(progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2)
            
        except Exception as e:
            logger.error(f"Error saving training progress: {e}")
    
    def load_training_progress(self) -> bool:
        """Load previous training progress."""
        try:
            progress_file = os.path.join(self.data_dir, "training_progress.json")
            
            if not os.path.exists(progress_file):
                return False
            
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
            
            self.improvement_history = progress_data.get('improvement_history', [])
            self.adaptive_thresholds.update(progress_data.get('adaptive_thresholds', {}))
            
            # Apply loaded thresholds
            self.game_detector.confidence_threshold = self.adaptive_thresholds['game_state_confidence']
            self.player_detector.confidence_threshold = self.adaptive_thresholds['player_confidence']
            
            print("✅ Loaded previous training progress")
            return True
            
        except Exception as e:
            logger.error(f"Error loading training progress: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.screen_capture.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
