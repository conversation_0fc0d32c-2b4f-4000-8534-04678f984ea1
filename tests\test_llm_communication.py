#!/usr/bin/env python3
"""
Test script for LLM-powered communication in Among Us simulation.
Requires Ollama to be running locally.
"""

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.scripted_agent import ScriptedAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

def test_ollama_connection():
    """Test if Ollama is available"""
    import requests
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama is running with {len(models)} models available")
            for model in models[:3]:  # Show first 3 models
                print(f"   - {model['name']}")
            return True
        else:
            print("❌ Ollama is running but returned error")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama is not running or not accessible")
        print("   Please start Ollama with: ollama serve")
        print("   And ensure you have a model installed: ollama pull llama3.2")
        return False

def run_llm_communication_demo():
    """Run a demo with LLM-powered communication"""
    print("🤖 LLM Communication Demo")
    print("=" * 50)
    
    # Check Ollama availability
    if not test_ollama_connection():
        print("\n⚠️  Running with scripted communication instead")
        use_llm = False
    else:
        use_llm = True
    
    # Setup enhanced settings
    settings = DEFAULT_SETTINGS.copy()
    settings["enable_llm_communication"] = use_llm
    settings["enable_sabotages"] = True
    settings["enable_security_systems"] = True
    settings["ollama_model"] = "llama3.2"  # Change this to your preferred model
    
    # Create game with fewer players for clearer demo
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    players = [
        Player("Red", CREWMATE, cafeteria, ScriptedAgent("Red", "detective_crewmate")),
        Player("Blue", CREWMATE, cafeteria, ScriptedAgent("Blue", "task_focused_crewmate")),
        Player("Green", IMPOSTOR, cafeteria, ScriptedAgent("Green", "sneaky_impostor")),
        Player("Yellow", CREWMATE, cafeteria, ScriptedAgent("Yellow", "balanced")),
    ]
    
    game = SimulatedGame(players, settings)
    
    print(f"\n🎮 Starting game with {'LLM' if use_llm else 'Scripted'} communication")
    print(f"Players: {len(players)}")
    print(f"Impostors: {len([p for p in players if p.role.name == 'Impostor'])}")
    
    # Force a meeting for demonstration
    print("\n🔧 Forcing a meeting for communication demo...")
    cafeteria.reported_body = True
    game.last_meeting_caller = "Red"
    
    # Add some fake deaths for context
    yellow_player = next(p for p in players if p.color == "Yellow")
    yellow_player.alive = False
    game.living_players.remove(yellow_player)
    game.dead_players.append(yellow_player)
    
    # Run the meeting
    game._resolve_meetings()
    
    print(f"\n{'='*50}")
    print("Demo completed!")
    
    if use_llm:
        print("\n🎉 LLM communication is working!")
        print("The messages above were generated by the language model.")
        print("Notice how they're more natural and contextual than scripted responses.")
    else:
        print("\n📝 Scripted communication was used as fallback.")
        print("To use LLM communication:")
        print("1. Install Ollama: https://ollama.ai/")
        print("2. Start Ollama: ollama serve")
        print("3. Install a model: ollama pull llama3.2")
        print("4. Run this script again")

def run_interactive_llm_test():
    """Interactive test of LLM communication"""
    from core.llm_communication import OllamaClient, LLMCommunicationStrategy
    
    print("\n🧪 Interactive LLM Test")
    print("-" * 30)
    
    if not test_ollama_connection():
        return
    
    # Create LLM client
    client = OllamaClient()
    
    # Test different roles
    roles = [("Red", "Crewmate"), ("Green", "Impostor")]
    
    for color, role in roles:
        print(f"\n🎭 Testing {color} ({role}):")
        strategy = LLMCommunicationStrategy(color, role, client)
        
        # Test initial message generation
        game_state = {
            'living_players': ['Red', 'Blue', 'Green', 'Yellow'],
            'dead_players': ['Purple'],
            'recent_deaths': ['Purple']
        }
        
        messages = strategy.generate_messages(game_state, {})
        for msg in messages:
            print(f"   {msg}")

if __name__ == "__main__":
    print("🚀 Among Us LLM Communication Test")
    print("=" * 50)
    
    # Run main demo
    run_llm_communication_demo()
    
    # Run interactive test if user wants
    try:
        response = input("\n❓ Run interactive LLM test? (y/n): ").lower().strip()
        if response == 'y':
            run_interactive_llm_test()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
