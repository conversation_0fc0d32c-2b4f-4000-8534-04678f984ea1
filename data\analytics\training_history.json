{"metrics": [{"episode": 0, "timestamp": "2025-08-04T19:14:24.194987", "role": "Scientist", "won": true, "win_rate_window": 0.2972865906832211, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 1, "timestamp": "2025-08-04T19:14:24.194991", "role": "Phantom", "won": false, "win_rate_window": 0.06399687763088485, "reward": -1.3102086837358584, "episode_length": 52, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 2, "timestamp": "2025-08-04T19:14:24.194993", "role": "Tracker", "won": true, "win_rate_window": 0.37781128328651625, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 3, "timestamp": "2025-08-04T19:14:24.194994", "role": "Engineer", "won": false, "win_rate_window": 0.05, "reward": -0.9029011104416099, "episode_length": 89, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 4, "timestamp": "2025-08-04T19:14:24.194995", "role": "Phantom", "won": false, "win_rate_window": 0.2188623258982905, "reward": -0.7732717653415415, "episode_length": 115, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 5, "timestamp": "2025-08-04T19:14:24.194996", "role": "Shapeshifter", "won": false, "win_rate_window": 0.3060532180878779, "reward": -1.9978053855948987, "episode_length": 70, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 6, "timestamp": "2025-08-04T19:14:24.194998", "role": "Scientist", "won": true, "win_rate_window": 0.30760958826450874, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 7, "timestamp": "2025-08-04T19:14:24.194999", "role": "Engineer", "won": false, "win_rate_window": 0.2494551472817709, "reward": -1.5250259901847942, "episode_length": 62, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 8, "timestamp": "2025-08-04T19:14:24.195000", "role": "Impostor", "won": true, "win_rate_window": 0.4235859341601057, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 9, "timestamp": "2025-08-04T19:14:24.195001", "role": "Crewmate", "won": false, "win_rate_window": 0.3429609858693184, "reward": -0.7538197848612758, "episode_length": 62, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 10, "timestamp": "2025-08-04T19:14:24.195002", "role": "Phantom", "won": false, "win_rate_window": 0.34437844290075403, "reward": -1.289711679826876, "episode_length": 110, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 11, "timestamp": "2025-08-04T19:14:24.195003", "role": "Phantom", "won": false, "win_rate_window": 0.33088311771507717, "reward": -1.7689703614781156, "episode_length": 40, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 12, "timestamp": "2025-08-04T19:14:24.195004", "role": "Tracker", "won": false, "win_rate_window": 0.07274674367728362, "reward": -0.8001685777749339, "episode_length": 119, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 13, "timestamp": "2025-08-04T19:14:24.195005", "role": "Scientist", "won": false, "win_rate_window": 0.2762124015715515, "reward": -1.5939787852269551, "episode_length": 101, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 14, "timestamp": "2025-08-04T19:14:24.195006", "role": "Phantom", "won": false, "win_rate_window": 0.06288231764016414, "reward": -0.4637281116679561, "episode_length": 69, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 15, "timestamp": "2025-08-04T19:14:24.195007", "role": "Impostor", "won": false, "win_rate_window": 0.06790793085260843, "reward": -1.3872847887268678, "episode_length": 114, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 16, "timestamp": "2025-08-04T19:14:24.195008", "role": "Crewmate", "won": true, "win_rate_window": 0.2214924417663241, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 17, "timestamp": "2025-08-04T19:14:24.195009", "role": "Tracker", "won": false, "win_rate_window": 0.2707907131942369, "reward": -0.47120436504882496, "episode_length": 117, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 18, "timestamp": "2025-08-04T19:14:24.195010", "role": "Crewmate", "won": true, "win_rate_window": 0.24592559349602744, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 19, "timestamp": "2025-08-04T19:14:24.195011", "role": "Impostor", "won": false, "win_rate_window": 0.36339894459674765, "reward": -1.9624110231543002, "episode_length": 88, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 20, "timestamp": "2025-08-04T19:14:24.195013", "role": "Impostor", "won": false, "win_rate_window": 0.30952232948102987, "reward": -1.5345248384241612, "episode_length": 109, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 21, "timestamp": "2025-08-04T19:14:24.195015", "role": "Crewmate", "won": false, "win_rate_window": 0.36246313488686827, "reward": -1.076355763806766, "episode_length": 63, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 22, "timestamp": "2025-08-04T19:14:24.195017", "role": "Guardian Angel", "won": true, "win_rate_window": 0.2608961243091499, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 23, "timestamp": "2025-08-04T19:14:24.195018", "role": "Impostor", "won": false, "win_rate_window": 0.40651695228935514, "reward": -1.4401599587122502, "episode_length": 60, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 24, "timestamp": "2025-08-04T19:14:24.195019", "role": "Scientist", "won": false, "win_rate_window": 0.14521711182374122, "reward": -1.485674780617504, "episode_length": 46, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 25, "timestamp": "2025-08-04T19:14:24.195020", "role": "Engineer", "won": false, "win_rate_window": 0.33554878963621987, "reward": -0.992999005876847, "episode_length": 113, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 26, "timestamp": "2025-08-04T19:14:24.195021", "role": "Scientist", "won": true, "win_rate_window": 0.40204630423800247, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 27, "timestamp": "2025-08-04T19:14:24.195022", "role": "Scientist", "won": true, "win_rate_window": 0.521713977171372, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 28, "timestamp": "2025-08-04T19:14:24.195023", "role": "Tracker", "won": false, "win_rate_window": 0.2263822779764802, "reward": -0.7609290378395331, "episode_length": 62, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 29, "timestamp": "2025-08-04T19:14:24.195024", "role": "Phantom", "won": false, "win_rate_window": 0.05, "reward": -2.0727517859949973, "episode_length": 104, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 30, "timestamp": "2025-08-04T19:14:24.195025", "role": "Impostor", "won": false, "win_rate_window": 0.29060708211488595, "reward": -0.7089239775608074, "episode_length": 120, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 31, "timestamp": "2025-08-04T19:14:24.195026", "role": "Guardian Angel", "won": false, "win_rate_window": 0.48393509265385937, "reward": -1.9843111719210542, "episode_length": 95, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 32, "timestamp": "2025-08-04T19:14:24.195027", "role": "Phantom", "won": false, "win_rate_window": 0.3048345865270556, "reward": -0.7428718458420474, "episode_length": 100, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 33, "timestamp": "2025-08-04T19:14:24.195028", "role": "Shapeshifter", "won": false, "win_rate_window": 0.2566769668651199, "reward": -1.2494488215072146, "episode_length": 81, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 34, "timestamp": "2025-08-04T19:14:24.195029", "role": "Shapeshifter", "won": false, "win_rate_window": 0.28439080499512814, "reward": -0.979980394875845, "episode_length": 120, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 35, "timestamp": "2025-08-04T19:14:24.195030", "role": "Engineer", "won": true, "win_rate_window": 0.4749860456019027, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 36, "timestamp": "2025-08-04T19:14:24.195031", "role": "Scientist", "won": false, "win_rate_window": 0.507410528177876, "reward": -0.8880481413030058, "episode_length": 77, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 37, "timestamp": "2025-08-04T19:14:24.195032", "role": "Guardian Angel", "won": false, "win_rate_window": 0.24434613228481933, "reward": -2.6614520346566732, "episode_length": 71, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 38, "timestamp": "2025-08-04T19:14:24.195033", "role": "Tracker", "won": false, "win_rate_window": 0.2450328336897344, "reward": -1.4533155709444014, "episode_length": 60, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 39, "timestamp": "2025-08-04T19:14:24.195034", "role": "Shapeshifter", "won": true, "win_rate_window": 0.5839406860848174, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 40, "timestamp": "2025-08-04T19:14:24.195036", "role": "Impostor", "won": false, "win_rate_window": 0.5022007249650733, "reward": -0.14041412123111655, "episode_length": 92, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 41, "timestamp": "2025-08-04T19:14:24.195037", "role": "Crewmate", "won": false, "win_rate_window": 0.21825157566503134, "reward": -0.7499941380576013, "episode_length": 56, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 42, "timestamp": "2025-08-04T19:14:24.195038", "role": "Engineer", "won": false, "win_rate_window": 0.4258807134553459, "reward": -2.0518130557090193, "episode_length": 44, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 43, "timestamp": "2025-08-04T19:14:24.195039", "role": "Impostor", "won": false, "win_rate_window": 0.4549143122960803, "reward": -0.76265717037903, "episode_length": 94, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 44, "timestamp": "2025-08-04T19:14:24.195040", "role": "Impostor", "won": true, "win_rate_window": 0.30338296178307406, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 45, "timestamp": "2025-08-04T19:14:24.195041", "role": "Phantom", "won": true, "win_rate_window": 0.43504123394247174, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 46, "timestamp": "2025-08-04T19:14:24.195042", "role": "Shapeshifter", "won": true, "win_rate_window": 0.550721968696733, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 47, "timestamp": "2025-08-04T19:14:24.195043", "role": "Phantom", "won": true, "win_rate_window": 0.47919315091303316, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 48, "timestamp": "2025-08-04T19:14:24.195044", "role": "Guardian Angel", "won": false, "win_rate_window": 0.4196829867382066, "reward": -1.4992341032040382, "episode_length": 46, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 49, "timestamp": "2025-08-04T19:14:24.195045", "role": "Impostor", "won": true, "win_rate_window": 0.6131074865265693, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 50, "timestamp": "2025-08-04T19:14:24.195046", "role": "Scientist", "won": false, "win_rate_window": 0.2584746265409682, "reward": -1.0654702910138247, "episode_length": 97, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 51, "timestamp": "2025-08-04T19:14:24.195047", "role": "Engineer", "won": false, "win_rate_window": 0.45807651593206883, "reward": -2.306562153413744, "episode_length": 75, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 52, "timestamp": "2025-08-04T19:14:24.195048", "role": "Crewmate", "won": true, "win_rate_window": 0.6104259812337762, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 53, "timestamp": "2025-08-04T19:14:24.195049", "role": "Engineer", "won": false, "win_rate_window": 0.35766242753894456, "reward": -1.1883170688815934, "episode_length": 53, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 54, "timestamp": "2025-08-04T19:14:24.195050", "role": "Guardian Angel", "won": true, "win_rate_window": 0.3590928077213505, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 55, "timestamp": "2025-08-04T19:14:24.195051", "role": "Shapeshifter", "won": false, "win_rate_window": 0.3377394214880296, "reward": -1.4035921147968737, "episode_length": 105, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 56, "timestamp": "2025-08-04T19:14:24.195052", "role": "Crewmate", "won": false, "win_rate_window": 0.5468654161154801, "reward": -1.675103180681293, "episode_length": 77, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 57, "timestamp": "2025-08-04T19:14:24.195053", "role": "Engineer", "won": true, "win_rate_window": 0.4450450427182366, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 58, "timestamp": "2025-08-04T19:14:24.195054", "role": "Crewmate", "won": true, "win_rate_window": 0.5755309805319293, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 59, "timestamp": "2025-08-04T19:14:24.195055", "role": "Tracker", "won": false, "win_rate_window": 0.42439779069282874, "reward": -1.8400936868183246, "episode_length": 63, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 60, "timestamp": "2025-08-04T19:14:24.195057", "role": "Tracker", "won": true, "win_rate_window": 0.4772641963988067, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 61, "timestamp": "2025-08-04T19:14:24.195058", "role": "Scientist", "won": true, "win_rate_window": 0.3534836205723714, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 62, "timestamp": "2025-08-04T19:14:24.195059", "role": "Shapeshifter", "won": false, "win_rate_window": 0.29013162606591625, "reward": -1.9251116518770077, "episode_length": 58, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 63, "timestamp": "2025-08-04T19:14:24.195060", "role": "Impostor", "won": false, "win_rate_window": 0.30115249111672726, "reward": -1.1470740891448108, "episode_length": 70, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 64, "timestamp": "2025-08-04T19:14:24.195061", "role": "Phantom", "won": false, "win_rate_window": 0.4139661134743719, "reward": -1.3998767338376565, "episode_length": 72, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 65, "timestamp": "2025-08-04T19:14:24.195063", "role": "Shapeshifter", "won": false, "win_rate_window": 0.46371251446856127, "reward": -1.4257358425676958, "episode_length": 44, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 66, "timestamp": "2025-08-04T19:14:24.195064", "role": "Shapeshifter", "won": false, "win_rate_window": 0.30470044899794935, "reward": -2.5846681211294817, "episode_length": 67, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 67, "timestamp": "2025-08-04T19:14:24.195065", "role": "Guardian Angel", "won": false, "win_rate_window": 0.36807000761313236, "reward": -1.1395171907345185, "episode_length": 78, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 68, "timestamp": "2025-08-04T19:14:24.195067", "role": "Shapeshifter", "won": false, "win_rate_window": 0.4628621279982263, "reward": -0.6746090377969732, "episode_length": 58, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 69, "timestamp": "2025-08-04T19:14:24.195068", "role": "Phantom", "won": false, "win_rate_window": 0.4247008206614007, "reward": -1.0650777929843103, "episode_length": 88, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 70, "timestamp": "2025-08-04T19:14:24.195069", "role": "Phantom", "won": false, "win_rate_window": 0.38142204436236127, "reward": -1.218479071127314, "episode_length": 99, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 71, "timestamp": "2025-08-04T19:14:24.195070", "role": "Scientist", "won": true, "win_rate_window": 0.3613987794827124, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 72, "timestamp": "2025-08-04T19:14:24.195072", "role": "Tracker", "won": true, "win_rate_window": 0.6455777323538214, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 73, "timestamp": "2025-08-04T19:14:24.195073", "role": "Scientist", "won": false, "win_rate_window": 0.354299501662244, "reward": -0.7537493655042574, "episode_length": 105, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 74, "timestamp": "2025-08-04T19:14:24.195074", "role": "Tracker", "won": true, "win_rate_window": 0.580461651093665, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 75, "timestamp": "2025-08-04T19:14:24.195075", "role": "Guardian Angel", "won": true, "win_rate_window": 0.45589309191097394, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 76, "timestamp": "2025-08-04T19:14:24.195076", "role": "Scientist", "won": true, "win_rate_window": 0.6489639521409577, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 77, "timestamp": "2025-08-04T19:14:24.195079", "role": "Crewmate", "won": false, "win_rate_window": 0.19140972622439328, "reward": -1.0635619463348283, "episode_length": 69, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 78, "timestamp": "2025-08-04T19:14:24.195080", "role": "Engineer", "won": false, "win_rate_window": 0.35385884352894614, "reward": -0.5987488085831286, "episode_length": 99, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 79, "timestamp": "2025-08-04T19:14:24.195082", "role": "Guardian Angel", "won": false, "win_rate_window": 0.3330795226243771, "reward": -1.064671665574903, "episode_length": 119, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 80, "timestamp": "2025-08-04T19:14:24.195083", "role": "Guardian Angel", "won": false, "win_rate_window": 0.574537276921842, "reward": -1.166924980158857, "episode_length": 40, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 81, "timestamp": "2025-08-04T19:14:24.195084", "role": "Scientist", "won": true, "win_rate_window": 0.35129763911269385, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 82, "timestamp": "2025-08-04T19:14:24.195085", "role": "Crewmate", "won": false, "win_rate_window": 0.5895508763375308, "reward": -0.773215372810513, "episode_length": 86, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 83, "timestamp": "2025-08-04T19:14:24.195086", "role": "Phantom", "won": true, "win_rate_window": 0.6826766537208095, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 84, "timestamp": "2025-08-04T19:14:24.195087", "role": "Guardian Angel", "won": true, "win_rate_window": 0.6099573029982024, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 85, "timestamp": "2025-08-04T19:14:24.195088", "role": "Shapeshifter", "won": false, "win_rate_window": 0.629004584920454, "reward": -0.8561252562483678, "episode_length": 108, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 86, "timestamp": "2025-08-04T19:14:24.195089", "role": "Tracker", "won": true, "win_rate_window": 0.8681170765339852, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 87, "timestamp": "2025-08-04T19:14:24.195090", "role": "Phantom", "won": false, "win_rate_window": 0.5083139237286127, "reward": -1.8251940639963624, "episode_length": 84, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 88, "timestamp": "2025-08-04T19:14:24.195091", "role": "Engineer", "won": true, "win_rate_window": 0.5477894404534477, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 89, "timestamp": "2025-08-04T19:14:24.195092", "role": "Engineer", "won": true, "win_rate_window": 0.5643805606931991, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 90, "timestamp": "2025-08-04T19:14:24.195093", "role": "Engineer", "won": false, "win_rate_window": 0.4642604693019715, "reward": -0.7119458522528762, "episode_length": 105, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 91, "timestamp": "2025-08-04T19:14:24.195094", "role": "Scientist", "won": true, "win_rate_window": 0.6303759759329916, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 92, "timestamp": "2025-08-04T19:14:24.195095", "role": "Tracker", "won": false, "win_rate_window": 0.3161223277230716, "reward": -0.75473434842299, "episode_length": 55, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 93, "timestamp": "2025-08-04T19:14:24.195096", "role": "Crewmate", "won": true, "win_rate_window": 0.6179051755933354, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 94, "timestamp": "2025-08-04T19:14:24.195097", "role": "Scientist", "won": false, "win_rate_window": 0.2737989940557853, "reward": -0.8446825376066949, "episode_length": 60, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 95, "timestamp": "2025-08-04T19:14:24.195098", "role": "Impostor", "won": false, "win_rate_window": 0.7482825960207407, "reward": -0.34423914048360665, "episode_length": 90, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 96, "timestamp": "2025-08-04T19:14:24.195099", "role": "Phantom", "won": false, "win_rate_window": 0.7389110576690792, "reward": -1.3980069347606447, "episode_length": 85, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 97, "timestamp": "2025-08-04T19:14:24.195100", "role": "Scientist", "won": true, "win_rate_window": 0.7183865907654824, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 98, "timestamp": "2025-08-04T19:14:24.195101", "role": "Scientist", "won": false, "win_rate_window": 0.6420811889028032, "reward": -0.7987550166034983, "episode_length": 64, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 99, "timestamp": "2025-08-04T19:14:24.195103", "role": "Phantom", "won": true, "win_rate_window": 0.5062442282335358, "reward": 3.5999999999999996, "episode_length": 93, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 100, "timestamp": "2025-08-04T19:14:24.195104", "role": "Guardian Angel", "won": false, "win_rate_window": 0.3694917591188195, "reward": -1.2481820883179593, "episode_length": 115, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 101, "timestamp": "2025-08-04T19:14:24.195105", "role": "Impostor", "won": false, "win_rate_window": 0.8132657404164573, "reward": -0.3691495831674072, "episode_length": 98, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 102, "timestamp": "2025-08-04T19:14:24.195106", "role": "Engineer", "won": true, "win_rate_window": 0.5722121172834885, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 103, "timestamp": "2025-08-04T19:14:24.195107", "role": "Tracker", "won": true, "win_rate_window": 0.6507569547723098, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 104, "timestamp": "2025-08-04T19:14:24.195108", "role": "Engineer", "won": false, "win_rate_window": 0.5469533692998474, "reward": -1.1533457023292202, "episode_length": 90, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 105, "timestamp": "2025-08-04T19:14:24.195109", "role": "Shapeshifter", "won": true, "win_rate_window": 0.4034915955852162, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 106, "timestamp": "2025-08-04T19:14:24.195110", "role": "Tracker", "won": true, "win_rate_window": 0.42203167510501033, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 107, "timestamp": "2025-08-04T19:14:24.195111", "role": "Phantom", "won": false, "win_rate_window": 0.4829084440506285, "reward": -0.09974322565799358, "episode_length": 60, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 108, "timestamp": "2025-08-04T19:14:24.195112", "role": "Impostor", "won": true, "win_rate_window": 0.5806174135143367, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 109, "timestamp": "2025-08-04T19:14:24.195114", "role": "Impostor", "won": true, "win_rate_window": 0.792309069547112, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 110, "timestamp": "2025-08-04T19:14:24.195115", "role": "Scientist", "won": false, "win_rate_window": 0.5262366912679025, "reward": -1.9106310845140009, "episode_length": 56, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 111, "timestamp": "2025-08-04T19:14:24.195116", "role": "Guardian Angel", "won": true, "win_rate_window": 0.6529415154872522, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 112, "timestamp": "2025-08-04T19:14:24.195117", "role": "Shapeshifter", "won": false, "win_rate_window": 0.484406238113679, "reward": -1.213118140140143, "episode_length": 106, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 113, "timestamp": "2025-08-04T19:14:24.195118", "role": "Engineer", "won": true, "win_rate_window": 0.3963376489978902, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 114, "timestamp": "2025-08-04T19:14:24.195119", "role": "Tracker", "won": false, "win_rate_window": 0.4514311344260369, "reward": -1.4485573590523646, "episode_length": 117, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 115, "timestamp": "2025-08-04T19:14:24.195120", "role": "Tracker", "won": true, "win_rate_window": 0.7524840887218693, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 116, "timestamp": "2025-08-04T19:14:24.195120", "role": "Impostor", "won": false, "win_rate_window": 0.6939425142247029, "reward": -0.2158988753858753, "episode_length": 42, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 117, "timestamp": "2025-08-04T19:14:24.195121", "role": "Tracker", "won": true, "win_rate_window": 0.5932941310549733, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 118, "timestamp": "2025-08-04T19:14:24.195123", "role": "Crewmate", "won": true, "win_rate_window": 0.743259052771834, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 119, "timestamp": "2025-08-04T19:14:24.195124", "role": "Impostor", "won": true, "win_rate_window": 0.7072001293030123, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 120, "timestamp": "2025-08-04T19:14:24.195125", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 121, "timestamp": "2025-08-04T19:14:24.195126", "role": "Engineer", "won": true, "win_rate_window": 0.6182839846051092, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 122, "timestamp": "2025-08-04T19:14:24.195128", "role": "Impostor", "won": true, "win_rate_window": 0.7861528189818936, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 123, "timestamp": "2025-08-04T19:14:24.195129", "role": "Tracker", "won": false, "win_rate_window": 0.5551587403912129, "reward": -1.4816097489693463, "episode_length": 96, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 124, "timestamp": "2025-08-04T19:14:24.195130", "role": "Scientist", "won": true, "win_rate_window": 0.22717767516493836, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 125, "timestamp": "2025-08-04T19:14:24.195131", "role": "Phantom", "won": false, "win_rate_window": 0.5176844807054448, "reward": -1.3748678992224372, "episode_length": 79, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 126, "timestamp": "2025-08-04T19:14:24.195132", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7121712776329293, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 127, "timestamp": "2025-08-04T19:14:24.195133", "role": "Crewmate", "won": true, "win_rate_window": 0.686054884985435, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 128, "timestamp": "2025-08-04T19:14:24.195134", "role": "Scientist", "won": true, "win_rate_window": 0.8417242357943222, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 129, "timestamp": "2025-08-04T19:14:24.195135", "role": "Phantom", "won": false, "win_rate_window": 0.5362153681611942, "reward": -0.4776780316229239, "episode_length": 104, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 130, "timestamp": "2025-08-04T19:14:24.195136", "role": "Phantom", "won": true, "win_rate_window": 0.7452380310632815, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 131, "timestamp": "2025-08-04T19:14:24.195138", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 132, "timestamp": "2025-08-04T19:14:24.195139", "role": "Tracker", "won": true, "win_rate_window": 0.675414804588343, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 133, "timestamp": "2025-08-04T19:14:24.195140", "role": "Crewmate", "won": true, "win_rate_window": 0.7473318805928848, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 134, "timestamp": "2025-08-04T19:14:24.195141", "role": "Crewmate", "won": true, "win_rate_window": 0.9426928014404977, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 135, "timestamp": "2025-08-04T19:14:24.195142", "role": "Scientist", "won": true, "win_rate_window": 0.7714972729222109, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 136, "timestamp": "2025-08-04T19:14:24.195143", "role": "Tracker", "won": true, "win_rate_window": 0.8506609119491646, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 137, "timestamp": "2025-08-04T19:14:24.195144", "role": "Phantom", "won": true, "win_rate_window": 0.4485458995088442, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 138, "timestamp": "2025-08-04T19:14:24.195162", "role": "Phantom", "won": true, "win_rate_window": 0.7783456462465598, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 139, "timestamp": "2025-08-04T19:14:24.195163", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6731343715192722, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 140, "timestamp": "2025-08-04T19:14:24.195164", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7571934815163058, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 141, "timestamp": "2025-08-04T19:14:24.195165", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7630615529225219, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 142, "timestamp": "2025-08-04T19:14:24.195166", "role": "Crewmate", "won": true, "win_rate_window": 0.8172387567769366, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 143, "timestamp": "2025-08-04T19:14:24.195169", "role": "Shapeshifter", "won": true, "win_rate_window": 0.5145348646968084, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 144, "timestamp": "2025-08-04T19:14:24.195170", "role": "Scientist", "won": true, "win_rate_window": 0.7106016485288699, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 145, "timestamp": "2025-08-04T19:14:24.195171", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9458844323464854, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 146, "timestamp": "2025-08-04T19:14:24.195172", "role": "Tracker", "won": true, "win_rate_window": 0.7351155733377959, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 147, "timestamp": "2025-08-04T19:14:24.195176", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 148, "timestamp": "2025-08-04T19:14:24.195177", "role": "Phantom", "won": true, "win_rate_window": 0.7930384876257679, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 149, "timestamp": "2025-08-04T19:14:24.195179", "role": "Engineer", "won": true, "win_rate_window": 0.6432715628887169, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 150, "timestamp": "2025-08-04T19:14:24.195180", "role": "Engineer", "won": true, "win_rate_window": 0.7728788387380005, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 151, "timestamp": "2025-08-04T19:14:24.195181", "role": "Scientist", "won": true, "win_rate_window": 0.5424680341211039, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 152, "timestamp": "2025-08-04T19:14:24.195182", "role": "Tracker", "won": true, "win_rate_window": 0.7937547170658561, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 153, "timestamp": "2025-08-04T19:14:24.195183", "role": "Scientist", "won": true, "win_rate_window": 0.8350797051667328, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 154, "timestamp": "2025-08-04T19:14:24.195184", "role": "Crewmate", "won": true, "win_rate_window": 0.6814090439397095, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 155, "timestamp": "2025-08-04T19:14:24.195185", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 92, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 156, "timestamp": "2025-08-04T19:14:24.195186", "role": "Impostor", "won": true, "win_rate_window": 0.7584947262326762, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 157, "timestamp": "2025-08-04T19:14:24.195188", "role": "Crewmate", "won": false, "win_rate_window": 0.593504803880466, "reward": -1.35448772524877, "episode_length": 58, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 158, "timestamp": "2025-08-04T19:14:24.195189", "role": "Crewmate", "won": false, "win_rate_window": 0.5152603233373007, "reward": -0.6394553861842898, "episode_length": 47, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 159, "timestamp": "2025-08-04T19:14:24.195190", "role": "Crewmate", "won": true, "win_rate_window": 0.8017276087566853, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 160, "timestamp": "2025-08-04T19:14:24.195191", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8755501536639252, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 161, "timestamp": "2025-08-04T19:14:24.195192", "role": "Tracker", "won": true, "win_rate_window": 0.7847819431863456, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 162, "timestamp": "2025-08-04T19:14:24.195193", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 163, "timestamp": "2025-08-04T19:14:24.195194", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 164, "timestamp": "2025-08-04T19:14:24.195195", "role": "Engineer", "won": true, "win_rate_window": 0.8438956381183382, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 165, "timestamp": "2025-08-04T19:14:24.195196", "role": "Crewmate", "won": true, "win_rate_window": 0.8991425331943153, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 166, "timestamp": "2025-08-04T19:14:24.195197", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 167, "timestamp": "2025-08-04T19:14:24.195198", "role": "Phantom", "won": false, "win_rate_window": 0.8302528076283152, "reward": -1.0045079926650229, "episode_length": 57, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 168, "timestamp": "2025-08-04T19:14:24.195199", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 169, "timestamp": "2025-08-04T19:14:24.195200", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8509171671250395, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 170, "timestamp": "2025-08-04T19:14:24.195200", "role": "Crewmate", "won": false, "win_rate_window": 0.8589482994466351, "reward": -1.2677645332971152, "episode_length": 56, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 171, "timestamp": "2025-08-04T19:14:24.195201", "role": "Guardian Angel", "won": true, "win_rate_window": 0.45061407117919183, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 172, "timestamp": "2025-08-04T19:14:24.195202", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7849549090691473, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 173, "timestamp": "2025-08-04T19:14:24.195204", "role": "Scientist", "won": false, "win_rate_window": 0.5955980002002561, "reward": -0.8307433695397968, "episode_length": 47, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 174, "timestamp": "2025-08-04T19:14:24.195206", "role": "Crewmate", "won": true, "win_rate_window": 0.5844765314822602, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 175, "timestamp": "2025-08-04T19:14:24.195207", "role": "Tracker", "won": false, "win_rate_window": 0.6065289932965819, "reward": -0.968257009529266, "episode_length": 82, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 176, "timestamp": "2025-08-04T19:14:24.195208", "role": "Phantom", "won": true, "win_rate_window": 0.6793329028989576, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 177, "timestamp": "2025-08-04T19:14:24.195209", "role": "Tracker", "won": true, "win_rate_window": 0.6586789799185949, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 178, "timestamp": "2025-08-04T19:14:24.195210", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 92, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 179, "timestamp": "2025-08-04T19:14:24.195211", "role": "Impostor", "won": true, "win_rate_window": 0.7785963826749863, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 180, "timestamp": "2025-08-04T19:14:24.195212", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 181, "timestamp": "2025-08-04T19:14:24.195213", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 182, "timestamp": "2025-08-04T19:14:24.195214", "role": "Phantom", "won": true, "win_rate_window": 0.9466151604896328, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 183, "timestamp": "2025-08-04T19:14:24.195215", "role": "Scientist", "won": true, "win_rate_window": 0.6627534172189316, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 184, "timestamp": "2025-08-04T19:14:24.195216", "role": "Crewmate", "won": true, "win_rate_window": 0.8207825352815916, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 185, "timestamp": "2025-08-04T19:14:24.195217", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7895807545053948, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 186, "timestamp": "2025-08-04T19:14:24.195218", "role": "Engineer", "won": true, "win_rate_window": 0.8190305413876537, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 187, "timestamp": "2025-08-04T19:14:24.195219", "role": "Scientist", "won": false, "win_rate_window": 0.6882600051889873, "reward": -0.6194051385685043, "episode_length": 63, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 188, "timestamp": "2025-08-04T19:14:24.195220", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8850841660810793, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 189, "timestamp": "2025-08-04T19:14:24.195221", "role": "Phantom", "won": true, "win_rate_window": 0.7348772080564117, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 190, "timestamp": "2025-08-04T19:14:24.195222", "role": "Tracker", "won": true, "win_rate_window": 0.8508558180834206, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 191, "timestamp": "2025-08-04T19:14:24.195223", "role": "Guardian Angel", "won": false, "win_rate_window": 0.8034951234783285, "reward": -0.9882284203380703, "episode_length": 100, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 192, "timestamp": "2025-08-04T19:14:24.195224", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8231054055395035, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 193, "timestamp": "2025-08-04T19:14:24.195225", "role": "Engineer", "won": true, "win_rate_window": 0.8572418490858732, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 194, "timestamp": "2025-08-04T19:14:24.195226", "role": "Crewmate", "won": true, "win_rate_window": 0.6450620658939213, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 195, "timestamp": "2025-08-04T19:14:24.195228", "role": "Engineer", "won": true, "win_rate_window": 0.725070754314042, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 196, "timestamp": "2025-08-04T19:14:24.195230", "role": "Impostor", "won": true, "win_rate_window": 0.7173811663044922, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 197, "timestamp": "2025-08-04T19:14:24.195231", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 198, "timestamp": "2025-08-04T19:14:24.195232", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 199, "timestamp": "2025-08-04T19:14:24.195233", "role": "Engineer", "won": true, "win_rate_window": 0.760096598407563, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 200, "timestamp": "2025-08-04T19:14:24.195234", "role": "Phantom", "won": true, "win_rate_window": 0.8899075471528922, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 201, "timestamp": "2025-08-04T19:14:24.195235", "role": "Guardian Angel", "won": true, "win_rate_window": 0.849173859853428, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 202, "timestamp": "2025-08-04T19:14:24.195236", "role": "Impostor", "won": true, "win_rate_window": 0.7822979493912684, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 203, "timestamp": "2025-08-04T19:14:24.195237", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8475815880028286, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 204, "timestamp": "2025-08-04T19:14:24.195238", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 205, "timestamp": "2025-08-04T19:14:24.195239", "role": "Phantom", "won": true, "win_rate_window": 0.7052131380778296, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 206, "timestamp": "2025-08-04T19:14:24.195240", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7984551059995431, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 207, "timestamp": "2025-08-04T19:14:24.195241", "role": "Tracker", "won": true, "win_rate_window": 0.9109602680257376, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 208, "timestamp": "2025-08-04T19:14:24.195242", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 209, "timestamp": "2025-08-04T19:14:24.195243", "role": "Crewmate", "won": true, "win_rate_window": 0.625734024784125, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 210, "timestamp": "2025-08-04T19:14:24.195244", "role": "Impostor", "won": true, "win_rate_window": 0.9317211150379168, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 211, "timestamp": "2025-08-04T19:14:24.195245", "role": "Crewmate", "won": true, "win_rate_window": 0.9416124383735004, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 212, "timestamp": "2025-08-04T19:14:24.195246", "role": "Tracker", "won": true, "win_rate_window": 0.913619755172719, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 213, "timestamp": "2025-08-04T19:14:24.195247", "role": "Scientist", "won": true, "win_rate_window": 0.8490803444939935, "reward": 3.5999999999999996, "episode_length": 84, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 214, "timestamp": "2025-08-04T19:14:24.195248", "role": "Impostor", "won": true, "win_rate_window": 0.8294332123983402, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 215, "timestamp": "2025-08-04T19:14:24.195249", "role": "Tracker", "won": true, "win_rate_window": 0.6784841617173811, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 216, "timestamp": "2025-08-04T19:14:24.195251", "role": "Impostor", "won": false, "win_rate_window": 0.8317179507188404, "reward": -1.0054093448481782, "episode_length": 47, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 217, "timestamp": "2025-08-04T19:14:24.195252", "role": "Scientist", "won": true, "win_rate_window": 0.8805787817292395, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 218, "timestamp": "2025-08-04T19:14:24.195253", "role": "Shapeshifter", "won": true, "win_rate_window": 0.756506826014036, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 219, "timestamp": "2025-08-04T19:14:24.195254", "role": "Engineer", "won": true, "win_rate_window": 0.5659192376615836, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 220, "timestamp": "2025-08-04T19:14:24.195255", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9212365559592111, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 221, "timestamp": "2025-08-04T19:14:24.195256", "role": "Crewmate", "won": true, "win_rate_window": 0.9303779647579975, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 222, "timestamp": "2025-08-04T19:14:24.195257", "role": "Scientist", "won": false, "win_rate_window": 0.95, "reward": -1.1151336413907946, "episode_length": 44, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 223, "timestamp": "2025-08-04T19:14:24.195258", "role": "Scientist", "won": true, "win_rate_window": 0.8201666493410243, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 224, "timestamp": "2025-08-04T19:14:24.195260", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 225, "timestamp": "2025-08-04T19:14:24.195261", "role": "Phantom", "won": true, "win_rate_window": 0.905163667298907, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 226, "timestamp": "2025-08-04T19:14:24.195262", "role": "Scientist", "won": false, "win_rate_window": 0.8815375325693743, "reward": 0.3321340915066684, "episode_length": 92, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 227, "timestamp": "2025-08-04T19:14:24.195263", "role": "Crewmate", "won": true, "win_rate_window": 0.9448174150626046, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 228, "timestamp": "2025-08-04T19:14:24.195264", "role": "Scientist", "won": true, "win_rate_window": 0.8693549135235552, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 229, "timestamp": "2025-08-04T19:14:24.195265", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 230, "timestamp": "2025-08-04T19:14:24.195266", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8123614217278275, "reward": -0.9200577867375332, "episode_length": 81, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 231, "timestamp": "2025-08-04T19:14:24.195267", "role": "Scientist", "won": true, "win_rate_window": 0.835512038543766, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 232, "timestamp": "2025-08-04T19:14:24.195268", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 233, "timestamp": "2025-08-04T19:14:24.195269", "role": "Tracker", "won": true, "win_rate_window": 0.7014334639664619, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 234, "timestamp": "2025-08-04T19:14:24.195270", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 235, "timestamp": "2025-08-04T19:14:24.195272", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 236, "timestamp": "2025-08-04T19:14:24.195273", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 237, "timestamp": "2025-08-04T19:14:24.195274", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 238, "timestamp": "2025-08-04T19:14:24.195275", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 239, "timestamp": "2025-08-04T19:14:24.195276", "role": "Engineer", "won": false, "win_rate_window": 0.42379431572021337, "reward": -1.2765866843372609, "episode_length": 73, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 240, "timestamp": "2025-08-04T19:14:24.195277", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6016654272583193, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 241, "timestamp": "2025-08-04T19:14:24.195278", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 242, "timestamp": "2025-08-04T19:14:24.195279", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8776770432740334, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 243, "timestamp": "2025-08-04T19:14:24.195280", "role": "Phantom", "won": true, "win_rate_window": 0.9331643635665952, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 244, "timestamp": "2025-08-04T19:14:24.195281", "role": "Impostor", "won": true, "win_rate_window": 0.9086982152676085, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 245, "timestamp": "2025-08-04T19:14:24.195282", "role": "Impostor", "won": true, "win_rate_window": 0.6082639837294833, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 246, "timestamp": "2025-08-04T19:14:24.195283", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8721668099093768, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 247, "timestamp": "2025-08-04T19:14:24.195284", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 248, "timestamp": "2025-08-04T19:14:24.195285", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 249, "timestamp": "2025-08-04T19:14:24.195286", "role": "Shapeshifter", "won": true, "win_rate_window": 0.715016508815522, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 250, "timestamp": "2025-08-04T19:14:24.195287", "role": "Crewmate", "won": true, "win_rate_window": 0.9495718901272364, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 251, "timestamp": "2025-08-04T19:14:24.195288", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8318438938604413, "reward": -0.8888764503958966, "episode_length": 52, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 252, "timestamp": "2025-08-04T19:14:24.195289", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 253, "timestamp": "2025-08-04T19:14:24.195290", "role": "Impostor", "won": false, "win_rate_window": 0.7424149498079976, "reward": 0.04637084411082206, "episode_length": 82, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 254, "timestamp": "2025-08-04T19:14:24.195291", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7259515287886963, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 255, "timestamp": "2025-08-04T19:14:24.195293", "role": "Scientist", "won": true, "win_rate_window": 0.7276301287121187, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 256, "timestamp": "2025-08-04T19:14:24.195294", "role": "Impostor", "won": false, "win_rate_window": 0.857695813662724, "reward": -1.455711442722238, "episode_length": 112, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 257, "timestamp": "2025-08-04T19:14:24.195295", "role": "Impostor", "won": true, "win_rate_window": 0.9190463702785396, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 258, "timestamp": "2025-08-04T19:14:24.195296", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7455667505384446, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 259, "timestamp": "2025-08-04T19:14:24.195298", "role": "Tracker", "won": true, "win_rate_window": 0.9449996885477934, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 260, "timestamp": "2025-08-04T19:14:24.195299", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8212828605868104, "reward": 3.5999999999999996, "episode_length": 84, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 261, "timestamp": "2025-08-04T19:14:24.195300", "role": "Guardian Angel", "won": true, "win_rate_window": 0.6961002157296271, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 262, "timestamp": "2025-08-04T19:14:24.195301", "role": "Scientist", "won": true, "win_rate_window": 0.5473034957034522, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 263, "timestamp": "2025-08-04T19:14:24.195302", "role": "Tracker", "won": true, "win_rate_window": 0.6206340363890831, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 264, "timestamp": "2025-08-04T19:14:24.195303", "role": "Phantom", "won": true, "win_rate_window": 0.770772157569519, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 265, "timestamp": "2025-08-04T19:14:24.195304", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 266, "timestamp": "2025-08-04T19:14:24.195305", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8181927251696077, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 267, "timestamp": "2025-08-04T19:14:24.195306", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7149397812760054, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 268, "timestamp": "2025-08-04T19:14:24.195307", "role": "Scientist", "won": true, "win_rate_window": 0.7774234153480065, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 269, "timestamp": "2025-08-04T19:14:24.195308", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 270, "timestamp": "2025-08-04T19:14:24.195309", "role": "Engineer", "won": true, "win_rate_window": 0.8193543971657395, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 271, "timestamp": "2025-08-04T19:14:24.195310", "role": "Scientist", "won": true, "win_rate_window": 0.5023615874962637, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 272, "timestamp": "2025-08-04T19:14:24.195311", "role": "Crewmate", "won": true, "win_rate_window": 0.6315066240700159, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 273, "timestamp": "2025-08-04T19:14:24.195312", "role": "Crewmate", "won": true, "win_rate_window": 0.931782133100417, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 274, "timestamp": "2025-08-04T19:14:24.195314", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 275, "timestamp": "2025-08-04T19:14:24.195315", "role": "Crewmate", "won": true, "win_rate_window": 0.6552878100589162, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 276, "timestamp": "2025-08-04T19:14:24.195317", "role": "Engineer", "won": true, "win_rate_window": 0.7595742610603494, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 277, "timestamp": "2025-08-04T19:14:24.195318", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 278, "timestamp": "2025-08-04T19:14:24.195319", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 279, "timestamp": "2025-08-04T19:14:24.195320", "role": "Crewmate", "won": true, "win_rate_window": 0.845828569228328, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 280, "timestamp": "2025-08-04T19:14:24.195321", "role": "Scientist", "won": true, "win_rate_window": 0.8781406978272085, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 281, "timestamp": "2025-08-04T19:14:24.195322", "role": "Crewmate", "won": true, "win_rate_window": 0.9253682599196829, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 282, "timestamp": "2025-08-04T19:14:24.195323", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 283, "timestamp": "2025-08-04T19:14:24.195324", "role": "Guardian Angel", "won": true, "win_rate_window": 0.6427727600062163, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 284, "timestamp": "2025-08-04T19:14:24.195325", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 285, "timestamp": "2025-08-04T19:14:24.195326", "role": "Tracker", "won": true, "win_rate_window": 0.5120533952113198, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 286, "timestamp": "2025-08-04T19:14:24.195327", "role": "Tracker", "won": false, "win_rate_window": 0.8538036599578298, "reward": -1.4537303451151387, "episode_length": 111, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 287, "timestamp": "2025-08-04T19:14:24.195328", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7100415453045906, "reward": 3.5999999999999996, "episode_length": 50, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 288, "timestamp": "2025-08-04T19:14:24.195329", "role": "Shapeshifter", "won": false, "win_rate_window": 0.95, "reward": -2.0163449442326495, "episode_length": 117, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 289, "timestamp": "2025-08-04T19:14:24.195330", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7933342120138379, "reward": -0.48460203522493756, "episode_length": 45, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 290, "timestamp": "2025-08-04T19:14:24.195331", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7782421486504858, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 291, "timestamp": "2025-08-04T19:14:24.195332", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7995909492405514, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 292, "timestamp": "2025-08-04T19:14:24.195333", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 293, "timestamp": "2025-08-04T19:14:24.195334", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9106813414758267, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 294, "timestamp": "2025-08-04T19:14:24.195336", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 295, "timestamp": "2025-08-04T19:14:24.195337", "role": "Impostor", "won": true, "win_rate_window": 0.7484378240143508, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 296, "timestamp": "2025-08-04T19:14:24.195338", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8946202872765271, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 297, "timestamp": "2025-08-04T19:14:24.195339", "role": "Engineer", "won": false, "win_rate_window": 0.7841110027313913, "reward": -1.5257483059499766, "episode_length": 90, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 298, "timestamp": "2025-08-04T19:14:24.195340", "role": "Tracker", "won": true, "win_rate_window": 0.7477145702432487, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 299, "timestamp": "2025-08-04T19:14:24.195340", "role": "Impostor", "won": true, "win_rate_window": 0.8287047119002651, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 300, "timestamp": "2025-08-04T19:14:24.195341", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 301, "timestamp": "2025-08-04T19:14:24.195342", "role": "Impostor", "won": true, "win_rate_window": 0.8038487032458723, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 302, "timestamp": "2025-08-04T19:14:24.195343", "role": "Impostor", "won": true, "win_rate_window": 0.902281589592386, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 303, "timestamp": "2025-08-04T19:14:24.195344", "role": "Impostor", "won": true, "win_rate_window": 0.8877922714229001, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 304, "timestamp": "2025-08-04T19:14:24.195345", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8728361971170845, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 305, "timestamp": "2025-08-04T19:14:24.195346", "role": "Tracker", "won": false, "win_rate_window": 0.95, "reward": -1.2290319541141672, "episode_length": 82, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 306, "timestamp": "2025-08-04T19:14:24.195347", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 307, "timestamp": "2025-08-04T19:14:24.195348", "role": "Tracker", "won": true, "win_rate_window": 0.7680643808360212, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 308, "timestamp": "2025-08-04T19:14:24.195349", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 309, "timestamp": "2025-08-04T19:14:24.195351", "role": "Guardian Angel", "won": true, "win_rate_window": 0.5248183292915582, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 310, "timestamp": "2025-08-04T19:14:24.195352", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 311, "timestamp": "2025-08-04T19:14:24.195353", "role": "Shapeshifter", "won": false, "win_rate_window": 0.870416823740194, "reward": -1.5276839314028738, "episode_length": 61, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 312, "timestamp": "2025-08-04T19:14:24.195354", "role": "Tracker", "won": false, "win_rate_window": 0.95, "reward": -1.2747437411169071, "episode_length": 111, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 313, "timestamp": "2025-08-04T19:14:24.195355", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 314, "timestamp": "2025-08-04T19:14:24.195356", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 315, "timestamp": "2025-08-04T19:14:24.195357", "role": "Impostor", "won": false, "win_rate_window": 0.6430392651980182, "reward": -1.8341884305648266, "episode_length": 47, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 316, "timestamp": "2025-08-04T19:14:24.195358", "role": "Phantom", "won": true, "win_rate_window": 0.946485811060644, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 317, "timestamp": "2025-08-04T19:14:24.195359", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 318, "timestamp": "2025-08-04T19:14:24.195360", "role": "Crewmate", "won": true, "win_rate_window": 0.9034264160113541, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 319, "timestamp": "2025-08-04T19:14:24.195361", "role": "Phantom", "won": true, "win_rate_window": 0.8642078478072768, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 320, "timestamp": "2025-08-04T19:14:24.195362", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8839860967231299, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 321, "timestamp": "2025-08-04T19:14:24.195363", "role": "Crewmate", "won": false, "win_rate_window": 0.7214325156451936, "reward": -1.4052731852520814, "episode_length": 105, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 322, "timestamp": "2025-08-04T19:14:24.195364", "role": "Tracker", "won": true, "win_rate_window": 0.7885539532935683, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 323, "timestamp": "2025-08-04T19:14:24.195366", "role": "Crewmate", "won": true, "win_rate_window": 0.5680688398492197, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 324, "timestamp": "2025-08-04T19:14:24.195367", "role": "Crewmate", "won": true, "win_rate_window": 0.9373404191330805, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 325, "timestamp": "2025-08-04T19:14:24.195368", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 326, "timestamp": "2025-08-04T19:14:24.195370", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 327, "timestamp": "2025-08-04T19:14:24.195371", "role": "Impostor", "won": false, "win_rate_window": 0.4167293226058091, "reward": -1.7239976496435743, "episode_length": 91, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 328, "timestamp": "2025-08-04T19:14:24.195372", "role": "Crewmate", "won": true, "win_rate_window": 0.7747721240224449, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 329, "timestamp": "2025-08-04T19:14:24.195373", "role": "Impostor", "won": true, "win_rate_window": 0.7219989955716584, "reward": 3.5999999999999996, "episode_length": 93, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 330, "timestamp": "2025-08-04T19:14:24.195374", "role": "Engineer", "won": true, "win_rate_window": 0.7775571817625732, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 331, "timestamp": "2025-08-04T19:14:24.195375", "role": "Engineer", "won": true, "win_rate_window": 0.8667714532447259, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 332, "timestamp": "2025-08-04T19:14:24.195376", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 333, "timestamp": "2025-08-04T19:14:24.195378", "role": "Engineer", "won": true, "win_rate_window": 0.8322870175544238, "reward": 3.5999999999999996, "episode_length": 50, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 334, "timestamp": "2025-08-04T19:14:24.195379", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8144432705705404, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 335, "timestamp": "2025-08-04T19:14:24.195380", "role": "Shapeshifter", "won": false, "win_rate_window": 0.483547356369248, "reward": -1.098784592648793, "episode_length": 108, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 336, "timestamp": "2025-08-04T19:14:24.195381", "role": "Engineer", "won": true, "win_rate_window": 0.5999257693835551, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 337, "timestamp": "2025-08-04T19:14:24.195382", "role": "Impostor", "won": true, "win_rate_window": 0.6281321761965267, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 338, "timestamp": "2025-08-04T19:14:24.195383", "role": "Phantom", "won": false, "win_rate_window": 0.95, "reward": -1.699403407799922, "episode_length": 118, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 339, "timestamp": "2025-08-04T19:14:24.195384", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 340, "timestamp": "2025-08-04T19:14:24.195385", "role": "Phantom", "won": true, "win_rate_window": 0.9157259828152193, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 341, "timestamp": "2025-08-04T19:14:24.195386", "role": "Engineer", "won": false, "win_rate_window": 0.6881661903901952, "reward": -2.2016816568195408, "episode_length": 112, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 342, "timestamp": "2025-08-04T19:14:24.195387", "role": "Tracker", "won": true, "win_rate_window": 0.7184897731251759, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 343, "timestamp": "2025-08-04T19:14:24.195388", "role": "Crewmate", "won": false, "win_rate_window": 0.6843404492944927, "reward": -2.3389799267310587, "episode_length": 85, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 344, "timestamp": "2025-08-04T19:14:24.195389", "role": "Engineer", "won": true, "win_rate_window": 0.8091137725581813, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 345, "timestamp": "2025-08-04T19:14:24.195390", "role": "Impostor", "won": true, "win_rate_window": 0.798119337889969, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 346, "timestamp": "2025-08-04T19:14:24.195391", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 347, "timestamp": "2025-08-04T19:14:24.195392", "role": "Tracker", "won": true, "win_rate_window": 0.7273827716740531, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 348, "timestamp": "2025-08-04T19:14:24.195393", "role": "Phantom", "won": true, "win_rate_window": 0.8114590779517727, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 349, "timestamp": "2025-08-04T19:14:24.195394", "role": "Impostor", "won": true, "win_rate_window": 0.7778832091512481, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 350, "timestamp": "2025-08-04T19:14:24.195395", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 351, "timestamp": "2025-08-04T19:14:24.195396", "role": "Phantom", "won": true, "win_rate_window": 0.7767621721411135, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 352, "timestamp": "2025-08-04T19:14:24.195397", "role": "Engineer", "won": true, "win_rate_window": 0.9318481738190377, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 353, "timestamp": "2025-08-04T19:14:24.195399", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 354, "timestamp": "2025-08-04T19:14:24.195400", "role": "Scientist", "won": true, "win_rate_window": 0.839819587962796, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 355, "timestamp": "2025-08-04T19:14:24.195401", "role": "Impostor", "won": false, "win_rate_window": 0.5846059234881633, "reward": -0.8457437264152902, "episode_length": 101, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 356, "timestamp": "2025-08-04T19:14:24.195402", "role": "Phantom", "won": false, "win_rate_window": 0.6703000329086769, "reward": -1.1290553152725875, "episode_length": 52, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 357, "timestamp": "2025-08-04T19:14:24.195403", "role": "Phantom", "won": true, "win_rate_window": 0.7635411265552869, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 358, "timestamp": "2025-08-04T19:14:24.195404", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 359, "timestamp": "2025-08-04T19:14:24.195405", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8501063113794955, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 360, "timestamp": "2025-08-04T19:14:24.195406", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 361, "timestamp": "2025-08-04T19:14:24.195407", "role": "Scientist", "won": true, "win_rate_window": 0.8699336002485522, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 362, "timestamp": "2025-08-04T19:14:24.195408", "role": "Scientist", "won": true, "win_rate_window": 0.8481360416137813, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 363, "timestamp": "2025-08-04T19:14:24.195409", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 364, "timestamp": "2025-08-04T19:14:24.195410", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 365, "timestamp": "2025-08-04T19:14:24.195411", "role": "Engineer", "won": true, "win_rate_window": 0.8055454064311299, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 366, "timestamp": "2025-08-04T19:14:24.195412", "role": "Scientist", "won": false, "win_rate_window": 0.8182012372679501, "reward": -1.08699353605867, "episode_length": 42, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 367, "timestamp": "2025-08-04T19:14:24.195413", "role": "Tracker", "won": true, "win_rate_window": 0.8172755132075928, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 368, "timestamp": "2025-08-04T19:14:24.195414", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 369, "timestamp": "2025-08-04T19:14:24.195415", "role": "Tracker", "won": true, "win_rate_window": 0.9009464497155552, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 370, "timestamp": "2025-08-04T19:14:24.195416", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 371, "timestamp": "2025-08-04T19:14:24.195417", "role": "Impostor", "won": true, "win_rate_window": 0.885517238403445, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 372, "timestamp": "2025-08-04T19:14:24.195419", "role": "Guardian Angel", "won": false, "win_rate_window": 0.7185427597181876, "reward": -1.9431802104937783, "episode_length": 60, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 373, "timestamp": "2025-08-04T19:14:24.195420", "role": "Phantom", "won": true, "win_rate_window": 0.8100224763621485, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 374, "timestamp": "2025-08-04T19:14:24.195421", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8317426028840761, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 375, "timestamp": "2025-08-04T19:14:24.195422", "role": "Shapeshifter", "won": true, "win_rate_window": 0.808897526537179, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 376, "timestamp": "2025-08-04T19:14:24.195423", "role": "Engineer", "won": true, "win_rate_window": 0.7257229787045859, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 377, "timestamp": "2025-08-04T19:14:24.195425", "role": "Tracker", "won": true, "win_rate_window": 0.6814798919245639, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 378, "timestamp": "2025-08-04T19:14:24.195426", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8054947075438297, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 379, "timestamp": "2025-08-04T19:14:24.195427", "role": "Impostor", "won": false, "win_rate_window": 0.7526705770614481, "reward": -1.5617599151320303, "episode_length": 107, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 380, "timestamp": "2025-08-04T19:14:24.195428", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 381, "timestamp": "2025-08-04T19:14:24.195429", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 382, "timestamp": "2025-08-04T19:14:24.195430", "role": "Tracker", "won": true, "win_rate_window": 0.7360420747981402, "reward": 3.5999999999999996, "episode_length": 84, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 383, "timestamp": "2025-08-04T19:14:24.195431", "role": "Engineer", "won": false, "win_rate_window": 0.7607066339359065, "reward": -0.2784927775069971, "episode_length": 84, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 384, "timestamp": "2025-08-04T19:14:24.195432", "role": "Tracker", "won": true, "win_rate_window": 0.908241137283051, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 385, "timestamp": "2025-08-04T19:14:24.195432", "role": "Phantom", "won": true, "win_rate_window": 0.8192290562308648, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 386, "timestamp": "2025-08-04T19:14:24.195434", "role": "Impostor", "won": true, "win_rate_window": 0.9153441119925123, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 387, "timestamp": "2025-08-04T19:14:24.195435", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6132468731721419, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 388, "timestamp": "2025-08-04T19:14:24.195436", "role": "Phantom", "won": true, "win_rate_window": 0.8431426127922098, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 389, "timestamp": "2025-08-04T19:14:24.195437", "role": "Impostor", "won": false, "win_rate_window": 0.575781974258704, "reward": 0.06462432343278905, "episode_length": 107, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 390, "timestamp": "2025-08-04T19:14:24.195438", "role": "Crewmate", "won": true, "win_rate_window": 0.884289240187416, "reward": 3.5999999999999996, "episode_length": 50, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 391, "timestamp": "2025-08-04T19:14:24.195440", "role": "Phantom", "won": true, "win_rate_window": 0.8069558221687541, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 392, "timestamp": "2025-08-04T19:14:24.195441", "role": "Scientist", "won": true, "win_rate_window": 0.8649185696723443, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 393, "timestamp": "2025-08-04T19:14:24.195442", "role": "Scientist", "won": true, "win_rate_window": 0.8209557063816557, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 61, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 394, "timestamp": "2025-08-04T19:14:24.195443", "role": "Engineer", "won": true, "win_rate_window": 0.8785163496415345, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 395, "timestamp": "2025-08-04T19:14:24.195444", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 396, "timestamp": "2025-08-04T19:14:24.195445", "role": "Tracker", "won": false, "win_rate_window": 0.7839346877871942, "reward": -0.8809972886659468, "episode_length": 62, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 397, "timestamp": "2025-08-04T19:14:24.195446", "role": "Impostor", "won": false, "win_rate_window": 0.7147409329496145, "reward": -1.1533930507345416, "episode_length": 87, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 398, "timestamp": "2025-08-04T19:14:24.195447", "role": "Impostor", "won": true, "win_rate_window": 0.8545354180061547, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 399, "timestamp": "2025-08-04T19:14:24.195448", "role": "Phantom", "won": false, "win_rate_window": 0.7748259564973076, "reward": -1.0382669341251103, "episode_length": 71, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 400, "timestamp": "2025-08-04T19:14:24.195449", "role": "Impostor", "won": false, "win_rate_window": 0.9431887532795346, "reward": -1.1145178956998603, "episode_length": 76, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 401, "timestamp": "2025-08-04T19:14:24.195450", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 402, "timestamp": "2025-08-04T19:14:24.195451", "role": "Impostor", "won": false, "win_rate_window": 0.4853365595148746, "reward": -0.4518728020121873, "episode_length": 63, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 403, "timestamp": "2025-08-04T19:14:24.195452", "role": "Shapeshifter", "won": true, "win_rate_window": 0.870684952725361, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 404, "timestamp": "2025-08-04T19:14:24.195453", "role": "Impostor", "won": true, "win_rate_window": 0.7379891407980236, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 405, "timestamp": "2025-08-04T19:14:24.195454", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7563721019302297, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 406, "timestamp": "2025-08-04T19:14:24.195455", "role": "Crewmate", "won": true, "win_rate_window": 0.6348104443131264, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 407, "timestamp": "2025-08-04T19:14:24.195456", "role": "Crewmate", "won": true, "win_rate_window": 0.9049784896208847, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 408, "timestamp": "2025-08-04T19:14:24.195457", "role": "Tracker", "won": false, "win_rate_window": 0.7911544207629286, "reward": -1.8284884034087332, "episode_length": 62, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 409, "timestamp": "2025-08-04T19:14:24.195458", "role": "Phantom", "won": false, "win_rate_window": 0.7599822035170436, "reward": -1.5718277398552367, "episode_length": 48, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 410, "timestamp": "2025-08-04T19:14:24.195459", "role": "Engineer", "won": true, "win_rate_window": 0.8099261134935699, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 411, "timestamp": "2025-08-04T19:14:24.195461", "role": "Impostor", "won": true, "win_rate_window": 0.8180090649017272, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 412, "timestamp": "2025-08-04T19:14:24.195462", "role": "Impostor", "won": true, "win_rate_window": 0.7936927731012777, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 413, "timestamp": "2025-08-04T19:14:24.195463", "role": "Engineer", "won": true, "win_rate_window": 0.8470551047560337, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 414, "timestamp": "2025-08-04T19:14:24.195464", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 415, "timestamp": "2025-08-04T19:14:24.195465", "role": "Tracker", "won": true, "win_rate_window": 0.861634731195419, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 416, "timestamp": "2025-08-04T19:14:24.195466", "role": "Engineer", "won": true, "win_rate_window": 0.7382272438343902, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 417, "timestamp": "2025-08-04T19:14:24.195467", "role": "Phantom", "won": true, "win_rate_window": 0.9203641580302415, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 418, "timestamp": "2025-08-04T19:14:24.195468", "role": "Tracker", "won": false, "win_rate_window": 0.642229175315696, "reward": -0.4505703547542248, "episode_length": 115, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 419, "timestamp": "2025-08-04T19:14:24.195469", "role": "Scientist", "won": false, "win_rate_window": 0.7389975184395541, "reward": -0.5856309587021425, "episode_length": 86, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 420, "timestamp": "2025-08-04T19:14:24.195470", "role": "Guardian Angel", "won": false, "win_rate_window": 0.8846691362218584, "reward": -1.2383817125106742, "episode_length": 57, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 421, "timestamp": "2025-08-04T19:14:24.195471", "role": "Engineer", "won": true, "win_rate_window": 0.754982449532392, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 422, "timestamp": "2025-08-04T19:14:24.195472", "role": "Scientist", "won": true, "win_rate_window": 0.9277483193948531, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 423, "timestamp": "2025-08-04T19:14:24.195473", "role": "Crewmate", "won": true, "win_rate_window": 0.9089013061252136, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 424, "timestamp": "2025-08-04T19:14:24.195474", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8240336691130913, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 425, "timestamp": "2025-08-04T19:14:24.195475", "role": "Crewmate", "won": true, "win_rate_window": 0.7886519065841138, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 426, "timestamp": "2025-08-04T19:14:24.195476", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6545160175048736, "reward": 3.5999999999999996, "episode_length": 92, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 427, "timestamp": "2025-08-04T19:14:24.195477", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 428, "timestamp": "2025-08-04T19:14:24.195479", "role": "Engineer", "won": true, "win_rate_window": 0.8824961304137859, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 61, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 429, "timestamp": "2025-08-04T19:14:24.195480", "role": "Impostor", "won": true, "win_rate_window": 0.8767250437067687, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 430, "timestamp": "2025-08-04T19:14:24.195481", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 431, "timestamp": "2025-08-04T19:14:24.195482", "role": "Tracker", "won": true, "win_rate_window": 0.7628227433309671, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 432, "timestamp": "2025-08-04T19:14:24.195483", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 433, "timestamp": "2025-08-04T19:14:24.195484", "role": "Phantom", "won": false, "win_rate_window": 0.7047257760175157, "reward": -0.9497553720975932, "episode_length": 120, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 434, "timestamp": "2025-08-04T19:14:24.195485", "role": "Engineer", "won": false, "win_rate_window": 0.8608387167176863, "reward": -1.503905894873133, "episode_length": 114, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 435, "timestamp": "2025-08-04T19:14:24.195486", "role": "Crewmate", "won": true, "win_rate_window": 0.7638210962282154, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 436, "timestamp": "2025-08-04T19:14:24.195487", "role": "Crewmate", "won": true, "win_rate_window": 0.70350445879982, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 437, "timestamp": "2025-08-04T19:14:24.195488", "role": "Engineer", "won": true, "win_rate_window": 0.9224381481504633, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 438, "timestamp": "2025-08-04T19:14:24.195489", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9153640346770809, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 439, "timestamp": "2025-08-04T19:14:24.195490", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 440, "timestamp": "2025-08-04T19:14:24.195491", "role": "Phantom", "won": false, "win_rate_window": 0.547867723568163, "reward": -1.483685798607048, "episode_length": 63, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 441, "timestamp": "2025-08-04T19:14:24.195492", "role": "Impostor", "won": false, "win_rate_window": 0.773485819377986, "reward": -1.4563753126773826, "episode_length": 84, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 442, "timestamp": "2025-08-04T19:14:24.195493", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 443, "timestamp": "2025-08-04T19:14:24.195494", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 444, "timestamp": "2025-08-04T19:14:24.195495", "role": "Tracker", "won": false, "win_rate_window": 0.6426539219720251, "reward": -1.7696703038849773, "episode_length": 105, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 445, "timestamp": "2025-08-04T19:14:24.195496", "role": "Impostor", "won": true, "win_rate_window": 0.8056872526110108, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 446, "timestamp": "2025-08-04T19:14:24.195497", "role": "Tracker", "won": true, "win_rate_window": 0.7589640024360097, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 447, "timestamp": "2025-08-04T19:14:24.195498", "role": "Phantom", "won": true, "win_rate_window": 0.7526828013658109, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 448, "timestamp": "2025-08-04T19:14:24.195499", "role": "Phantom", "won": true, "win_rate_window": 0.67100738978782, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 449, "timestamp": "2025-08-04T19:14:24.195500", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 450, "timestamp": "2025-08-04T19:14:24.195503", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6401541791394731, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 451, "timestamp": "2025-08-04T19:14:24.195504", "role": "Crewmate", "won": true, "win_rate_window": 0.8688672012858847, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 452, "timestamp": "2025-08-04T19:14:24.195505", "role": "Shapeshifter", "won": false, "win_rate_window": 0.6197098019598454, "reward": -1.4247666115780784, "episode_length": 81, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 453, "timestamp": "2025-08-04T19:14:24.195506", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 454, "timestamp": "2025-08-04T19:14:24.195507", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6609931653862162, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 455, "timestamp": "2025-08-04T19:14:24.195508", "role": "Phantom", "won": true, "win_rate_window": 0.8685986645685903, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 456, "timestamp": "2025-08-04T19:14:24.195509", "role": "Impostor", "won": true, "win_rate_window": 0.8615516018502724, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 457, "timestamp": "2025-08-04T19:14:24.195510", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 458, "timestamp": "2025-08-04T19:14:24.195511", "role": "Tracker", "won": true, "win_rate_window": 0.6785684795209982, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 459, "timestamp": "2025-08-04T19:14:24.195512", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 460, "timestamp": "2025-08-04T19:14:24.195513", "role": "Tracker", "won": true, "win_rate_window": 0.794505576812496, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 461, "timestamp": "2025-08-04T19:14:24.195514", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 462, "timestamp": "2025-08-04T19:14:24.195515", "role": "Phantom", "won": true, "win_rate_window": 0.8479301687546539, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 463, "timestamp": "2025-08-04T19:14:24.195516", "role": "Engineer", "won": true, "win_rate_window": 0.743100096289809, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 464, "timestamp": "2025-08-04T19:14:24.195517", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 465, "timestamp": "2025-08-04T19:14:24.195603", "role": "Tracker", "won": false, "win_rate_window": 0.8923941715676731, "reward": -1.0398929744815362, "episode_length": 58, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 466, "timestamp": "2025-08-04T19:14:24.195606", "role": "Impostor", "won": true, "win_rate_window": 0.9189461398199332, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 467, "timestamp": "2025-08-04T19:14:24.195607", "role": "Guardian Angel", "won": true, "win_rate_window": 0.774310512436554, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 468, "timestamp": "2025-08-04T19:14:24.195608", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9351669578445976, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 469, "timestamp": "2025-08-04T19:14:24.195612", "role": "Phantom", "won": true, "win_rate_window": 0.8206572993312673, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 470, "timestamp": "2025-08-04T19:14:24.195613", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 471, "timestamp": "2025-08-04T19:14:24.195614", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 472, "timestamp": "2025-08-04T19:14:24.195625", "role": "Impostor", "won": true, "win_rate_window": 0.9151746582006791, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 473, "timestamp": "2025-08-04T19:14:24.195627", "role": "Tracker", "won": false, "win_rate_window": 0.6445036952619101, "reward": -1.9515330988986226, "episode_length": 110, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 474, "timestamp": "2025-08-04T19:14:24.195628", "role": "Impostor", "won": true, "win_rate_window": 0.8623301902599636, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 475, "timestamp": "2025-08-04T19:14:24.195629", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7125010446885519, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 476, "timestamp": "2025-08-04T19:14:24.195630", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8517703277213342, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 477, "timestamp": "2025-08-04T19:14:24.195631", "role": "Crewmate", "won": true, "win_rate_window": 0.9482527029195681, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 478, "timestamp": "2025-08-04T19:14:24.195632", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7022030650525686, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 479, "timestamp": "2025-08-04T19:14:24.195634", "role": "Engineer", "won": false, "win_rate_window": 0.47218104139959344, "reward": -0.25399709731347714, "episode_length": 74, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 480, "timestamp": "2025-08-04T19:14:24.195636", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 481, "timestamp": "2025-08-04T19:14:24.195637", "role": "Crewmate", "won": true, "win_rate_window": 0.8587313507690691, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 482, "timestamp": "2025-08-04T19:14:24.195638", "role": "Impostor", "won": true, "win_rate_window": 0.7936035969644675, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 483, "timestamp": "2025-08-04T19:14:24.195639", "role": "Phantom", "won": true, "win_rate_window": 0.6981572249733226, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 484, "timestamp": "2025-08-04T19:14:24.195640", "role": "Crewmate", "won": false, "win_rate_window": 0.690112089529537, "reward": -1.1020639758729043, "episode_length": 79, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 485, "timestamp": "2025-08-04T19:14:24.195641", "role": "Engineer", "won": true, "win_rate_window": 0.6435963312838812, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 486, "timestamp": "2025-08-04T19:14:24.195642", "role": "Tracker", "won": true, "win_rate_window": 0.6693196784748638, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 487, "timestamp": "2025-08-04T19:14:24.195643", "role": "Impostor", "won": true, "win_rate_window": 0.8885064869762627, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 488, "timestamp": "2025-08-04T19:14:24.195644", "role": "Scientist", "won": true, "win_rate_window": 0.8509761100860612, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 489, "timestamp": "2025-08-04T19:14:24.195646", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 490, "timestamp": "2025-08-04T19:14:24.195647", "role": "Engineer", "won": true, "win_rate_window": 0.8782581738040138, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 491, "timestamp": "2025-08-04T19:14:24.195648", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8451626642742079, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 492, "timestamp": "2025-08-04T19:14:24.195649", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 493, "timestamp": "2025-08-04T19:14:24.195650", "role": "Engineer", "won": true, "win_rate_window": 0.7999893936465984, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 494, "timestamp": "2025-08-04T19:14:24.195651", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 495, "timestamp": "2025-08-04T19:14:24.195652", "role": "Scientist", "won": true, "win_rate_window": 0.8232727078024226, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 496, "timestamp": "2025-08-04T19:14:24.195653", "role": "Engineer", "won": true, "win_rate_window": 0.7793583771090171, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 497, "timestamp": "2025-08-04T19:14:24.195654", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 498, "timestamp": "2025-08-04T19:14:24.195655", "role": "Crewmate", "won": true, "win_rate_window": 0.7486585343557667, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 499, "timestamp": "2025-08-04T19:14:24.195656", "role": "Phantom", "won": false, "win_rate_window": 0.6729979060119995, "reward": -0.10383989154734823, "episode_length": 106, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 500, "timestamp": "2025-08-04T19:14:24.195657", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8482735577259496, "reward": 3.5999999999999996, "episode_length": 50, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 501, "timestamp": "2025-08-04T19:14:24.195658", "role": "Scientist", "won": true, "win_rate_window": 0.8903278818876487, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 502, "timestamp": "2025-08-04T19:14:24.195659", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 503, "timestamp": "2025-08-04T19:14:24.195660", "role": "Crewmate", "won": true, "win_rate_window": 0.6332381461776998, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 504, "timestamp": "2025-08-04T19:14:24.195661", "role": "Scientist", "won": true, "win_rate_window": 0.8190211804597767, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 505, "timestamp": "2025-08-04T19:14:24.195695", "role": "Engineer", "won": true, "win_rate_window": 0.4921247306941123, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 506, "timestamp": "2025-08-04T19:14:24.195698", "role": "Phantom", "won": true, "win_rate_window": 0.9142902626842151, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 507, "timestamp": "2025-08-04T19:14:24.195699", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6465248970608228, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 508, "timestamp": "2025-08-04T19:14:24.195701", "role": "Engineer", "won": true, "win_rate_window": 0.8502726284890035, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 509, "timestamp": "2025-08-04T19:14:24.195702", "role": "Impostor", "won": true, "win_rate_window": 0.9378998017667239, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 510, "timestamp": "2025-08-04T19:14:24.195703", "role": "Impostor", "won": false, "win_rate_window": 0.6391173928001279, "reward": -1.6323035925224005, "episode_length": 90, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 511, "timestamp": "2025-08-04T19:14:24.195705", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 512, "timestamp": "2025-08-04T19:14:24.195706", "role": "Engineer", "won": false, "win_rate_window": 0.7113273276713036, "reward": -0.5116430068928641, "episode_length": 62, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 513, "timestamp": "2025-08-04T19:14:24.195707", "role": "Shapeshifter", "won": true, "win_rate_window": 0.688650886557918, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 514, "timestamp": "2025-08-04T19:14:24.195708", "role": "Scientist", "won": false, "win_rate_window": 0.8251784831327741, "reward": -0.8604704411549507, "episode_length": 65, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 515, "timestamp": "2025-08-04T19:14:24.195710", "role": "Impostor", "won": true, "win_rate_window": 0.702721376393741, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 516, "timestamp": "2025-08-04T19:14:24.195711", "role": "Impostor", "won": true, "win_rate_window": 0.7778512413349717, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 517, "timestamp": "2025-08-04T19:14:24.195712", "role": "Crewmate", "won": true, "win_rate_window": 0.7921782035379804, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 518, "timestamp": "2025-08-04T19:14:24.195713", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8176579288077661, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 519, "timestamp": "2025-08-04T19:14:24.195714", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6061005894272072, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 520, "timestamp": "2025-08-04T19:14:24.195715", "role": "Phantom", "won": true, "win_rate_window": 0.9458659880086597, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 521, "timestamp": "2025-08-04T19:14:24.195717", "role": "Engineer", "won": false, "win_rate_window": 0.4783048215894343, "reward": -1.1791579525801867, "episode_length": 116, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 522, "timestamp": "2025-08-04T19:14:24.195719", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 523, "timestamp": "2025-08-04T19:14:24.195720", "role": "Scientist", "won": true, "win_rate_window": 0.9341047141959864, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 524, "timestamp": "2025-08-04T19:14:24.195721", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 525, "timestamp": "2025-08-04T19:14:24.195722", "role": "Crewmate", "won": true, "win_rate_window": 0.676257406263438, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 526, "timestamp": "2025-08-04T19:14:24.195723", "role": "Scientist", "won": true, "win_rate_window": 0.9178454255231598, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 527, "timestamp": "2025-08-04T19:14:24.195724", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 528, "timestamp": "2025-08-04T19:14:24.195725", "role": "Crewmate", "won": false, "win_rate_window": 0.7341413185201264, "reward": -1.4871690514395577, "episode_length": 99, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 529, "timestamp": "2025-08-04T19:14:24.195726", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 530, "timestamp": "2025-08-04T19:14:24.195728", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 531, "timestamp": "2025-08-04T19:14:24.195729", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 532, "timestamp": "2025-08-04T19:14:24.195730", "role": "Scientist", "won": true, "win_rate_window": 0.9258051385352425, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 533, "timestamp": "2025-08-04T19:14:24.195731", "role": "Tracker", "won": true, "win_rate_window": 0.9206508848271354, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 534, "timestamp": "2025-08-04T19:14:24.195732", "role": "Scientist", "won": true, "win_rate_window": 0.730808266739029, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 535, "timestamp": "2025-08-04T19:14:24.195733", "role": "Engineer", "won": true, "win_rate_window": 0.9376732694127489, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 536, "timestamp": "2025-08-04T19:14:24.195734", "role": "Crewmate", "won": false, "win_rate_window": 0.6765473155954618, "reward": -1.1471350463319683, "episode_length": 99, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 537, "timestamp": "2025-08-04T19:14:24.195735", "role": "Crewmate", "won": true, "win_rate_window": 0.9025316049805717, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 538, "timestamp": "2025-08-04T19:14:24.195736", "role": "Engineer", "won": true, "win_rate_window": 0.8858619385199616, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 539, "timestamp": "2025-08-04T19:14:24.195737", "role": "Phantom", "won": true, "win_rate_window": 0.9034179669418541, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 540, "timestamp": "2025-08-04T19:14:24.195738", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 541, "timestamp": "2025-08-04T19:14:24.195739", "role": "Tracker", "won": true, "win_rate_window": 0.7153888543077512, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 542, "timestamp": "2025-08-04T19:14:24.195740", "role": "Impostor", "won": true, "win_rate_window": 0.9130502241337566, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 543, "timestamp": "2025-08-04T19:14:24.195741", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 544, "timestamp": "2025-08-04T19:14:24.195742", "role": "Engineer", "won": true, "win_rate_window": 0.7678022943157843, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 545, "timestamp": "2025-08-04T19:14:24.195743", "role": "Scientist", "won": true, "win_rate_window": 0.8335769514887267, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 546, "timestamp": "2025-08-04T19:14:24.195744", "role": "Phantom", "won": true, "win_rate_window": 0.8629687023374164, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 547, "timestamp": "2025-08-04T19:14:24.195746", "role": "Scientist", "won": true, "win_rate_window": 0.7279096603236659, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 548, "timestamp": "2025-08-04T19:14:24.195747", "role": "Impostor", "won": true, "win_rate_window": 0.9159971753948514, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 549, "timestamp": "2025-08-04T19:14:24.195748", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 550, "timestamp": "2025-08-04T19:14:24.195749", "role": "Phantom", "won": true, "win_rate_window": 0.5678151047566966, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 551, "timestamp": "2025-08-04T19:14:24.195750", "role": "Tracker", "won": true, "win_rate_window": 0.6542172160119546, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 552, "timestamp": "2025-08-04T19:14:24.195751", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 553, "timestamp": "2025-08-04T19:14:24.195752", "role": "Scientist", "won": true, "win_rate_window": 0.8643167144183135, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 554, "timestamp": "2025-08-04T19:14:24.195753", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7186102653590374, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 555, "timestamp": "2025-08-04T19:14:24.195754", "role": "Shapeshifter", "won": false, "win_rate_window": 0.9133687335829095, "reward": -0.6753758141675671, "episode_length": 96, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 556, "timestamp": "2025-08-04T19:14:24.195755", "role": "Scientist", "won": false, "win_rate_window": 0.823191886067338, "reward": -1.0187150436497037, "episode_length": 45, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 557, "timestamp": "2025-08-04T19:14:24.195756", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7622583318280072, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 558, "timestamp": "2025-08-04T19:14:24.195757", "role": "Engineer", "won": false, "win_rate_window": 0.5886371579124376, "reward": -0.16746610509483786, "episode_length": 95, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 559, "timestamp": "2025-08-04T19:14:24.195758", "role": "Crewmate", "won": true, "win_rate_window": 0.769808832421558, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 560, "timestamp": "2025-08-04T19:14:24.195759", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9379420689745794, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 561, "timestamp": "2025-08-04T19:14:24.195760", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7682803597398569, "reward": -0.7453346176568444, "episode_length": 102, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 562, "timestamp": "2025-08-04T19:14:24.195761", "role": "Tracker", "won": true, "win_rate_window": 0.6973970232368828, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 563, "timestamp": "2025-08-04T19:14:24.195762", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 564, "timestamp": "2025-08-04T19:14:24.195763", "role": "Crewmate", "won": true, "win_rate_window": 0.8417944479360715, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 565, "timestamp": "2025-08-04T19:14:24.195764", "role": "Engineer", "won": true, "win_rate_window": 0.760022466683183, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 566, "timestamp": "2025-08-04T19:14:24.195765", "role": "Crewmate", "won": false, "win_rate_window": 0.7895038287220382, "reward": -1.8787041857843758, "episode_length": 60, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 567, "timestamp": "2025-08-04T19:14:24.195767", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 568, "timestamp": "2025-08-04T19:14:24.195768", "role": "Impostor", "won": false, "win_rate_window": 0.6079037855221507, "reward": -0.8635160231483086, "episode_length": 92, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 569, "timestamp": "2025-08-04T19:14:24.195769", "role": "Tracker", "won": false, "win_rate_window": 0.9088654711350795, "reward": -1.164862328215158, "episode_length": 112, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 570, "timestamp": "2025-08-04T19:14:24.195770", "role": "Crewmate", "won": true, "win_rate_window": 0.8768929495519188, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 571, "timestamp": "2025-08-04T19:14:24.195771", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7451750730212608, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 572, "timestamp": "2025-08-04T19:14:24.195772", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 573, "timestamp": "2025-08-04T19:14:24.195773", "role": "Scientist", "won": true, "win_rate_window": 0.7201382800576794, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 574, "timestamp": "2025-08-04T19:14:24.195774", "role": "Shapeshifter", "won": false, "win_rate_window": 0.6963893436977105, "reward": -0.5193302611928646, "episode_length": 73, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 575, "timestamp": "2025-08-04T19:14:24.195775", "role": "Tracker", "won": true, "win_rate_window": 0.9272353280854704, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 576, "timestamp": "2025-08-04T19:14:24.195776", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 577, "timestamp": "2025-08-04T19:14:24.195777", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7824255192581152, "reward": -0.7760703655727446, "episode_length": 92, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 578, "timestamp": "2025-08-04T19:14:24.195779", "role": "Tracker", "won": true, "win_rate_window": 0.8959303798806417, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 579, "timestamp": "2025-08-04T19:14:24.195780", "role": "Phantom", "won": false, "win_rate_window": 0.8072119994099626, "reward": -1.4162694961628404, "episode_length": 64, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 580, "timestamp": "2025-08-04T19:14:24.195781", "role": "Impostor", "won": false, "win_rate_window": 0.6156461412964896, "reward": -1.4624333260714812, "episode_length": 64, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 581, "timestamp": "2025-08-04T19:14:24.195783", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7875955433007504, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 582, "timestamp": "2025-08-04T19:14:24.195784", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 583, "timestamp": "2025-08-04T19:14:24.195785", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7431578434593225, "reward": -0.36976177496075385, "episode_length": 62, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 584, "timestamp": "2025-08-04T19:14:24.195786", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 585, "timestamp": "2025-08-04T19:14:24.195787", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6306558533681217, "reward": 3.5999999999999996, "episode_length": 43, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 586, "timestamp": "2025-08-04T19:14:24.195788", "role": "Impostor", "won": false, "win_rate_window": 0.46583781833029, "reward": -1.5630042709930847, "episode_length": 44, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 587, "timestamp": "2025-08-04T19:14:24.195789", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 588, "timestamp": "2025-08-04T19:14:24.195790", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7357777113694248, "reward": -0.8566333905518578, "episode_length": 67, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 589, "timestamp": "2025-08-04T19:14:24.195791", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 590, "timestamp": "2025-08-04T19:14:24.195792", "role": "Crewmate", "won": true, "win_rate_window": 0.8707948825002155, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 591, "timestamp": "2025-08-04T19:14:24.195793", "role": "Scientist", "won": true, "win_rate_window": 0.8548200390425668, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 592, "timestamp": "2025-08-04T19:14:24.195794", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 593, "timestamp": "2025-08-04T19:14:24.195811", "role": "Crewmate", "won": false, "win_rate_window": 0.95, "reward": -1.447273478459254, "episode_length": 87, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 594, "timestamp": "2025-08-04T19:14:24.195812", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 595, "timestamp": "2025-08-04T19:14:24.195813", "role": "Tracker", "won": true, "win_rate_window": 0.8565668513366911, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 596, "timestamp": "2025-08-04T19:14:24.195814", "role": "Impostor", "won": false, "win_rate_window": 0.7450104821747632, "reward": -1.2910938157199598, "episode_length": 88, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 597, "timestamp": "2025-08-04T19:14:24.195815", "role": "Scientist", "won": true, "win_rate_window": 0.7010202744609316, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 598, "timestamp": "2025-08-04T19:14:24.195817", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 599, "timestamp": "2025-08-04T19:14:24.195818", "role": "Guardian Angel", "won": false, "win_rate_window": 0.7577892931868333, "reward": -1.0329311011103937, "episode_length": 58, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 600, "timestamp": "2025-08-04T19:14:24.195819", "role": "Tracker", "won": true, "win_rate_window": 0.6405213029398145, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 601, "timestamp": "2025-08-04T19:14:24.195820", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9468845911260042, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 602, "timestamp": "2025-08-04T19:14:24.195821", "role": "Crewmate", "won": true, "win_rate_window": 0.7088514803716511, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 603, "timestamp": "2025-08-04T19:14:24.195822", "role": "Tracker", "won": true, "win_rate_window": 0.8658365391472924, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 604, "timestamp": "2025-08-04T19:14:24.195823", "role": "Guardian Angel", "won": true, "win_rate_window": 0.810999442797806, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 605, "timestamp": "2025-08-04T19:14:24.195823", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8126697972887439, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 606, "timestamp": "2025-08-04T19:14:24.195826", "role": "Tracker", "won": true, "win_rate_window": 0.8303225031418635, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 607, "timestamp": "2025-08-04T19:14:24.195827", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 608, "timestamp": "2025-08-04T19:14:24.195828", "role": "Impostor", "won": true, "win_rate_window": 0.7447614524729264, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 609, "timestamp": "2025-08-04T19:14:24.195829", "role": "Scientist", "won": true, "win_rate_window": 0.8271797449773179, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 610, "timestamp": "2025-08-04T19:14:24.195830", "role": "Tracker", "won": true, "win_rate_window": 0.7928193068016935, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 611, "timestamp": "2025-08-04T19:14:24.195831", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 612, "timestamp": "2025-08-04T19:14:24.195832", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8556511534896422, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 613, "timestamp": "2025-08-04T19:14:24.195833", "role": "Engineer", "won": false, "win_rate_window": 0.7892104691652198, "reward": -0.9801847215952559, "episode_length": 62, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 614, "timestamp": "2025-08-04T19:14:24.195834", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 615, "timestamp": "2025-08-04T19:14:24.195835", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7023120250661194, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 616, "timestamp": "2025-08-04T19:14:24.195836", "role": "Engineer", "won": false, "win_rate_window": 0.6019790845258316, "reward": -0.5112686848992885, "episode_length": 113, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 617, "timestamp": "2025-08-04T19:14:24.195837", "role": "Tracker", "won": true, "win_rate_window": 0.8539900337908621, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 618, "timestamp": "2025-08-04T19:14:24.195838", "role": "Scientist", "won": false, "win_rate_window": 0.6084439371864393, "reward": -1.2509667591222162, "episode_length": 100, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 619, "timestamp": "2025-08-04T19:14:24.195839", "role": "Engineer", "won": false, "win_rate_window": 0.7244552467071197, "reward": -1.4199619178595426, "episode_length": 80, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 620, "timestamp": "2025-08-04T19:14:24.195840", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 621, "timestamp": "2025-08-04T19:14:24.195841", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 622, "timestamp": "2025-08-04T19:14:24.195842", "role": "Crewmate", "won": true, "win_rate_window": 0.8879402695676, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 623, "timestamp": "2025-08-04T19:14:24.195843", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8438788994768254, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 624, "timestamp": "2025-08-04T19:14:24.195844", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8637536585542138, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 625, "timestamp": "2025-08-04T19:14:24.195845", "role": "Phantom", "won": true, "win_rate_window": 0.8287381255481298, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 626, "timestamp": "2025-08-04T19:14:24.195846", "role": "Guardian Angel", "won": false, "win_rate_window": 0.6523870789564516, "reward": -1.0667868221201575, "episode_length": 43, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 627, "timestamp": "2025-08-04T19:14:24.195847", "role": "Guardian Angel", "won": true, "win_rate_window": 0.6318703541676054, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 628, "timestamp": "2025-08-04T19:14:24.195848", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 629, "timestamp": "2025-08-04T19:14:24.195849", "role": "Tracker", "won": true, "win_rate_window": 0.9228989024226744, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 630, "timestamp": "2025-08-04T19:14:24.195851", "role": "Tracker", "won": true, "win_rate_window": 0.8934687203451864, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 631, "timestamp": "2025-08-04T19:14:24.195852", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 632, "timestamp": "2025-08-04T19:14:24.195853", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 633, "timestamp": "2025-08-04T19:14:24.195854", "role": "Impostor", "won": true, "win_rate_window": 0.5989707089502794, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 634, "timestamp": "2025-08-04T19:14:24.195855", "role": "Phantom", "won": true, "win_rate_window": 0.8568381995911694, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 635, "timestamp": "2025-08-04T19:14:24.195856", "role": "Guardian Angel", "won": false, "win_rate_window": 0.6861436788583989, "reward": -0.9960942204582559, "episode_length": 76, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 636, "timestamp": "2025-08-04T19:14:24.195857", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 637, "timestamp": "2025-08-04T19:14:24.195858", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 638, "timestamp": "2025-08-04T19:14:24.195859", "role": "Crewmate", "won": true, "win_rate_window": 0.7418857247942621, "reward": 3.5999999999999996, "episode_length": 84, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 639, "timestamp": "2025-08-04T19:14:24.195860", "role": "Scientist", "won": true, "win_rate_window": 0.9086102941976473, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 640, "timestamp": "2025-08-04T19:14:24.195861", "role": "Impostor", "won": true, "win_rate_window": 0.8799661863014848, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 641, "timestamp": "2025-08-04T19:14:24.195863", "role": "Crewmate", "won": false, "win_rate_window": 0.6467554673222672, "reward": -1.2911625951278198, "episode_length": 57, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 642, "timestamp": "2025-08-04T19:14:24.195864", "role": "Scientist", "won": false, "win_rate_window": 0.6014714164010085, "reward": -1.7095106285572186, "episode_length": 58, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 643, "timestamp": "2025-08-04T19:14:24.195865", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 644, "timestamp": "2025-08-04T19:14:24.195866", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 645, "timestamp": "2025-08-04T19:14:24.195868", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 646, "timestamp": "2025-08-04T19:14:24.195869", "role": "Engineer", "won": true, "win_rate_window": 0.8531622553410526, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 647, "timestamp": "2025-08-04T19:14:24.195870", "role": "Impostor", "won": true, "win_rate_window": 0.7632418168070569, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 648, "timestamp": "2025-08-04T19:14:24.195871", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 649, "timestamp": "2025-08-04T19:14:24.195872", "role": "Tracker", "won": true, "win_rate_window": 0.7021498468291728, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 650, "timestamp": "2025-08-04T19:14:24.195873", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 651, "timestamp": "2025-08-04T19:14:24.195874", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 652, "timestamp": "2025-08-04T19:14:24.195875", "role": "Tracker", "won": true, "win_rate_window": 0.9381620789212565, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 653, "timestamp": "2025-08-04T19:14:24.195876", "role": "Scientist", "won": false, "win_rate_window": 0.6622508929395587, "reward": -1.4725963257331844, "episode_length": 96, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 654, "timestamp": "2025-08-04T19:14:24.195877", "role": "Crewmate", "won": true, "win_rate_window": 0.7848252450250638, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 655, "timestamp": "2025-08-04T19:14:24.195878", "role": "Engineer", "won": true, "win_rate_window": 0.9410328320046305, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 656, "timestamp": "2025-08-04T19:14:24.195879", "role": "Impostor", "won": true, "win_rate_window": 0.8645960105558091, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 657, "timestamp": "2025-08-04T19:14:24.195880", "role": "Engineer", "won": true, "win_rate_window": 0.4912218260037006, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 658, "timestamp": "2025-08-04T19:14:24.195881", "role": "Engineer", "won": true, "win_rate_window": 0.7541964478167176, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 659, "timestamp": "2025-08-04T19:14:24.195882", "role": "Impostor", "won": true, "win_rate_window": 0.8796234431839751, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 660, "timestamp": "2025-08-04T19:14:24.195883", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 661, "timestamp": "2025-08-04T19:14:24.195884", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 662, "timestamp": "2025-08-04T19:14:24.195885", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 112, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 663, "timestamp": "2025-08-04T19:14:24.195886", "role": "Engineer", "won": true, "win_rate_window": 0.7146191276032832, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 664, "timestamp": "2025-08-04T19:14:24.195888", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 665, "timestamp": "2025-08-04T19:14:24.195889", "role": "Tracker", "won": true, "win_rate_window": 0.9099697393427892, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 666, "timestamp": "2025-08-04T19:14:24.195890", "role": "Phantom", "won": false, "win_rate_window": 0.572082961991523, "reward": -1.7199235972852256, "episode_length": 65, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 667, "timestamp": "2025-08-04T19:14:24.195891", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 668, "timestamp": "2025-08-04T19:14:24.195892", "role": "Impostor", "won": true, "win_rate_window": 0.9164771103535693, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 669, "timestamp": "2025-08-04T19:14:24.195893", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6378704195011895, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 670, "timestamp": "2025-08-04T19:14:24.195894", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 671, "timestamp": "2025-08-04T19:14:24.195895", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 672, "timestamp": "2025-08-04T19:14:24.195896", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 673, "timestamp": "2025-08-04T19:14:24.195907", "role": "Crewmate", "won": false, "win_rate_window": 0.5831127520684168, "reward": -1.1134099940156978, "episode_length": 105, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 674, "timestamp": "2025-08-04T19:14:24.195908", "role": "Engineer", "won": true, "win_rate_window": 0.6918580868881334, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 675, "timestamp": "2025-08-04T19:14:24.195909", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 676, "timestamp": "2025-08-04T19:14:24.195910", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7086971153103871, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 677, "timestamp": "2025-08-04T19:14:24.195911", "role": "Phantom", "won": true, "win_rate_window": 0.868997675463732, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 678, "timestamp": "2025-08-04T19:14:24.195912", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 679, "timestamp": "2025-08-04T19:14:24.195913", "role": "Tracker", "won": true, "win_rate_window": 0.6784982668713084, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 680, "timestamp": "2025-08-04T19:14:24.195914", "role": "Crewmate", "won": false, "win_rate_window": 0.7041730638586012, "reward": -1.652966605271102, "episode_length": 52, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 681, "timestamp": "2025-08-04T19:14:24.195915", "role": "Phantom", "won": true, "win_rate_window": 0.7058840666112765, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 682, "timestamp": "2025-08-04T19:14:24.195916", "role": "Engineer", "won": true, "win_rate_window": 0.8761358382413105, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 683, "timestamp": "2025-08-04T19:14:24.195917", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 684, "timestamp": "2025-08-04T19:14:24.195920", "role": "Engineer", "won": true, "win_rate_window": 0.8917034839691919, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 685, "timestamp": "2025-08-04T19:14:24.195921", "role": "Crewmate", "won": true, "win_rate_window": 0.9449127882856826, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 686, "timestamp": "2025-08-04T19:14:24.195922", "role": "Crewmate", "won": true, "win_rate_window": 0.7704724719981912, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 49, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 687, "timestamp": "2025-08-04T19:14:24.195923", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 688, "timestamp": "2025-08-04T19:14:24.195924", "role": "Scientist", "won": true, "win_rate_window": 0.8797850063130997, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 689, "timestamp": "2025-08-04T19:14:24.195925", "role": "Tracker", "won": true, "win_rate_window": 0.8017990133220099, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 690, "timestamp": "2025-08-04T19:14:24.195926", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 691, "timestamp": "2025-08-04T19:14:24.195927", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7872858214806286, "reward": -0.8777078489910938, "episode_length": 99, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 692, "timestamp": "2025-08-04T19:14:24.195928", "role": "Crewmate", "won": true, "win_rate_window": 0.8167132842450426, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 693, "timestamp": "2025-08-04T19:14:24.195929", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 694, "timestamp": "2025-08-04T19:14:24.195930", "role": "Crewmate", "won": true, "win_rate_window": 0.8447708500336045, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 695, "timestamp": "2025-08-04T19:14:24.195931", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8952111912061692, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 696, "timestamp": "2025-08-04T19:14:24.195932", "role": "Phantom", "won": false, "win_rate_window": 0.6325631992830031, "reward": -1.1885634757323478, "episode_length": 107, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 697, "timestamp": "2025-08-04T19:14:24.195933", "role": "Phantom", "won": false, "win_rate_window": 0.7628223843986901, "reward": -1.485495708843539, "episode_length": 41, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 698, "timestamp": "2025-08-04T19:14:24.195934", "role": "Shapeshifter", "won": false, "win_rate_window": 0.632802285010203, "reward": -0.7479556041106362, "episode_length": 50, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 699, "timestamp": "2025-08-04T19:14:24.195935", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 700, "timestamp": "2025-08-04T19:14:24.195936", "role": "Crewmate", "won": true, "win_rate_window": 0.7750854351487739, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 701, "timestamp": "2025-08-04T19:14:24.195937", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 702, "timestamp": "2025-08-04T19:14:24.195938", "role": "Crewmate", "won": true, "win_rate_window": 0.8305951848341973, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 703, "timestamp": "2025-08-04T19:14:24.195940", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 704, "timestamp": "2025-08-04T19:14:24.195941", "role": "Impostor", "won": false, "win_rate_window": 0.6245911354578237, "reward": -0.6855292761820181, "episode_length": 45, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 705, "timestamp": "2025-08-04T19:14:24.195943", "role": "Tracker", "won": true, "win_rate_window": 0.8721794022252756, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 706, "timestamp": "2025-08-04T19:14:24.195944", "role": "Impostor", "won": false, "win_rate_window": 0.6119132555310535, "reward": -1.1676873401110714, "episode_length": 84, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 707, "timestamp": "2025-08-04T19:14:24.195945", "role": "Phantom", "won": true, "win_rate_window": 0.6397874359648413, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 708, "timestamp": "2025-08-04T19:14:24.195946", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 709, "timestamp": "2025-08-04T19:14:24.195947", "role": "Scientist", "won": true, "win_rate_window": 0.7002914250431209, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 710, "timestamp": "2025-08-04T19:14:24.195948", "role": "Tracker", "won": true, "win_rate_window": 0.9404975487487854, "reward": 3.5999999999999996, "episode_length": 110, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 711, "timestamp": "2025-08-04T19:14:24.195949", "role": "Impostor", "won": true, "win_rate_window": 0.896387140377488, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 712, "timestamp": "2025-08-04T19:14:24.195950", "role": "Phantom", "won": true, "win_rate_window": 0.7089066381408576, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 713, "timestamp": "2025-08-04T19:14:24.195951", "role": "Guardian Angel", "won": true, "win_rate_window": 0.726141528756343, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 714, "timestamp": "2025-08-04T19:14:24.195952", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 715, "timestamp": "2025-08-04T19:14:24.195953", "role": "Engineer", "won": true, "win_rate_window": 0.8156381913381237, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 716, "timestamp": "2025-08-04T19:14:24.195954", "role": "Crewmate", "won": true, "win_rate_window": 0.7873035902468976, "reward": 3.5999999999999996, "episode_length": 88, "actions_taken": 61, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 717, "timestamp": "2025-08-04T19:14:24.195955", "role": "Scientist", "won": true, "win_rate_window": 0.6594971930817088, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 718, "timestamp": "2025-08-04T19:14:24.195956", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 719, "timestamp": "2025-08-04T19:14:24.195957", "role": "Crewmate", "won": true, "win_rate_window": 0.6043856010896945, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 720, "timestamp": "2025-08-04T19:14:24.195958", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 721, "timestamp": "2025-08-04T19:14:24.195959", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 722, "timestamp": "2025-08-04T19:14:24.195960", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 723, "timestamp": "2025-08-04T19:14:24.195961", "role": "Phantom", "won": true, "win_rate_window": 0.9172239716067829, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 724, "timestamp": "2025-08-04T19:14:24.195962", "role": "Engineer", "won": true, "win_rate_window": 0.7791574375290999, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 725, "timestamp": "2025-08-04T19:14:24.195963", "role": "Engineer", "won": true, "win_rate_window": 0.8212210895655999, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 726, "timestamp": "2025-08-04T19:14:24.195964", "role": "Scientist", "won": false, "win_rate_window": 0.8053968456365914, "reward": -0.41806164378772864, "episode_length": 92, "actions_taken": 36, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 727, "timestamp": "2025-08-04T19:14:24.195966", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 94, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 728, "timestamp": "2025-08-04T19:14:24.195966", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 729, "timestamp": "2025-08-04T19:14:24.195968", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9490882042669698, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 730, "timestamp": "2025-08-04T19:14:24.195968", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 92, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 731, "timestamp": "2025-08-04T19:14:24.195969", "role": "Engineer", "won": true, "win_rate_window": 0.7557692692707634, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 732, "timestamp": "2025-08-04T19:14:24.195970", "role": "Engineer", "won": true, "win_rate_window": 0.9098465792241707, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 733, "timestamp": "2025-08-04T19:14:24.195971", "role": "Guardian Angel", "won": false, "win_rate_window": 0.8387908329925792, "reward": -1.992852816670205, "episode_length": 67, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 734, "timestamp": "2025-08-04T19:14:24.195973", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 735, "timestamp": "2025-08-04T19:14:24.195974", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 736, "timestamp": "2025-08-04T19:14:24.195975", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 737, "timestamp": "2025-08-04T19:14:24.195976", "role": "Impostor", "won": false, "win_rate_window": 0.8020847671957265, "reward": -1.2757653735149954, "episode_length": 58, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 738, "timestamp": "2025-08-04T19:14:24.195977", "role": "Phantom", "won": true, "win_rate_window": 0.7804720311395353, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 739, "timestamp": "2025-08-04T19:14:24.195978", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8331345709949487, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 740, "timestamp": "2025-08-04T19:14:24.195979", "role": "Crewmate", "won": true, "win_rate_window": 0.8038461695570555, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 741, "timestamp": "2025-08-04T19:14:24.195980", "role": "Engineer", "won": false, "win_rate_window": 0.706879327723539, "reward": -0.15957276269721507, "episode_length": 49, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 742, "timestamp": "2025-08-04T19:14:24.195982", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7777519886478501, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 743, "timestamp": "2025-08-04T19:14:24.195983", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 744, "timestamp": "2025-08-04T19:14:24.195984", "role": "Guardian Angel", "won": true, "win_rate_window": 0.914205357707486, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 745, "timestamp": "2025-08-04T19:14:24.195985", "role": "Tracker", "won": true, "win_rate_window": 0.7156773894028836, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 746, "timestamp": "2025-08-04T19:14:24.195986", "role": "Phantom", "won": true, "win_rate_window": 0.7612758072486618, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 747, "timestamp": "2025-08-04T19:14:24.195987", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8469230397797042, "reward": -1.0032891296848063, "episode_length": 101, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 748, "timestamp": "2025-08-04T19:14:24.195988", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 749, "timestamp": "2025-08-04T19:14:24.195989", "role": "Crewmate", "won": true, "win_rate_window": 0.8743648034257866, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 750, "timestamp": "2025-08-04T19:14:24.195990", "role": "Tracker", "won": true, "win_rate_window": 0.9054503398440217, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 751, "timestamp": "2025-08-04T19:14:24.195991", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7418977139674529, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 752, "timestamp": "2025-08-04T19:14:24.195992", "role": "Impostor", "won": true, "win_rate_window": 0.8507723488988357, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 753, "timestamp": "2025-08-04T19:14:24.195993", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9143007699146017, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 754, "timestamp": "2025-08-04T19:14:24.195994", "role": "Guardian Angel", "won": false, "win_rate_window": 0.5098928867372123, "reward": -1.7431216239951446, "episode_length": 71, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 755, "timestamp": "2025-08-04T19:14:24.195995", "role": "Tracker", "won": true, "win_rate_window": 0.8606982985750825, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 756, "timestamp": "2025-08-04T19:14:24.195996", "role": "Engineer", "won": true, "win_rate_window": 0.807863080177217, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 757, "timestamp": "2025-08-04T19:14:24.195997", "role": "Tracker", "won": false, "win_rate_window": 0.6208434634567546, "reward": -1.6737154219894952, "episode_length": 117, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 758, "timestamp": "2025-08-04T19:14:24.195998", "role": "Phantom", "won": true, "win_rate_window": 0.6420039682272158, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 759, "timestamp": "2025-08-04T19:14:24.195999", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 760, "timestamp": "2025-08-04T19:14:24.196000", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7139362340203776, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 761, "timestamp": "2025-08-04T19:14:24.196002", "role": "Scientist", "won": true, "win_rate_window": 0.7168182075573537, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 762, "timestamp": "2025-08-04T19:14:24.196004", "role": "Crewmate", "won": true, "win_rate_window": 0.882731304464026, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 763, "timestamp": "2025-08-04T19:14:24.196005", "role": "Impostor", "won": true, "win_rate_window": 0.9243606230839876, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 764, "timestamp": "2025-08-04T19:14:24.196006", "role": "Impostor", "won": true, "win_rate_window": 0.7037842018158316, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 765, "timestamp": "2025-08-04T19:14:24.196007", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8536205631377954, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 766, "timestamp": "2025-08-04T19:14:24.196008", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 75, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 767, "timestamp": "2025-08-04T19:14:24.196009", "role": "Scientist", "won": true, "win_rate_window": 0.9261970020173561, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 768, "timestamp": "2025-08-04T19:14:24.196010", "role": "Tracker", "won": true, "win_rate_window": 0.6980407063056655, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 769, "timestamp": "2025-08-04T19:14:24.196012", "role": "Impostor", "won": true, "win_rate_window": 0.9002380743389691, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 770, "timestamp": "2025-08-04T19:14:24.196013", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 771, "timestamp": "2025-08-04T19:14:24.196014", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7826139683916313, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 772, "timestamp": "2025-08-04T19:14:24.196015", "role": "Scientist", "won": true, "win_rate_window": 0.5871860280816165, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 773, "timestamp": "2025-08-04T19:14:24.196016", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 774, "timestamp": "2025-08-04T19:14:24.196017", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9437420025913472, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 775, "timestamp": "2025-08-04T19:14:24.196018", "role": "Scientist", "won": true, "win_rate_window": 0.6323093810244664, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 776, "timestamp": "2025-08-04T19:14:24.196019", "role": "Scientist", "won": false, "win_rate_window": 0.751606379812088, "reward": -0.5905438232394309, "episode_length": 90, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 777, "timestamp": "2025-08-04T19:14:24.196020", "role": "Scientist", "won": true, "win_rate_window": 0.7580034757919808, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 778, "timestamp": "2025-08-04T19:14:24.196021", "role": "Scientist", "won": true, "win_rate_window": 0.9141869376752436, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 779, "timestamp": "2025-08-04T19:14:24.196022", "role": "Scientist", "won": true, "win_rate_window": 0.874668999747848, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 780, "timestamp": "2025-08-04T19:14:24.196023", "role": "Phantom", "won": false, "win_rate_window": 0.6166210197106436, "reward": -0.30978332745359743, "episode_length": 50, "actions_taken": 50, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 781, "timestamp": "2025-08-04T19:14:24.196025", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6825478057643579, "reward": 3.5999999999999996, "episode_length": 43, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 782, "timestamp": "2025-08-04T19:14:24.196026", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 59, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 783, "timestamp": "2025-08-04T19:14:24.196027", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 784, "timestamp": "2025-08-04T19:14:24.196028", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9278444480195143, "reward": 3.5999999999999996, "episode_length": 109, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 785, "timestamp": "2025-08-04T19:14:24.196029", "role": "Engineer", "won": true, "win_rate_window": 0.7559308369790119, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 786, "timestamp": "2025-08-04T19:14:24.196030", "role": "Scientist", "won": true, "win_rate_window": 0.7900427264320516, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 787, "timestamp": "2025-08-04T19:14:24.196031", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 53, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 788, "timestamp": "2025-08-04T19:14:24.196032", "role": "Engineer", "won": true, "win_rate_window": 0.8427533788196826, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 789, "timestamp": "2025-08-04T19:14:24.196033", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 95, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 790, "timestamp": "2025-08-04T19:14:24.196034", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 791, "timestamp": "2025-08-04T19:14:24.196035", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8223024037057022, "reward": -1.0819543819957689, "episode_length": 45, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 792, "timestamp": "2025-08-04T19:14:24.196036", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8473125156306435, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 793, "timestamp": "2025-08-04T19:14:24.196037", "role": "Crewmate", "won": true, "win_rate_window": 0.9253381893451161, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 794, "timestamp": "2025-08-04T19:14:24.196038", "role": "Impostor", "won": true, "win_rate_window": 0.7250162316825868, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 795, "timestamp": "2025-08-04T19:14:24.196039", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 796, "timestamp": "2025-08-04T19:14:24.196040", "role": "Phantom", "won": false, "win_rate_window": 0.8179076122060427, "reward": -1.1317402974512432, "episode_length": 97, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 797, "timestamp": "2025-08-04T19:14:24.196041", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8862471713346958, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 82, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 798, "timestamp": "2025-08-04T19:14:24.196042", "role": "Shapeshifter", "won": true, "win_rate_window": 0.87100949255102, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 80, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 799, "timestamp": "2025-08-04T19:14:24.196043", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 800, "timestamp": "2025-08-04T19:14:24.196044", "role": "Scientist", "won": true, "win_rate_window": 0.7836672958046711, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 801, "timestamp": "2025-08-04T19:14:24.196046", "role": "Crewmate", "won": true, "win_rate_window": 0.9298608853833046, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 802, "timestamp": "2025-08-04T19:14:24.196047", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 113, "actions_taken": 37, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 803, "timestamp": "2025-08-04T19:14:24.196048", "role": "Scientist", "won": true, "win_rate_window": 0.9446304234788081, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 804, "timestamp": "2025-08-04T19:14:24.196049", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 107, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 805, "timestamp": "2025-08-04T19:14:24.196050", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8012586875006737, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 806, "timestamp": "2025-08-04T19:14:24.196051", "role": "Scientist", "won": true, "win_rate_window": 0.8362728530885892, "reward": 3.5999999999999996, "episode_length": 72, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 807, "timestamp": "2025-08-04T19:14:24.196052", "role": "Engineer", "won": true, "win_rate_window": 0.9216108287217578, "reward": 3.5999999999999996, "episode_length": 93, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 808, "timestamp": "2025-08-04T19:14:24.196053", "role": "Shapeshifter", "won": true, "win_rate_window": 0.5819572160617039, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 809, "timestamp": "2025-08-04T19:14:24.196054", "role": "Phantom", "won": true, "win_rate_window": 0.7093132742898333, "reward": 3.5999999999999996, "episode_length": 43, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 810, "timestamp": "2025-08-04T19:14:24.196055", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 55, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 811, "timestamp": "2025-08-04T19:14:24.196056", "role": "Impostor", "won": false, "win_rate_window": 0.5960868654208754, "reward": -1.9636467676536542, "episode_length": 99, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 812, "timestamp": "2025-08-04T19:14:24.196057", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 813, "timestamp": "2025-08-04T19:14:24.196058", "role": "Impostor", "won": true, "win_rate_window": 0.8360615598448451, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 814, "timestamp": "2025-08-04T19:14:24.196059", "role": "Phantom", "won": true, "win_rate_window": 0.8794703187129425, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 815, "timestamp": "2025-08-04T19:14:24.196060", "role": "Impostor", "won": true, "win_rate_window": 0.913916611101107, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 91, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 816, "timestamp": "2025-08-04T19:14:24.196061", "role": "Impostor", "won": true, "win_rate_window": 0.9240983518306745, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 817, "timestamp": "2025-08-04T19:14:24.196062", "role": "Crewmate", "won": false, "win_rate_window": 0.5329719905585819, "reward": -1.7045964305254298, "episode_length": 70, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 818, "timestamp": "2025-08-04T19:14:24.196063", "role": "Scientist", "won": true, "win_rate_window": 0.7050703821612561, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 819, "timestamp": "2025-08-04T19:14:24.196064", "role": "Impostor", "won": true, "win_rate_window": 0.6442793977116344, "reward": 3.5999999999999996, "episode_length": 93, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 820, "timestamp": "2025-08-04T19:14:24.196066", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9015009806179572, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 821, "timestamp": "2025-08-04T19:14:24.196067", "role": "Engineer", "won": false, "win_rate_window": 0.571393658708149, "reward": -0.8905497890442249, "episode_length": 96, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 822, "timestamp": "2025-08-04T19:14:24.196068", "role": "Impostor", "won": false, "win_rate_window": 0.837261992287297, "reward": -1.4332035181626017, "episode_length": 107, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 823, "timestamp": "2025-08-04T19:14:24.196069", "role": "Crewmate", "won": true, "win_rate_window": 0.712665733156757, "reward": 3.5999999999999996, "episode_length": 67, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 824, "timestamp": "2025-08-04T19:14:24.196070", "role": "Phantom", "won": true, "win_rate_window": 0.9035902144702126, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 825, "timestamp": "2025-08-04T19:14:24.196071", "role": "Phantom", "won": true, "win_rate_window": 0.844050655473772, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 826, "timestamp": "2025-08-04T19:14:24.196072", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 79, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 827, "timestamp": "2025-08-04T19:14:24.196073", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 828, "timestamp": "2025-08-04T19:14:24.196074", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 42, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 829, "timestamp": "2025-08-04T19:14:24.196075", "role": "Tracker", "won": true, "win_rate_window": 0.7595209346962418, "reward": 3.5999999999999996, "episode_length": 77, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 830, "timestamp": "2025-08-04T19:14:24.196076", "role": "Guardian Angel", "won": false, "win_rate_window": 0.6804820795742009, "reward": -1.2425449371369475, "episode_length": 55, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 831, "timestamp": "2025-08-04T19:14:24.196077", "role": "Engineer", "won": false, "win_rate_window": 0.6501877001868986, "reward": -0.547138955534271, "episode_length": 77, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 832, "timestamp": "2025-08-04T19:14:24.196078", "role": "Scientist", "won": true, "win_rate_window": 0.8776401804361409, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 31, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 833, "timestamp": "2025-08-04T19:14:24.196079", "role": "Phantom", "won": true, "win_rate_window": 0.7452940242741173, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 834, "timestamp": "2025-08-04T19:14:24.196080", "role": "Scientist", "won": true, "win_rate_window": 0.9168161129468246, "reward": 3.5999999999999996, "episode_length": 74, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 835, "timestamp": "2025-08-04T19:14:24.196081", "role": "Shapeshifter", "won": false, "win_rate_window": 0.757651653291458, "reward": -1.0343729904430434, "episode_length": 68, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 836, "timestamp": "2025-08-04T19:14:24.196083", "role": "Crewmate", "won": true, "win_rate_window": 0.5292501858361636, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 77, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 837, "timestamp": "2025-08-04T19:14:24.196084", "role": "Phantom", "won": false, "win_rate_window": 0.6796236498242979, "reward": -1.1587356365969483, "episode_length": 74, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 838, "timestamp": "2025-08-04T19:14:24.196085", "role": "Impostor", "won": false, "win_rate_window": 0.6028027509303645, "reward": -0.7290695623163382, "episode_length": 98, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 839, "timestamp": "2025-08-04T19:14:24.196086", "role": "Phantom", "won": true, "win_rate_window": 0.9001052963256142, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 840, "timestamp": "2025-08-04T19:14:24.196088", "role": "Crewmate", "won": true, "win_rate_window": 0.8922260479671623, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 841, "timestamp": "2025-08-04T19:14:24.196089", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9001847475675575, "reward": 3.5999999999999996, "episode_length": 52, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 842, "timestamp": "2025-08-04T19:14:24.196090", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8476572759943074, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 843, "timestamp": "2025-08-04T19:14:24.196091", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8599623524009959, "reward": 3.5999999999999996, "episode_length": 54, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 844, "timestamp": "2025-08-04T19:14:24.196092", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 71, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 845, "timestamp": "2025-08-04T19:14:24.196093", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 846, "timestamp": "2025-08-04T19:14:24.196094", "role": "Engineer", "won": true, "win_rate_window": 0.6532547080038775, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 847, "timestamp": "2025-08-04T19:14:24.196095", "role": "Impostor", "won": false, "win_rate_window": 0.9354018985800492, "reward": -1.2197603872017786, "episode_length": 43, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 848, "timestamp": "2025-08-04T19:14:24.196096", "role": "Crewmate", "won": false, "win_rate_window": 0.6147879297873678, "reward": -1.4872298916209414, "episode_length": 59, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 849, "timestamp": "2025-08-04T19:14:24.196097", "role": "Impostor", "won": true, "win_rate_window": 0.8884472979576873, "reward": 3.5999999999999996, "episode_length": 92, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 850, "timestamp": "2025-08-04T19:14:24.196098", "role": "Shapeshifter", "won": true, "win_rate_window": 0.772310617273231, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 851, "timestamp": "2025-08-04T19:14:24.196099", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7984077034061147, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 852, "timestamp": "2025-08-04T19:14:24.196100", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 120, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 853, "timestamp": "2025-08-04T19:14:24.196101", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 854, "timestamp": "2025-08-04T19:14:24.196102", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 855, "timestamp": "2025-08-04T19:14:24.196103", "role": "Phantom", "won": true, "win_rate_window": 0.9001619842002594, "reward": 3.5999999999999996, "episode_length": 40, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 856, "timestamp": "2025-08-04T19:14:24.196104", "role": "Phantom", "won": true, "win_rate_window": 0.8770552847825276, "reward": 3.5999999999999996, "episode_length": 43, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 857, "timestamp": "2025-08-04T19:14:24.196105", "role": "Crewmate", "won": true, "win_rate_window": 0.9323361904780069, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 858, "timestamp": "2025-08-04T19:14:24.196106", "role": "Phantom", "won": true, "win_rate_window": 0.8212667817096321, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 859, "timestamp": "2025-08-04T19:14:24.196107", "role": "Crewmate", "won": true, "win_rate_window": 0.7739214229096123, "reward": 3.5999999999999996, "episode_length": 111, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 860, "timestamp": "2025-08-04T19:14:24.196109", "role": "Tracker", "won": true, "win_rate_window": 0.7600316907328792, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 861, "timestamp": "2025-08-04T19:14:24.196110", "role": "Engineer", "won": true, "win_rate_window": 0.8524467382320187, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 862, "timestamp": "2025-08-04T19:14:24.196111", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 863, "timestamp": "2025-08-04T19:14:24.196112", "role": "Crewmate", "won": false, "win_rate_window": 0.7314014872589226, "reward": -0.6802462462827605, "episode_length": 75, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 864, "timestamp": "2025-08-04T19:14:24.196113", "role": "Engineer", "won": true, "win_rate_window": 0.8090414347622968, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 865, "timestamp": "2025-08-04T19:14:24.196114", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7095179427991809, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 866, "timestamp": "2025-08-04T19:14:24.196115", "role": "Shapeshifter", "won": true, "win_rate_window": 0.8472224852260943, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 867, "timestamp": "2025-08-04T19:14:24.196116", "role": "Phantom", "won": true, "win_rate_window": 0.5133973878328233, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 52, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 868, "timestamp": "2025-08-04T19:14:24.196117", "role": "Crewmate", "won": true, "win_rate_window": 0.8127802340633438, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 869, "timestamp": "2025-08-04T19:14:24.196118", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 870, "timestamp": "2025-08-04T19:14:24.196119", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 117, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 871, "timestamp": "2025-08-04T19:14:24.196120", "role": "Scientist", "won": true, "win_rate_window": 0.9445709307379802, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 872, "timestamp": "2025-08-04T19:14:24.196121", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 91, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 873, "timestamp": "2025-08-04T19:14:24.196122", "role": "Crewmate", "won": false, "win_rate_window": 0.5818935066066312, "reward": -1.1471874389986545, "episode_length": 110, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 874, "timestamp": "2025-08-04T19:14:24.196123", "role": "Impostor", "won": false, "win_rate_window": 0.5815392599360895, "reward": -0.6697979449128577, "episode_length": 89, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 875, "timestamp": "2025-08-04T19:14:24.196124", "role": "Shapeshifter", "won": false, "win_rate_window": 0.5084894076007312, "reward": -0.6546149534126495, "episode_length": 119, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 876, "timestamp": "2025-08-04T19:14:24.196125", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8446315382130927, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 877, "timestamp": "2025-08-04T19:14:24.196126", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 93, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 878, "timestamp": "2025-08-04T19:14:24.196127", "role": "Shapeshifter", "won": true, "win_rate_window": 0.879520187938316, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 879, "timestamp": "2025-08-04T19:14:24.196133", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7135744929563537, "reward": 3.5999999999999996, "episode_length": 119, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 880, "timestamp": "2025-08-04T19:14:24.196134", "role": "Shapeshifter", "won": true, "win_rate_window": 0.598004435288835, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 881, "timestamp": "2025-08-04T19:14:24.196135", "role": "Phantom", "won": true, "win_rate_window": 0.818255795081152, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 882, "timestamp": "2025-08-04T19:14:24.196136", "role": "Engineer", "won": false, "win_rate_window": 0.476326578587079, "reward": -1.799796881667034, "episode_length": 55, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 883, "timestamp": "2025-08-04T19:14:24.196137", "role": "Guardian Angel", "won": true, "win_rate_window": 0.900430993206545, "reward": 3.5999999999999996, "episode_length": 83, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 884, "timestamp": "2025-08-04T19:14:24.196138", "role": "Phantom", "won": true, "win_rate_window": 0.690528123873372, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 885, "timestamp": "2025-08-04T19:14:24.196139", "role": "Impostor", "won": true, "win_rate_window": 0.8403045897722407, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 886, "timestamp": "2025-08-04T19:14:24.196140", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7789230250280681, "reward": -0.8039745112763573, "episode_length": 118, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 887, "timestamp": "2025-08-04T19:14:24.196141", "role": "Impostor", "won": true, "win_rate_window": 0.7731008786891307, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 888, "timestamp": "2025-08-04T19:14:24.196142", "role": "Guardian Angel", "won": false, "win_rate_window": 0.7625934823200143, "reward": -0.6810948668073801, "episode_length": 47, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 889, "timestamp": "2025-08-04T19:14:24.196144", "role": "Phantom", "won": true, "win_rate_window": 0.8741948139016881, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 81, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 890, "timestamp": "2025-08-04T19:14:24.196145", "role": "Shapeshifter", "won": true, "win_rate_window": 0.9282135508793553, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 891, "timestamp": "2025-08-04T19:14:24.196146", "role": "Impostor", "won": true, "win_rate_window": 0.5790301209466167, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 76, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 892, "timestamp": "2025-08-04T19:14:24.196147", "role": "Guardian Angel", "won": true, "win_rate_window": 0.8402397494807738, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 68, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 893, "timestamp": "2025-08-04T19:14:24.196148", "role": "Impostor", "won": true, "win_rate_window": 0.8131452159193879, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 894, "timestamp": "2025-08-04T19:14:24.196149", "role": "Phantom", "won": true, "win_rate_window": 0.6400730810838661, "reward": 3.5999999999999996, "episode_length": 87, "actions_taken": 45, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 895, "timestamp": "2025-08-04T19:14:24.196150", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 896, "timestamp": "2025-08-04T19:14:24.196151", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8563709853138672, "reward": -1.0757537528537255, "episode_length": 47, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 897, "timestamp": "2025-08-04T19:14:24.196152", "role": "Impostor", "won": true, "win_rate_window": 0.7644032510776254, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 88, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 898, "timestamp": "2025-08-04T19:14:24.196154", "role": "Phantom", "won": false, "win_rate_window": 0.95, "reward": -0.6332456970282844, "episode_length": 69, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 899, "timestamp": "2025-08-04T19:14:24.196155", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 900, "timestamp": "2025-08-04T19:14:24.196156", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7967643427403488, "reward": -1.9050348549800822, "episode_length": 118, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 901, "timestamp": "2025-08-04T19:14:24.196157", "role": "Phantom", "won": true, "win_rate_window": 0.9229201769536779, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 902, "timestamp": "2025-08-04T19:14:24.196158", "role": "Guardian Angel", "won": true, "win_rate_window": 0.9035940937766209, "reward": 3.5999999999999996, "episode_length": 61, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 903, "timestamp": "2025-08-04T19:14:24.196159", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 104, "actions_taken": 53, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 904, "timestamp": "2025-08-04T19:14:24.196160", "role": "Guardian Angel", "won": false, "win_rate_window": 0.703287619050762, "reward": -1.4346459084594292, "episode_length": 56, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 905, "timestamp": "2025-08-04T19:14:24.196161", "role": "Phantom", "won": true, "win_rate_window": 0.850925191223015, "reward": 3.5999999999999996, "episode_length": 96, "actions_taken": 55, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 906, "timestamp": "2025-08-04T19:14:24.196162", "role": "Impostor", "won": false, "win_rate_window": 0.7178017471074882, "reward": -1.086967792442992, "episode_length": 106, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 907, "timestamp": "2025-08-04T19:14:24.196163", "role": "Scientist", "won": true, "win_rate_window": 0.9414859465984169, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 908, "timestamp": "2025-08-04T19:14:24.196164", "role": "Shapeshifter", "won": true, "win_rate_window": 0.846238711612671, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 59, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 909, "timestamp": "2025-08-04T19:14:24.196165", "role": "Engineer", "won": true, "win_rate_window": 0.8364191417995025, "reward": 3.5999999999999996, "episode_length": 75, "actions_taken": 41, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 910, "timestamp": "2025-08-04T19:14:24.196166", "role": "Scientist", "won": false, "win_rate_window": 0.805403820403838, "reward": -1.3995993994158524, "episode_length": 52, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 911, "timestamp": "2025-08-04T19:14:24.196167", "role": "Shapeshifter", "won": false, "win_rate_window": 0.9178520338213982, "reward": -1.5542514156857363, "episode_length": 111, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 912, "timestamp": "2025-08-04T19:14:24.196168", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 60, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 913, "timestamp": "2025-08-04T19:14:24.196169", "role": "Tracker", "won": false, "win_rate_window": 0.6345392720991258, "reward": 0.01962849313831434, "episode_length": 108, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 914, "timestamp": "2025-08-04T19:14:24.196170", "role": "Impostor", "won": true, "win_rate_window": 0.813602090974251, "reward": 3.5999999999999996, "episode_length": 86, "actions_taken": 46, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 915, "timestamp": "2025-08-04T19:14:24.196171", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 45, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 916, "timestamp": "2025-08-04T19:14:24.196172", "role": "Tracker", "won": true, "win_rate_window": 0.760555160265975, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 917, "timestamp": "2025-08-04T19:14:24.196173", "role": "Impostor", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 42, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 918, "timestamp": "2025-08-04T19:14:24.196175", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 919, "timestamp": "2025-08-04T19:14:24.196176", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 95, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 920, "timestamp": "2025-08-04T19:14:24.196177", "role": "Scientist", "won": true, "win_rate_window": 0.7958236654019195, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 921, "timestamp": "2025-08-04T19:14:24.196178", "role": "Guardian Angel", "won": true, "win_rate_window": 0.940046502073295, "reward": 3.5999999999999996, "episode_length": 84, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 922, "timestamp": "2025-08-04T19:14:24.196179", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7072070405531773, "reward": 0.4687712093920578, "episode_length": 75, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 923, "timestamp": "2025-08-04T19:14:24.196180", "role": "Tracker", "won": true, "win_rate_window": 0.9411223248212179, "reward": 3.5999999999999996, "episode_length": 115, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 924, "timestamp": "2025-08-04T19:14:24.196181", "role": "Shapeshifter", "won": false, "win_rate_window": 0.6284452863519631, "reward": -1.2809859513312865, "episode_length": 63, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 925, "timestamp": "2025-08-04T19:14:24.196182", "role": "Scientist", "won": true, "win_rate_window": 0.7468147208149972, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 926, "timestamp": "2025-08-04T19:14:24.196183", "role": "Guardian Angel", "won": false, "win_rate_window": 0.8130480843595514, "reward": -0.8084227973479613, "episode_length": 62, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 927, "timestamp": "2025-08-04T19:14:24.196184", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 85, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 928, "timestamp": "2025-08-04T19:14:24.196185", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7812962751516331, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 71, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 929, "timestamp": "2025-08-04T19:14:24.196186", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6229986181521123, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 930, "timestamp": "2025-08-04T19:14:24.196187", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 931, "timestamp": "2025-08-04T19:14:24.196188", "role": "Impostor", "won": true, "win_rate_window": 0.9029074085804849, "reward": 3.5999999999999996, "episode_length": 114, "actions_taken": 74, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 932, "timestamp": "2025-08-04T19:14:24.196189", "role": "Scientist", "won": true, "win_rate_window": 0.8236575700594211, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 933, "timestamp": "2025-08-04T19:14:24.196190", "role": "Scientist", "won": true, "win_rate_window": 0.7574353750159265, "reward": 3.5999999999999996, "episode_length": 81, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 934, "timestamp": "2025-08-04T19:14:24.196191", "role": "Phantom", "won": false, "win_rate_window": 0.7465826909597897, "reward": -2.1692896131246293, "episode_length": 104, "actions_taken": 44, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 935, "timestamp": "2025-08-04T19:14:24.196192", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 47, "actions_taken": 34, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 936, "timestamp": "2025-08-04T19:14:24.196193", "role": "Impostor", "won": true, "win_rate_window": 0.7343210096375556, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 78, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 937, "timestamp": "2025-08-04T19:14:24.196194", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6208116620785304, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 938, "timestamp": "2025-08-04T19:14:24.196196", "role": "Impostor", "won": true, "win_rate_window": 0.7880417325495308, "reward": 3.5999999999999996, "episode_length": 73, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 939, "timestamp": "2025-08-04T19:14:24.196197", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 54, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 940, "timestamp": "2025-08-04T19:14:24.196198", "role": "Scientist", "won": true, "win_rate_window": 0.86644392459715, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 941, "timestamp": "2025-08-04T19:14:24.196199", "role": "Impostor", "won": true, "win_rate_window": 0.660916001115236, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 942, "timestamp": "2025-08-04T19:14:24.196200", "role": "Engineer", "won": true, "win_rate_window": 0.9268959214781662, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 30, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 943, "timestamp": "2025-08-04T19:14:24.196201", "role": "Scientist", "won": true, "win_rate_window": 0.8882068695162878, "reward": 3.5999999999999996, "episode_length": 46, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 944, "timestamp": "2025-08-04T19:14:24.196202", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 945, "timestamp": "2025-08-04T19:14:24.196203", "role": "Crewmate", "won": true, "win_rate_window": 0.8739265681047671, "reward": 3.5999999999999996, "episode_length": 68, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 946, "timestamp": "2025-08-04T19:14:24.196204", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 102, "actions_taken": 40, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 947, "timestamp": "2025-08-04T19:14:24.196205", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 44, "actions_taken": 58, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 948, "timestamp": "2025-08-04T19:14:24.196206", "role": "Tracker", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 80, "actions_taken": 73, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 949, "timestamp": "2025-08-04T19:14:24.196207", "role": "Tracker", "won": true, "win_rate_window": 0.6100715155232854, "reward": 3.5999999999999996, "episode_length": 98, "actions_taken": 100, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 950, "timestamp": "2025-08-04T19:14:24.196208", "role": "Crewmate", "won": true, "win_rate_window": 0.732165612847864, "reward": 3.5999999999999996, "episode_length": 89, "actions_taken": 83, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 951, "timestamp": "2025-08-04T19:14:24.196209", "role": "Scientist", "won": true, "win_rate_window": 0.9267941002470124, "reward": 3.5999999999999996, "episode_length": 85, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 952, "timestamp": "2025-08-04T19:14:24.196210", "role": "Crewmate", "won": false, "win_rate_window": 0.838327076677997, "reward": -1.8889947975197747, "episode_length": 53, "actions_taken": 32, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 953, "timestamp": "2025-08-04T19:14:24.196211", "role": "Engineer", "won": true, "win_rate_window": 0.7704336202354833, "reward": 3.5999999999999996, "episode_length": 99, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 954, "timestamp": "2025-08-04T19:14:24.196212", "role": "Scientist", "won": false, "win_rate_window": 0.7155936905100719, "reward": -0.5012881583411106, "episode_length": 116, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 955, "timestamp": "2025-08-04T19:14:24.196213", "role": "Engineer", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 956, "timestamp": "2025-08-04T19:14:24.196214", "role": "Crewmate", "won": true, "win_rate_window": 0.6858674697165049, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 66, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 957, "timestamp": "2025-08-04T19:14:24.196215", "role": "Guardian Angel", "won": true, "win_rate_window": 0.7548515058471973, "reward": 3.5999999999999996, "episode_length": 70, "actions_taken": 65, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 958, "timestamp": "2025-08-04T19:14:24.196216", "role": "Impostor", "won": true, "win_rate_window": 0.7833671459984363, "reward": 3.5999999999999996, "episode_length": 62, "actions_taken": 56, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 959, "timestamp": "2025-08-04T19:14:24.196217", "role": "Impostor", "won": true, "win_rate_window": 0.7783390088056795, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 960, "timestamp": "2025-08-04T19:14:24.196219", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6060434415606406, "reward": 3.5999999999999996, "episode_length": 97, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 961, "timestamp": "2025-08-04T19:14:24.196220", "role": "Shapeshifter", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 118, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 962, "timestamp": "2025-08-04T19:14:24.196221", "role": "Tracker", "won": true, "win_rate_window": 0.8824857155545202, "reward": 3.5999999999999996, "episode_length": 103, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 963, "timestamp": "2025-08-04T19:14:24.196222", "role": "Engineer", "won": false, "win_rate_window": 0.8988513647737414, "reward": -1.2020746515394218, "episode_length": 72, "actions_taken": 62, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 964, "timestamp": "2025-08-04T19:14:24.196223", "role": "Phantom", "won": true, "win_rate_window": 0.8707068644002814, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 89, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 965, "timestamp": "2025-08-04T19:14:24.196224", "role": "Engineer", "won": true, "win_rate_window": 0.9219778689508459, "reward": 3.5999999999999996, "episode_length": 116, "actions_taken": 64, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 966, "timestamp": "2025-08-04T19:14:24.196225", "role": "Tracker", "won": true, "win_rate_window": 0.8276454024507552, "reward": 3.5999999999999996, "episode_length": 66, "actions_taken": 48, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 967, "timestamp": "2025-08-04T19:14:24.196226", "role": "Engineer", "won": true, "win_rate_window": 0.7953212592221679, "reward": 3.5999999999999996, "episode_length": 65, "actions_taken": 60, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 968, "timestamp": "2025-08-04T19:14:24.196227", "role": "Tracker", "won": true, "win_rate_window": 0.7819560854818911, "reward": 3.5999999999999996, "episode_length": 57, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 969, "timestamp": "2025-08-04T19:14:24.196228", "role": "Scientist", "won": false, "win_rate_window": 0.6838848002854087, "reward": -1.030332596414959, "episode_length": 48, "actions_taken": 94, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 970, "timestamp": "2025-08-04T19:14:24.196229", "role": "Tracker", "won": true, "win_rate_window": 0.9347722370649142, "reward": 3.5999999999999996, "episode_length": 108, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 971, "timestamp": "2025-08-04T19:14:24.196230", "role": "Guardian Angel", "won": false, "win_rate_window": 0.7468436144384039, "reward": -2.043351633043359, "episode_length": 68, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 972, "timestamp": "2025-08-04T19:14:24.196231", "role": "Scientist", "won": true, "win_rate_window": 0.8442339227302381, "reward": 3.5999999999999996, "episode_length": 56, "actions_taken": 99, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 973, "timestamp": "2025-08-04T19:14:24.196233", "role": "Guardian Angel", "won": true, "win_rate_window": 0.768861231390691, "reward": 3.5999999999999996, "episode_length": 64, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 974, "timestamp": "2025-08-04T19:14:24.196234", "role": "Engineer", "won": false, "win_rate_window": 0.8790572216527499, "reward": -1.536121183626361, "episode_length": 80, "actions_taken": 84, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 975, "timestamp": "2025-08-04T19:14:24.196235", "role": "Phantom", "won": true, "win_rate_window": 0.5503191125908615, "reward": 3.5999999999999996, "episode_length": 78, "actions_taken": 86, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 976, "timestamp": "2025-08-04T19:14:24.196237", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 100, "actions_taken": 51, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 977, "timestamp": "2025-08-04T19:14:24.196238", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7364230382338475, "reward": -1.641255620414915, "episode_length": 89, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 978, "timestamp": "2025-08-04T19:14:24.196239", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7567785817114702, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 70, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 979, "timestamp": "2025-08-04T19:14:24.196240", "role": "Shapeshifter", "won": false, "win_rate_window": 0.8971431112399811, "reward": -1.718542328683032, "episode_length": 79, "actions_taken": 33, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 980, "timestamp": "2025-08-04T19:14:24.196245", "role": "Phantom", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 63, "actions_taken": 93, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 981, "timestamp": "2025-08-04T19:14:24.196246", "role": "Scientist", "won": true, "win_rate_window": 0.8616819820568815, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 87, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 982, "timestamp": "2025-08-04T19:14:24.196247", "role": "Crewmate", "won": true, "win_rate_window": 0.8034601073712052, "reward": 3.5999999999999996, "episode_length": 90, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 983, "timestamp": "2025-08-04T19:14:24.196248", "role": "Impostor", "won": true, "win_rate_window": 0.8574744541599977, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 97, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 984, "timestamp": "2025-08-04T19:14:24.196249", "role": "Scientist", "won": true, "win_rate_window": 0.8874667168921275, "reward": 3.5999999999999996, "episode_length": 58, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 985, "timestamp": "2025-08-04T19:14:24.196250", "role": "Phantom", "won": true, "win_rate_window": 0.837513527535029, "reward": 3.5999999999999996, "episode_length": 105, "actions_taken": 35, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 986, "timestamp": "2025-08-04T19:14:24.196251", "role": "Impostor", "won": true, "win_rate_window": 0.6376256186739371, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 67, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 987, "timestamp": "2025-08-04T19:14:24.196252", "role": "Tracker", "won": true, "win_rate_window": 0.7726471489089147, "reward": 3.5999999999999996, "episode_length": 69, "actions_taken": 47, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 988, "timestamp": "2025-08-04T19:14:24.196253", "role": "Shapeshifter", "won": false, "win_rate_window": 0.7134271834419965, "reward": -1.0811984589770245, "episode_length": 106, "actions_taken": 69, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 989, "timestamp": "2025-08-04T19:14:24.196255", "role": "Scientist", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 49, "actions_taken": 57, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 990, "timestamp": "2025-08-04T19:14:24.196256", "role": "Phantom", "won": true, "win_rate_window": 0.6605139539412243, "reward": 3.5999999999999996, "episode_length": 101, "actions_taken": 38, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 991, "timestamp": "2025-08-04T19:14:24.196257", "role": "Crewmate", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 51, "actions_taken": 90, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 992, "timestamp": "2025-08-04T19:14:24.196258", "role": "Engineer", "won": true, "win_rate_window": 0.9457069183049909, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 63, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 993, "timestamp": "2025-08-04T19:14:24.196259", "role": "Guardian Angel", "won": true, "win_rate_window": 0.95, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 96, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 994, "timestamp": "2025-08-04T19:14:24.196260", "role": "Shapeshifter", "won": true, "win_rate_window": 0.7275943170550443, "reward": 3.5999999999999996, "episode_length": 106, "actions_taken": 98, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 995, "timestamp": "2025-08-04T19:14:24.196261", "role": "Shapeshifter", "won": true, "win_rate_window": 0.6141930436442651, "reward": 3.5999999999999996, "episode_length": 41, "actions_taken": 92, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 996, "timestamp": "2025-08-04T19:14:24.196263", "role": "Engineer", "won": true, "win_rate_window": 0.6846119196457298, "reward": 3.5999999999999996, "episode_length": 48, "actions_taken": 39, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 997, "timestamp": "2025-08-04T19:14:24.196264", "role": "Shapeshifter", "won": true, "win_rate_window": 0.909554556047595, "reward": 3.5999999999999996, "episode_length": 76, "actions_taken": 72, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 998, "timestamp": "2025-08-04T19:14:24.196265", "role": "Scientist", "won": false, "win_rate_window": 0.95, "reward": -2.0262837712012387, "episode_length": 76, "actions_taken": 79, "training_mode": "self_play", "complexity_level": "advanced"}, {"episode": 999, "timestamp": "2025-08-04T19:14:24.196266", "role": "Tracker", "won": true, "win_rate_window": 0.9380056591863313, "reward": 3.5999999999999996, "episode_length": 82, "actions_taken": 43, "training_mode": "self_play", "complexity_level": "advanced"}], "thresholds": {"mastery_win_rate": 0.7, "plateau_episodes": 200, "min_improvement": 0.05}, "last_updated": "2025-08-04T19:14:24.194967"}