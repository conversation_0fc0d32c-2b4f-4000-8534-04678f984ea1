"""
Game state detection for Among Us computer vision.

Detects different game states (lobby, gameplay, meetings, tasks)
and extracts relevant game information from screenshots.
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class GameState(Enum):
    """Possible game states in Among Us."""
    UNKNOWN = "unknown"
    MAIN_MENU = "main_menu"
    LOBBY = "lobby"
    GAMEPLAY = "gameplay"
    MEETING = "meeting"
    TASK = "task"
    VOTING = "voting"
    GAME_OVER = "game_over"
    LOADING = "loading"


@dataclass
class GameStateInfo:
    """Information about current game state."""
    state: GameState
    confidence: float
    details: Dict[str, Any]
    timestamp: float


class GameStateDetector:
    """Detects game states and extracts game information from screenshots."""
    
    def __init__(self):
        """Initialize game state detector."""
        self.current_state = GameState.UNKNOWN
        self.state_history = []
        self.confidence_threshold = 0.7
        
        # Template matching for UI elements
        self.ui_templates = {}
        self.color_ranges = self._initialize_color_ranges()
        
        # State detection parameters
        self.detection_params = {
            'lobby': {
                'start_button_color': [(0, 150, 0), (100, 255, 100)],  # Green start button
                'lobby_code_area': (0.1, 0.1, 0.4, 0.2),  # Relative coordinates
            },
            'gameplay': {
                'task_bar_color': [(200, 200, 0), (255, 255, 100)],  # Yellow task bar
                'minimap_area': (0.02, 0.02, 0.25, 0.25),
            },
            'meeting': {
                'discussion_bg_color': [(20, 20, 40), (60, 60, 100)],  # Dark blue meeting bg
                'player_icons_area': (0.1, 0.3, 0.9, 0.8),
            },
            'voting': {
                'vote_button_color': [(100, 0, 0), (200, 50, 50)],  # Red vote buttons
                'voting_area': (0.2, 0.4, 0.8, 0.9),
            }
        }
    
    def _initialize_color_ranges(self) -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
        """Initialize color ranges for different UI elements."""
        return {
            # Among Us specific colors
            'red_player': (np.array([0, 120, 120]), np.array([10, 255, 255])),
            'blue_player': (np.array([100, 120, 120]), np.array([130, 255, 255])),
            'green_player': (np.array([40, 120, 120]), np.array([80, 255, 255])),
            'yellow_player': (np.array([20, 120, 120]), np.array([30, 255, 255])),
            'orange_player': (np.array([10, 120, 120]), np.array([20, 255, 255])),
            'purple_player': (np.array([130, 120, 120]), np.array([160, 255, 255])),
            'pink_player': (np.array([160, 120, 120]), np.array([180, 255, 255])),
            'white_player': (np.array([0, 0, 200]), np.array([180, 30, 255])),
            'black_player': (np.array([0, 0, 0]), np.array([180, 255, 50])),
            'brown_player': (np.array([10, 100, 100]), np.array([20, 255, 200])),
            'cyan_player': (np.array([80, 120, 120]), np.array([100, 255, 255])),
            'lime_player': (np.array([60, 120, 120]), np.array([80, 255, 255])),
            
            # UI elements
            'emergency_button': (np.array([0, 0, 150]), np.array([10, 100, 255])),
            'task_highlight': (np.array([20, 200, 200]), np.array([30, 255, 255])),
            'vent_highlight': (np.array([0, 0, 100]), np.array([180, 100, 200])),
        }
    
    def detect_game_state(self, frame: np.ndarray) -> GameStateInfo:
        """
        Detect the current game state from a frame.
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            GameStateInfo: Detected game state with confidence and details
        """
        try:
            import time
            timestamp = time.time()
            
            # Convert to HSV for better color detection
            hsv_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Try to detect each possible state
            state_confidences = {}
            
            # Check for main menu
            state_confidences[GameState.MAIN_MENU] = self._detect_main_menu(frame, hsv_frame)
            
            # Check for lobby
            state_confidences[GameState.LOBBY] = self._detect_lobby(frame, hsv_frame)
            
            # Check for gameplay
            state_confidences[GameState.GAMEPLAY] = self._detect_gameplay(frame, hsv_frame)
            
            # Check for meeting/discussion
            state_confidences[GameState.MEETING] = self._detect_meeting(frame, hsv_frame)
            
            # Check for voting
            state_confidences[GameState.VOTING] = self._detect_voting(frame, hsv_frame)
            
            # Check for task interface
            state_confidences[GameState.TASK] = self._detect_task_interface(frame, hsv_frame)
            
            # Check for game over
            state_confidences[GameState.GAME_OVER] = self._detect_game_over(frame, hsv_frame)
            
            # Determine best state
            best_state = max(state_confidences.items(), key=lambda x: x[1])
            detected_state, confidence = best_state
            
            # If confidence is too low, mark as unknown
            if confidence < self.confidence_threshold:
                detected_state = GameState.UNKNOWN
                confidence = 0.0
            
            # Extract additional details based on detected state
            details = self._extract_state_details(detected_state, frame, hsv_frame)
            
            # Create state info
            state_info = GameStateInfo(
                state=detected_state,
                confidence=confidence,
                details=details,
                timestamp=timestamp
            )
            
            # Update history
            self.state_history.append(state_info)
            if len(self.state_history) > 100:  # Keep last 100 states
                self.state_history.pop(0)
            
            self.current_state = detected_state
            return state_info
            
        except Exception as e:
            logger.error(f"Error detecting game state: {e}")
            return GameStateInfo(
                state=GameState.UNKNOWN,
                confidence=0.0,
                details={},
                timestamp=time.time()
            )
    
    def _detect_main_menu(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect main menu state."""
        try:
            # Look for "PLAY" button or Among Us logo
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Check for typical main menu elements
            # Large text areas (logo, buttons)
            text_areas = self._find_text_regions(gray)
            
            # Main menu typically has fewer, larger text elements
            if len(text_areas) < 10 and any(area > 5000 for area in text_areas):
                return 0.8
            
            return 0.1
            
        except Exception as e:
            logger.error(f"Error detecting main menu: {e}")
            return 0.0
    
    def _detect_lobby(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect lobby state."""
        try:
            confidence = 0.0
            
            # Look for lobby code area (top-left)
            h, w = frame.shape[:2]
            lobby_area = frame[int(h*0.05):int(h*0.25), int(w*0.05):int(w*0.45)]
            
            # Check for text in lobby code area
            gray_lobby = cv2.cvtColor(lobby_area, cv2.COLOR_BGR2GRAY)
            text_density = np.sum(gray_lobby < 100) / gray_lobby.size
            
            if text_density > 0.1:  # Significant text presence
                confidence += 0.4
            
            # Look for start button (green button)
            green_mask = cv2.inRange(hsv_frame, 
                                   np.array([40, 100, 100]), 
                                   np.array([80, 255, 255]))
            green_pixels = np.sum(green_mask > 0)
            
            if green_pixels > 1000:  # Significant green area (start button)
                confidence += 0.4
            
            # Look for player list area
            player_area = frame[int(h*0.3):int(h*0.8), int(w*0.1):int(w*0.9)]
            # Players in lobby typically show as colored rectangles
            colored_regions = self._count_colored_regions(player_area)
            
            if colored_regions > 2:  # Multiple players visible
                confidence += 0.3
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error detecting lobby: {e}")
            return 0.0
    
    def _detect_gameplay(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect gameplay state."""
        try:
            confidence = 0.0
            h, w = frame.shape[:2]
            
            # Look for minimap (top-left corner)
            minimap_area = frame[int(h*0.02):int(h*0.25), int(w*0.02):int(w*0.25)]
            
            # Minimap has distinctive blue/gray colors
            minimap_hsv = cv2.cvtColor(minimap_area, cv2.COLOR_BGR2HSV)
            blue_mask = cv2.inRange(minimap_hsv, 
                                  np.array([100, 50, 50]), 
                                  np.array([130, 255, 255]))
            
            if np.sum(blue_mask > 0) > 500:  # Minimap present
                confidence += 0.5
            
            # Look for task bar (yellow/orange bar at top)
            task_bar_area = frame[int(h*0.02):int(h*0.15), int(w*0.3):int(w*0.7)]
            task_bar_hsv = cv2.cvtColor(task_bar_area, cv2.COLOR_BGR2HSV)
            yellow_mask = cv2.inRange(task_bar_hsv,
                                    np.array([20, 100, 100]),
                                    np.array([30, 255, 255]))
            
            if np.sum(yellow_mask > 0) > 200:  # Task bar present
                confidence += 0.4
            
            # Look for use button or interact prompts
            use_button_area = frame[int(h*0.7):int(h*0.9), int(w*0.4):int(w*0.6)]
            # Use buttons are typically white/light colored
            gray_use = cv2.cvtColor(use_button_area, cv2.COLOR_BGR2GRAY)
            bright_pixels = np.sum(gray_use > 200)
            
            if bright_pixels > 100:  # Bright UI elements present
                confidence += 0.2
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error detecting gameplay: {e}")
            return 0.0
    
    def _detect_meeting(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect meeting/discussion state."""
        try:
            confidence = 0.0
            h, w = frame.shape[:2]
            
            # Meeting has distinctive dark blue background
            dark_blue_mask = cv2.inRange(hsv_frame,
                                       np.array([100, 100, 20]),
                                       np.array([130, 255, 100]))
            
            dark_blue_ratio = np.sum(dark_blue_mask > 0) / (h * w)
            
            if dark_blue_ratio > 0.3:  # Significant dark blue background
                confidence += 0.6
            
            # Look for player discussion icons (circular player representations)
            player_area = frame[int(h*0.2):int(h*0.8), int(w*0.1):int(w*0.9)]
            circles = self._detect_circular_regions(player_area)
            
            if len(circles) >= 4:  # Multiple player icons
                confidence += 0.4
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error detecting meeting: {e}")
            return 0.0
    
    def _detect_voting(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect voting state."""
        try:
            confidence = 0.0
            h, w = frame.shape[:2]
            
            # Look for vote buttons (red buttons next to player names)
            red_mask = cv2.inRange(hsv_frame,
                                 np.array([0, 100, 100]),
                                 np.array([10, 255, 255]))
            
            # Count red button-like regions
            contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            button_like_contours = [c for c in contours if 100 < cv2.contourArea(c) < 2000]
            
            if len(button_like_contours) >= 3:  # Multiple vote buttons
                confidence += 0.7
            
            # Look for voting interface elements
            voting_area = frame[int(h*0.3):int(h*0.9), int(w*0.2):int(w*0.8)]
            text_regions = self._find_text_regions(cv2.cvtColor(voting_area, cv2.COLOR_BGR2GRAY))
            
            if len(text_regions) > 5:  # Player names and voting info
                confidence += 0.3
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error detecting voting: {e}")
            return 0.0
    
    def _detect_task_interface(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect task interface state."""
        try:
            # Task interfaces typically have distinctive UI elements
            # and take up most of the screen
            
            # Look for task-specific colors and patterns
            # This is a simplified detection - would need specific task templates
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # Task interfaces typically have many UI elements (high edge density)
            if edge_density > 0.1:
                return 0.6
            
            return 0.1
            
        except Exception as e:
            logger.error(f"Error detecting task interface: {e}")
            return 0.0
    
    def _detect_game_over(self, frame: np.ndarray, hsv_frame: np.ndarray) -> float:
        """Detect game over state."""
        try:
            # Look for victory/defeat screens
            # These typically have large text and distinctive colors
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Look for large text regions (victory/defeat messages)
            text_areas = self._find_text_regions(gray)
            large_text_areas = [area for area in text_areas if area > 10000]
            
            if len(large_text_areas) > 0:
                return 0.8
            
            return 0.1
            
        except Exception as e:
            logger.error(f"Error detecting game over: {e}")
            return 0.0

    def _extract_state_details(self, state: GameState, frame: np.ndarray, hsv_frame: np.ndarray) -> Dict[str, Any]:
        """Extract additional details based on detected state."""
        details = {}

        try:
            if state == GameState.LOBBY:
                details.update(self._extract_lobby_details(frame, hsv_frame))
            elif state == GameState.GAMEPLAY:
                details.update(self._extract_gameplay_details(frame, hsv_frame))
            elif state == GameState.MEETING:
                details.update(self._extract_meeting_details(frame, hsv_frame))
            elif state == GameState.VOTING:
                details.update(self._extract_voting_details(frame, hsv_frame))

        except Exception as e:
            logger.error(f"Error extracting state details: {e}")

        return details

    def _extract_lobby_details(self, frame: np.ndarray, hsv_frame: np.ndarray) -> Dict[str, Any]:
        """Extract lobby-specific details."""
        details = {}
        h, w = frame.shape[:2]

        # Try to detect number of players
        player_area = frame[int(h*0.3):int(h*0.8), int(w*0.1):int(w*0.9)]
        player_count = self._count_colored_regions(player_area)
        details['estimated_player_count'] = player_count

        # Check if start button is available (host privileges)
        green_mask = cv2.inRange(hsv_frame, np.array([40, 100, 100]), np.array([80, 255, 255]))
        details['can_start'] = np.sum(green_mask > 0) > 1000

        return details

    def _extract_gameplay_details(self, frame: np.ndarray, hsv_frame: np.ndarray) -> Dict[str, Any]:
        """Extract gameplay-specific details."""
        details = {}
        h, w = frame.shape[:2]

        # Check for emergency button availability
        center_area = frame[int(h*0.4):int(h*0.6), int(w*0.4):int(w*0.6)]
        emergency_available = self._detect_emergency_button(center_area)
        details['emergency_available'] = emergency_available

        # Check for task highlights
        task_highlights = self._detect_task_highlights(frame, hsv_frame)
        details['task_locations'] = task_highlights

        # Estimate player positions (simplified)
        player_positions = self._estimate_player_positions(frame, hsv_frame)
        details['visible_players'] = player_positions

        return details

    def _extract_meeting_details(self, frame: np.ndarray, hsv_frame: np.ndarray) -> Dict[str, Any]:
        """Extract meeting-specific details."""
        details = {}
        h, w = frame.shape[:2]

        # Count visible players in meeting
        player_area = frame[int(h*0.2):int(h*0.8), int(w*0.1):int(w*0.9)]
        circles = self._detect_circular_regions(player_area)
        details['players_in_meeting'] = len(circles)

        return details

    def _extract_voting_details(self, frame: np.ndarray, hsv_frame: np.ndarray) -> Dict[str, Any]:
        """Extract voting-specific details."""
        details = {}

        # Count available vote buttons
        red_mask = cv2.inRange(hsv_frame, np.array([0, 100, 100]), np.array([10, 255, 255]))
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        vote_buttons = [c for c in contours if 100 < cv2.contourArea(c) < 2000]
        details['available_votes'] = len(vote_buttons)

        return details

    def _find_text_regions(self, gray_image: np.ndarray) -> List[int]:
        """Find text regions in grayscale image and return their areas."""
        try:
            # Use morphological operations to find text-like regions
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

            # Threshold to get text regions
            _, thresh = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Morphological operations to connect text
            morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)

            # Find contours
            contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Return areas of text-like regions
            areas = [cv2.contourArea(c) for c in contours if cv2.contourArea(c) > 50]
            return areas

        except Exception as e:
            logger.error(f"Error finding text regions: {e}")
            return []

    def _count_colored_regions(self, image: np.ndarray) -> int:
        """Count distinct colored regions (players) in image."""
        try:
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Count regions for each player color
            total_regions = 0

            for color_name, (lower, upper) in self.color_ranges.items():
                if 'player' in color_name:
                    mask = cv2.inRange(hsv, lower, upper)
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    # Count significant colored regions
                    significant_contours = [c for c in contours if cv2.contourArea(c) > 200]
                    total_regions += len(significant_contours)

            return total_regions

        except Exception as e:
            logger.error(f"Error counting colored regions: {e}")
            return 0

    def _detect_circular_regions(self, image: np.ndarray) -> List[Tuple[int, int, int]]:
        """Detect circular regions (player icons) in image."""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Use HoughCircles to detect circular regions
            circles = cv2.HoughCircles(
                gray,
                cv2.HOUGH_GRADIENT,
                dp=1,
                minDist=30,
                param1=50,
                param2=30,
                minRadius=10,
                maxRadius=50
            )

            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                return [(x, y, r) for x, y, r in circles]

            return []

        except Exception as e:
            logger.error(f"Error detecting circular regions: {e}")
            return []

    def _detect_emergency_button(self, center_area: np.ndarray) -> bool:
        """Detect if emergency button is available."""
        try:
            hsv = cv2.cvtColor(center_area, cv2.COLOR_BGR2HSV)

            # Emergency button is typically red
            red_mask = cv2.inRange(hsv, np.array([0, 100, 100]), np.array([10, 255, 255]))

            # Look for circular red region
            contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # Reasonable button size
                    # Check if roughly circular
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        if circularity > 0.5:  # Reasonably circular
                            return True

            return False

        except Exception as e:
            logger.error(f"Error detecting emergency button: {e}")
            return False

    def _detect_task_highlights(self, frame: np.ndarray, hsv_frame: np.ndarray) -> List[Tuple[int, int]]:
        """Detect task highlight locations."""
        try:
            # Task highlights are typically yellow/orange
            task_mask = cv2.inRange(hsv_frame,
                                  np.array([20, 200, 200]),
                                  np.array([30, 255, 255]))

            contours, _ = cv2.findContours(task_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            task_locations = []
            for contour in contours:
                if cv2.contourArea(contour) > 100:  # Significant highlight
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        task_locations.append((cx, cy))

            return task_locations

        except Exception as e:
            logger.error(f"Error detecting task highlights: {e}")
            return []

    def _estimate_player_positions(self, frame: np.ndarray, hsv_frame: np.ndarray) -> List[Dict[str, Any]]:
        """Estimate positions of visible players."""
        try:
            players = []

            # Look for each player color
            for color_name, (lower, upper) in self.color_ranges.items():
                if 'player' in color_name:
                    mask = cv2.inRange(hsv_frame, lower, upper)
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    for contour in contours:
                        area = cv2.contourArea(contour)
                        if 200 < area < 5000:  # Reasonable player size
                            M = cv2.moments(contour)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])

                                players.append({
                                    'color': color_name.replace('_player', ''),
                                    'position': (cx, cy),
                                    'area': area
                                })

            return players

        except Exception as e:
            logger.error(f"Error estimating player positions: {e}")
            return []

    def get_state_history(self, last_n: int = 10) -> List[GameStateInfo]:
        """Get recent state history."""
        return self.state_history[-last_n:] if self.state_history else []

    def is_state_stable(self, required_duration: float = 2.0) -> bool:
        """Check if current state has been stable for required duration."""
        if len(self.state_history) < 2:
            return False

        current_time = self.state_history[-1].timestamp
        current_state = self.state_history[-1].state

        for state_info in reversed(self.state_history):
            if state_info.state != current_state:
                break
            if current_time - state_info.timestamp >= required_duration:
                return True

        return False
