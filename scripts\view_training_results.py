#!/usr/bin/env python3
"""
View and analyze Among Us AI training results
"""

import os
import json
import glob
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np

def find_latest_results():
    """Find the most recent training results"""
    
    # Look for analytics directories
    analytics_dirs = [
        "training_analytics",
        "cpu_training_analytics", 
        "analytics_output"
    ]
    
    latest_files = {}
    
    for analytics_dir in analytics_dirs:
        if os.path.exists(analytics_dir):
            # Find latest report
            reports = glob.glob(os.path.join(analytics_dir, "training_report_*.json"))
            if reports:
                latest_report = max(reports, key=os.path.getctime)
                latest_files['report'] = latest_report
            
            # Find latest visualization
            visualizations = glob.glob(os.path.join(analytics_dir, "training_visualization_*.png"))
            if visualizations:
                latest_viz = max(visualizations, key=os.path.getctime)
                latest_files['visualization'] = latest_viz
    
    # Look for model directories
    model_dirs = glob.glob("models/ultimate_agent_*")
    if model_dirs:
        latest_model_dir = max(model_dirs, key=os.path.getctime)
        latest_files['model_dir'] = latest_model_dir
    
    return latest_files

def display_training_summary(report_path: str):
    """Display training summary from report"""
    
    try:
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        print("🎯 TRAINING SUMMARY")
        print("=" * 50)
        
        # Training summary
        if 'training_summary' in report:
            summary = report['training_summary']
            print(f"📊 Total Episodes: {summary.get('total_episodes', 'N/A')}")
            print(f"⏱️  Training Duration: {summary.get('training_duration', 'N/A')}")
            print(f"🎮 Current Performance: {summary.get('current_performance', 0):.1%}")
            
            # Role statistics
            if 'role_statistics' in summary:
                print(f"\n🎭 ROLE PERFORMANCE:")
                for role, stats in summary['role_statistics'].items():
                    print(f"   {role}:")
                    print(f"      Win Rate: {stats.get('win_rate', 0):.1%}")
                    print(f"      Recent Win Rate: {stats.get('recent_win_rate', 0):.1%}")
                    print(f"      Total Games: {stats.get('total_games', 0)}")
                    trend = stats.get('win_rate_trend', 0)
                    trend_arrow = "📈" if trend > 0 else "📉" if trend < 0 else "➡️"
                    print(f"      Trend: {trend_arrow} {trend:.3f}")
        
        # Performance analysis
        if 'performance_analysis' in report:
            perf = report['performance_analysis']
            print(f"\n📈 PERFORMANCE ANALYSIS:")
            
            if 'performance_by_opponent' in perf:
                print(f"   Performance vs Opponents:")
                for opponent, stats in perf['performance_by_opponent'].items():
                    print(f"      vs {opponent}: {stats.get('win_rate', 0):.1%} ({stats.get('total_games', 0)} games)")
        
        # Curriculum analysis
        if 'curriculum_analysis' in report:
            curriculum = report['curriculum_analysis']
            print(f"\n🎓 CURRICULUM PROGRESS:")
            print(f"   Current Stage: {curriculum.get('current_stage', 'Unknown')}")
            print(f"   Stages Completed: {curriculum.get('stages_completed', 0)}")
            print(f"   Learning Efficiency: {curriculum.get('learning_efficiency', 0):.2f}")
        
        # Social deduction analysis
        if 'social_deduction_analysis' in report:
            social = report['social_deduction_analysis']
            if 'deduction_accuracy' in social:
                accuracy = social['deduction_accuracy']
                print(f"\n🕵️ SOCIAL DEDUCTION:")
                print(f"   Current Accuracy: {accuracy.get('current', 0):.1%}")
                print(f"   Overall Accuracy: {accuracy.get('overall', 0):.1%}")
                trend = accuracy.get('trend', 0)
                trend_arrow = "📈" if trend > 0 else "📉" if trend < 0 else "➡️"
                print(f"   Trend: {trend_arrow} {trend:.3f}")
        
        # Recommendations
        if 'recommendations' in report:
            recommendations = report['recommendations']
            if recommendations:
                print(f"\n💡 RECOMMENDATIONS:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"   {i}. {rec}")
        
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error reading report: {e}")

def display_model_info(model_dir: str):
    """Display model information"""
    
    print(f"\n🤖 MODEL INFORMATION")
    print("=" * 30)
    print(f"📁 Model Directory: {model_dir}")
    
    # Check for config
    config_path = os.path.join(model_dir, "config.json")
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            print(f"⚙️  Configuration:")
            print(f"   Episodes: {config.get('total_episodes', 'N/A')}")
            print(f"   Learning Rate: {config.get('learning_rate', 'N/A')}")
            print(f"   Device: {config.get('device', 'N/A')}")
            print(f"   LLM Enabled: {config.get('enable_llm', False)}")
        except:
            print("⚠️  Could not read config file")
    
    # Check for model files
    model_files = glob.glob(os.path.join(model_dir, "*.pt"))
    if model_files:
        print(f"💾 Model Files:")
        for model_file in model_files:
            filename = os.path.basename(model_file)
            size_mb = os.path.getsize(model_file) / (1024 * 1024)
            print(f"   {filename} ({size_mb:.1f} MB)")
    
    # Check for curriculum files
    curriculum_files = glob.glob(os.path.join(model_dir, "curriculum_*.json"))
    if curriculum_files:
        latest_curriculum = max(curriculum_files, key=os.path.getctime)
        try:
            with open(latest_curriculum, 'r') as f:
                curriculum = json.load(f)
            print(f"🎓 Latest Curriculum State:")
            print(f"   Stage: {curriculum.get('current_stage', 'Unknown')}")
            print(f"   Episodes: {curriculum.get('episodes_completed', 0)}")
            print(f"   Performance: {curriculum.get('recent_performance', 0):.1%}")
        except:
            print("⚠️  Could not read curriculum file")

def show_visualization_info(viz_path: str):
    """Show information about visualization"""
    
    print(f"\n📊 VISUALIZATION")
    print("=" * 20)
    print(f"📈 Chart: {viz_path}")
    
    if os.path.exists(viz_path):
        size_mb = os.path.getsize(viz_path) / (1024 * 1024)
        mod_time = datetime.fromtimestamp(os.path.getmtime(viz_path))
        print(f"📅 Created: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💾 Size: {size_mb:.1f} MB")
        print(f"🖼️  To view: Open {viz_path} in image viewer")
    else:
        print("❌ Visualization file not found")

def create_quick_summary_chart(report_path: str):
    """Create a quick summary chart from report data"""
    
    try:
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        # Extract win rates
        win_rates = {}
        if 'training_summary' in report and 'role_statistics' in report['training_summary']:
            for role, stats in report['training_summary']['role_statistics'].items():
                win_rates[role] = stats.get('win_rate', 0)
        
        if win_rates:
            # Create simple bar chart
            plt.figure(figsize=(10, 6))
            
            roles = list(win_rates.keys())
            rates = [win_rates[role] * 100 for role in roles]  # Convert to percentage
            
            colors = ['#4CAF50' if rate >= 50 else '#FF5722' for rate in rates]
            bars = plt.bar(roles, rates, color=colors, alpha=0.7)
            
            plt.title('Among Us AI Training Results - Win Rates by Role', fontsize=16, fontweight='bold')
            plt.ylabel('Win Rate (%)', fontsize=12)
            plt.xlabel('Role', fontsize=12)
            plt.ylim(0, 100)
            
            # Add value labels on bars
            for bar, rate in zip(bars, rates):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                        f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
            
            # Add horizontal line at 50%
            plt.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='50% baseline')
            plt.legend()
            
            plt.tight_layout()
            
            # Save chart
            chart_path = "training_summary_chart.png"
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"📊 Quick summary chart saved: {chart_path}")
            return chart_path
            
    except Exception as e:
        print(f"⚠️  Could not create summary chart: {e}")
        return None

def main():
    print("🔍 Among Us AI Training Results Viewer")
    print("=" * 60)
    
    # Find latest results
    latest_files = find_latest_results()
    
    if not latest_files:
        print("❌ No training results found!")
        print("\n💡 To generate results, run:")
        print("   python train_cpu_safe.py --episodes 50")
        print("   python train_ultimate_agent.py --episodes 100")
        return
    
    print(f"📁 Found {len(latest_files)} result files:")
    for file_type, path in latest_files.items():
        print(f"   {file_type}: {path}")
    
    # Display report if available
    if 'report' in latest_files:
        display_training_summary(latest_files['report'])
        
        # Create quick chart
        chart_path = create_quick_summary_chart(latest_files['report'])
    
    # Display model info if available
    if 'model_dir' in latest_files:
        display_model_info(latest_files['model_dir'])
    
    # Display visualization info if available
    if 'visualization' in latest_files:
        show_visualization_info(latest_files['visualization'])
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. 📊 View detailed charts: Open visualization PNG files")
    print(f"2. 📈 Continue training: Run more episodes to improve performance")
    print(f"3. 🎮 Test agent: Use trained model for gameplay")
    print(f"4. 🔧 Adjust parameters: Modify learning rate, curriculum, etc.")

if __name__ == "__main__":
    main()
