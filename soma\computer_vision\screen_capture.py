"""
Screen capture system for Among Us computer vision.

Handles window detection, screen capture, and image preprocessing
for real-time Among Us gameplay analysis.
"""

import cv2
import numpy as np
import mss
import pygetwindow as gw
from typing import Optional, Tuple, Dict, Any
import time
import logging
from PIL import Image

logger = logging.getLogger(__name__)


class ScreenCapture:
    """Robust screen capture system for Among Us game window."""
    
    def __init__(self, target_fps: int = 10):
        """
        Initialize screen capture system.
        
        Args:
            target_fps: Target frames per second for capture
        """
        self.target_fps = target_fps
        self.frame_interval = 1.0 / target_fps
        self.last_capture_time = 0
        
        # Screen capture backend
        self.sct = mss.mss()
        
        # Game window properties
        self.game_window = None
        self.window_region = None
        self.game_resolution = None
        
        # Capture settings
        self.capture_region = None
        self.scale_factor = 1.0
        
        # Performance tracking
        self.capture_stats = {
            'total_captures': 0,
            'failed_captures': 0,
            'avg_capture_time': 0.0
        }
        
    def find_among_us_window(self) -> bool:
        """
        Find and set the Among Us game window.
        
        Returns:
            bool: True if window found and configured successfully
        """
        try:
            # Common Among Us window titles
            window_titles = [
                "Among Us",
                "Among Us - Steam",
                "Among Us - Epic Games",
                "AmongUs.exe"
            ]
            
            for title in window_titles:
                windows = gw.getWindowsWithTitle(title)
                if windows:
                    self.game_window = windows[0]
                    logger.info(f"Found Among Us window: {title}")
                    return self._configure_capture_region()
            
            # Fallback: search for any window containing "Among"
            all_windows = gw.getAllWindows()
            for window in all_windows:
                if "among" in window.title.lower():
                    self.game_window = window
                    logger.info(f"Found potential Among Us window: {window.title}")
                    return self._configure_capture_region()
            
            logger.warning("Among Us window not found")
            return False
            
        except Exception as e:
            logger.error(f"Error finding Among Us window: {e}")
            return False
    
    def _configure_capture_region(self) -> bool:
        """Configure the capture region based on game window."""
        try:
            if not self.game_window:
                return False
            
            # Get window bounds
            left = self.game_window.left
            top = self.game_window.top
            width = self.game_window.width
            height = self.game_window.height
            
            # Validate window size
            if width < 100 or height < 100:
                logger.warning(f"Window too small: {width}x{height}")
                return False
            
            # Set capture region
            self.window_region = {
                'left': left,
                'top': top,
                'width': width,
                'height': height
            }
            
            # Determine game resolution and scaling
            self.game_resolution = (width, height)
            
            # Among Us common resolutions for scaling reference
            common_resolutions = [
                (1920, 1080),  # 1080p
                (1280, 720),   # 720p
                (1366, 768),   # Common laptop
                (1600, 900),   # 16:9 variant
            ]
            
            # Find best matching resolution for scaling
            best_match = min(common_resolutions, 
                           key=lambda res: abs(res[0] - width) + abs(res[1] - height))
            
            self.scale_factor = min(width / best_match[0], height / best_match[1])
            
            logger.info(f"Configured capture region: {width}x{height} at ({left}, {top})")
            logger.info(f"Scale factor: {self.scale_factor:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error configuring capture region: {e}")
            return False
    
    def capture_frame(self, preprocess: bool = True) -> Optional[np.ndarray]:
        """
        Capture a frame from the game window.
        
        Args:
            preprocess: Whether to apply preprocessing to the frame
            
        Returns:
            numpy.ndarray: Captured frame as BGR image, or None if failed
        """
        current_time = time.time()
        
        # Rate limiting
        if current_time - self.last_capture_time < self.frame_interval:
            return None
        
        try:
            start_time = time.time()
            
            # Ensure window is still valid
            if not self._validate_window():
                if not self.find_among_us_window():
                    return None
            
            # Capture screenshot
            screenshot = self.sct.grab(self.window_region)
            
            # Convert to numpy array
            frame = np.array(screenshot)
            
            # Convert BGRA to BGR (remove alpha channel)
            if frame.shape[2] == 4:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
            
            # Apply preprocessing if requested
            if preprocess:
                frame = self._preprocess_frame(frame)
            
            # Update stats
            capture_time = time.time() - start_time
            self.capture_stats['total_captures'] += 1
            self.capture_stats['avg_capture_time'] = (
                (self.capture_stats['avg_capture_time'] * (self.capture_stats['total_captures'] - 1) + 
                 capture_time) / self.capture_stats['total_captures']
            )
            
            self.last_capture_time = current_time
            return frame
            
        except Exception as e:
            logger.error(f"Error capturing frame: {e}")
            self.capture_stats['failed_captures'] += 1
            return None
    
    def _validate_window(self) -> bool:
        """Validate that the game window is still available and focused."""
        try:
            if not self.game_window:
                return False
            
            # Check if window still exists
            current_windows = gw.getAllWindows()
            window_exists = any(w.title == self.game_window.title for w in current_windows)
            
            if not window_exists:
                logger.warning("Game window no longer exists")
                return False
            
            # Update window position if it moved
            current_window = next((w for w in current_windows if w.title == self.game_window.title), None)
            if current_window:
                if (current_window.left != self.game_window.left or 
                    current_window.top != self.game_window.top or
                    current_window.width != self.game_window.width or
                    current_window.height != self.game_window.height):
                    
                    logger.info("Window position/size changed, reconfiguring...")
                    self.game_window = current_window
                    return self._configure_capture_region()
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating window: {e}")
            return False
    
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply preprocessing to captured frame.
        
        Args:
            frame: Raw captured frame
            
        Returns:
            numpy.ndarray: Preprocessed frame
        """
        try:
            # Resize to standard resolution if needed
            target_height = 720  # Standard processing resolution
            if frame.shape[0] != target_height:
                aspect_ratio = frame.shape[1] / frame.shape[0]
                target_width = int(target_height * aspect_ratio)
                frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_AREA)
            
            # Enhance contrast and brightness for better detection
            frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)
            
            # Reduce noise
            frame = cv2.bilateralFilter(frame, 5, 50, 50)
            
            return frame
            
        except Exception as e:
            logger.error(f"Error preprocessing frame: {e}")
            return frame
    
    def get_capture_stats(self) -> Dict[str, Any]:
        """Get capture performance statistics."""
        stats = self.capture_stats.copy()
        if stats['total_captures'] > 0:
            stats['success_rate'] = 1.0 - (stats['failed_captures'] / stats['total_captures'])
        else:
            stats['success_rate'] = 0.0
        return stats
    
    def reset_stats(self):
        """Reset capture statistics."""
        self.capture_stats = {
            'total_captures': 0,
            'failed_captures': 0,
            'avg_capture_time': 0.0
        }
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if hasattr(self, 'sct'):
                self.sct.close()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
