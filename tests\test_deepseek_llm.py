#!/usr/bin/env python3
"""
Test DeepSeek-R1 LLM communication for Among Us
"""

from core.llm_communication import OllamaClient, LLMCommunicationStrategy
from core.communication import Message, MessageType

def test_deepseek_communication():
    """Test DeepSeek-R1 with Among Us prompts"""
    print("🤖 Testing DeepSeek-R1 for Among Us Communication")
    print("=" * 60)
    
    try:
        # Create DeepSeek-R1 client
        client = OllamaClient(model="deepseek-r1:latest")
        
        # Test basic generation
        print("🧪 Testing Basic Generation...")
        test_prompt = """You are Red in Among Us. A meeting was called.

TASK: Generate a short Among Us chat message (maximum 50 characters).

CONTEXT:
- Recent deaths: Pink
- Other players: Blue, Green, Yellow

CONSTRAINTS:
- Maximum 50 characters
- Use Among Us locations: admin, electrical, medbay, weapons, cafeteria
- Use Among Us tasks: wires, card swipe, scan, asteroids, upload
- Be realistic and simple

EXAMPLES:
"i was doing wires in electrical"
"red sus"
"where was everyone?"
"doing scan in medbay"

Your message:"""
        
        response = client.generate(test_prompt, max_tokens=30)
        print(f"   Raw Response: '{response}'")
        print(f"   Length: {len(response)} characters")
        print(f"   Word Count: {len(response.split())} words")
        
        # Test with communication strategy
        print("\n🎮 Testing Communication Strategy...")
        
        # Test Crewmate
        crewmate_strategy = LLMCommunicationStrategy("Red", "Crewmate", client)
        
        game_state = {
            'living_players': ['Red', 'Blue', 'Green', 'Yellow'],
            'dead_players': ['Pink'],
        }
        
        discussion_context = {
            'recent_deaths': ['Pink'],
            'accusations': [],
            'alibis': []
        }
        
        print("   🔵 Crewmate Messages:")
        for i in range(3):
            messages = crewmate_strategy.generate_messages(game_state, discussion_context)
            if messages:
                msg = messages[0]
                print(f"      {i+1}. [{msg.message_type.value}] '{msg.content}' ({len(msg.content)} chars)")
            else:
                print(f"      {i+1}. No message generated")
        
        # Test Impostor
        impostor_strategy = LLMCommunicationStrategy("Blue", "Impostor", client)
        
        print("\n   🔴 Impostor Messages:")
        for i in range(3):
            messages = impostor_strategy.generate_messages(game_state, discussion_context)
            if messages:
                msg = messages[0]
                print(f"      {i+1}. [{msg.message_type.value}] '{msg.content}' ({len(msg.content)} chars)")
            else:
                print(f"      {i+1}. No message generated")
        
        # Test responses
        print("\n💬 Testing Response Generation...")
        
        # Create test messages to respond to
        test_messages = [
            Message("Green", MessageType.ACCUSATION, "red is sus i saw them vent", "Red", 0.8),
            Message("Yellow", MessageType.QUESTION, "where was everyone?", None, 0.7),
            Message("Blue", MessageType.ALIBI, "i was doing wires in electrical", None, 0.6),
        ]
        
        for i, test_msg in enumerate(test_messages):
            print(f"   Test {i+1}: {test_msg.sender} said '{test_msg.content}'")
            response = crewmate_strategy.respond_to_message(test_msg, game_state)
            if response:
                print(f"      Response: '{response.content}' ({len(response.content)} chars)")
            else:
                print(f"      No response generated")
        
        print("\n✅ DeepSeek-R1 Communication Test Complete!")
        
    except Exception as e:
        print(f"❌ DeepSeek-R1 test failed: {e}")
        print("Make sure DeepSeek-R1 is installed in Ollama:")
        print("   ollama pull deepseek-r1:latest")

def test_performance_comparison():
    """Compare DeepSeek-R1 vs fallback messages"""
    print("\n📊 Performance Comparison")
    print("-" * 40)
    
    try:
        client = OllamaClient(model="deepseek-r1:latest")
        strategy = LLMCommunicationStrategy("Red", "Crewmate", client)
        
        game_state = {'living_players': ['Red', 'Blue'], 'dead_players': ['Green']}
        discussion_context = {'recent_deaths': ['Green']}
        
        print("DeepSeek-R1 vs Fallback Messages:")
        
        # Test multiple generations
        llm_messages = []
        fallback_messages = []
        
        for i in range(5):
            # Try LLM generation
            messages = strategy.generate_messages(game_state, discussion_context)
            if messages and len(messages[0].content) > 0:
                llm_messages.append(messages[0].content)
            
            # Get fallback
            fallback = strategy._get_fallback_message(discussion_context)
            if fallback:
                fallback_messages.append(fallback.content)
        
        print(f"\nLLM Messages ({len(llm_messages)} generated):")
        for i, msg in enumerate(llm_messages):
            print(f"   {i+1}. '{msg}' ({len(msg)} chars)")
        
        print(f"\nFallback Messages ({len(fallback_messages)} generated):")
        for i, msg in enumerate(fallback_messages):
            print(f"   {i+1}. '{msg}' ({len(msg)} chars)")
        
        # Quality assessment
        print(f"\nQuality Assessment:")
        print(f"   LLM Success Rate: {len(llm_messages)}/5 ({len(llm_messages)*20}%)")
        print(f"   Average LLM Length: {sum(len(m) for m in llm_messages)/max(len(llm_messages),1):.1f} chars")
        print(f"   Average Fallback Length: {sum(len(m) for m in fallback_messages)/max(len(fallback_messages),1):.1f} chars")
        
    except Exception as e:
        print(f"Performance test failed: {e}")

def main():
    print("🚀 DeepSeek-R1 Among Us LLM Testing")
    
    test_deepseek_communication()
    test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("🎯 DeepSeek-R1 Benefits for Among Us:")
    print("✅ Fast inference for real-time chat")
    print("✅ Good at following constraints (50 char limit)")
    print("✅ Understands context and responds appropriately")
    print("✅ More natural than rule-based responses")
    print("✅ Can adapt to different roles (Impostor vs Crewmate)")
    
    print("\n📋 Next Steps:")
    print("1. Install DeepSeek-R1: ollama pull deepseek-r1:latest")
    print("2. Test in actual training: python train_cpu_safe.py --episodes 50 --llm")
    print("3. Monitor chat quality during games")

if __name__ == "__main__":
    main()
