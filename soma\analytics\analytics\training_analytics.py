import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, deque
import json
import os
from datetime import datetime
import pandas as pd

class AmongUsTrainingAnalytics:
    """Comprehensive analytics system for Among Us AI training"""
    
    def __init__(self, save_dir: str = "analytics_output"):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Training metrics
        self.episode_data = []
        self.win_rates = {"Impostor": deque(maxlen=1000), "Crewmate": deque(maxlen=1000)}
        self.reward_history = deque(maxlen=5000)
        self.loss_history = {"game": deque(maxlen=1000), "social": deque(maxlen=1000), "comm": deque(maxlen=1000)}
        
        # Strategy evolution tracking
        self.strategy_evolution = defaultdict(list)
        self.action_distributions = defaultdict(lambda: defaultdict(int))
        self.communication_patterns = defaultdict(list)
        
        # Performance metrics
        self.role_performance = defaultdict(list)
        self.opponent_analysis = defaultdict(lambda: defaultdict(list))
        self.curriculum_progress = []
        
        # Social deduction metrics
        self.deduction_accuracy = deque(maxlen=500)
        self.voting_patterns = defaultdict(list)
        self.trust_evolution = defaultdict(list)

        # Visual observation tracking (for real game integration)
        self.visual_observations = defaultdict(list)
        self.sus_events = defaultdict(list)  # Track suspicious visual events
        
        # Real-time dashboard data
        self.dashboard_data = {
            'current_episode': 0,
            'training_time': 0,
            'current_stage': 'Unknown',
            'recent_performance': 0.0
        }
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def record_episode(self, episode_data: Dict):
        """Record data from a completed episode"""
        
        episode_data['timestamp'] = datetime.now().isoformat()
        episode_data['episode_id'] = len(self.episode_data)
        
        self.episode_data.append(episode_data)
        
        # Update specific metrics
        if 'role' in episode_data and 'won' in episode_data:
            role = episode_data['role']
            won = episode_data['won']
            self.win_rates[role].append(1.0 if won else 0.0)
            self.role_performance[role].append(episode_data.get('performance_score', 0))
        
        if 'reward' in episode_data:
            self.reward_history.append(episode_data['reward'])
        
        # Update dashboard
        self.dashboard_data['current_episode'] = len(self.episode_data)
        self.dashboard_data['recent_performance'] = self._calculate_recent_performance()
        
        # Record strategy data
        if 'actions_taken' in episode_data:
            self._update_action_distributions(episode_data['actions_taken'], episode_data.get('role', 'Unknown'))
        
        if 'communication_data' in episode_data:
            self._update_communication_patterns(episode_data['communication_data'])
        
        if 'social_deduction' in episode_data:
            self._update_social_metrics(episode_data['social_deduction'])
    
    def record_training_step(self, losses: Dict[str, float]):
        """Record training step losses"""
        for loss_type, loss_value in losses.items():
            if loss_type in self.loss_history:
                self.loss_history[loss_type].append(loss_value)
    
    def record_curriculum_progress(self, stage_data: Dict):
        """Record curriculum learning progress"""
        stage_data['timestamp'] = datetime.now().isoformat()
        self.curriculum_progress.append(stage_data)
        self.dashboard_data['current_stage'] = stage_data.get('stage', 'Unknown')
    
    def _calculate_recent_performance(self, window: int = 50) -> float:
        """Calculate recent performance across all roles"""
        if len(self.episode_data) < window:
            return 0.0
        
        recent_episodes = self.episode_data[-window:]
        wins = sum(1 for ep in recent_episodes if ep.get('won', False))
        return wins / len(recent_episodes)
    
    def _update_action_distributions(self, actions: List[str], role: str):
        """Update action distribution tracking"""
        for action in actions:
            self.action_distributions[role][action] += 1
    
    def _update_communication_patterns(self, comm_data: Dict):
        """Update communication pattern analysis"""
        for pattern_type, data in comm_data.items():
            self.communication_patterns[pattern_type].append(data)
    
    def _update_social_metrics(self, social_data: Dict):
        """Update social deduction metrics"""
        if 'deduction_accuracy' in social_data:
            self.deduction_accuracy.append(social_data['deduction_accuracy'])
        
        if 'voting_data' in social_data:
            for vote_data in social_data['voting_data']:
                self.voting_patterns[vote_data['voter']].append(vote_data)
    
    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive training report"""
        
        report = {
            'training_summary': self._generate_training_summary(),
            'performance_analysis': self._generate_performance_analysis(),
            'strategy_evolution': self._generate_strategy_analysis(),
            'social_deduction_analysis': self._generate_social_analysis(),
            'curriculum_analysis': self._generate_curriculum_analysis(),
            'recommendations': self._generate_recommendations()
        }
        
        # Save report
        report_path = os.path.join(self.save_dir, f"training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report
    
    def _generate_training_summary(self) -> Dict:
        """Generate training summary statistics"""
        
        total_episodes = len(self.episode_data)
        if total_episodes == 0:
            return {"error": "No training data available"}
        
        # Role-specific win rates
        role_stats = {}
        for role in self.win_rates:
            if self.win_rates[role]:
                role_stats[role] = {
                    'total_games': len(self.win_rates[role]),
                    'win_rate': np.mean(self.win_rates[role]),
                    'recent_win_rate': np.mean(list(self.win_rates[role])[-50:]) if len(self.win_rates[role]) >= 50 else np.mean(self.win_rates[role]),
                    'win_rate_trend': self._calculate_trend(list(self.win_rates[role]))
                }
        
        return {
            'total_episodes': total_episodes,
            'training_duration': self._calculate_training_duration(),
            'role_statistics': role_stats,
            'average_reward': np.mean(self.reward_history) if self.reward_history else 0,
            'reward_trend': self._calculate_trend(list(self.reward_history)),
            'current_performance': self.dashboard_data['recent_performance']
        }
    
    def _generate_performance_analysis(self) -> Dict:
        """Generate detailed performance analysis"""
        
        analysis = {
            'win_rate_evolution': {},
            'performance_by_opponent': {},
            'action_effectiveness': {},
            'learning_curves': {}
        }
        
        # Win rate evolution
        for role in self.win_rates:
            if self.win_rates[role]:
                win_rates = list(self.win_rates[role])
                analysis['win_rate_evolution'][role] = {
                    'data': win_rates,
                    'moving_average': self._calculate_moving_average(win_rates, window=20),
                    'improvement_rate': self._calculate_improvement_rate(win_rates)
                }
        
        # Performance by opponent type
        opponent_performance = defaultdict(lambda: {'wins': 0, 'total': 0})
        for episode in self.episode_data:
            if 'opponent_types' in episode:
                for opponent in episode['opponent_types']:
                    opponent_performance[opponent]['total'] += 1
                    if episode.get('won', False):
                        opponent_performance[opponent]['wins'] += 1
        
        for opponent, stats in opponent_performance.items():
            if stats['total'] > 0:
                analysis['performance_by_opponent'][opponent] = {
                    'win_rate': stats['wins'] / stats['total'],
                    'total_games': stats['total']
                }
        
        return analysis
    
    def _generate_strategy_analysis(self) -> Dict:
        """Generate strategy evolution analysis"""
        
        strategy_data = {}
        
        # Action distribution evolution
        for role, actions in self.action_distributions.items():
            total_actions = sum(actions.values())
            if total_actions > 0:
                strategy_data[f'{role}_action_distribution'] = {
                    action: count / total_actions 
                    for action, count in actions.items()
                }
        
        # Communication pattern analysis
        comm_analysis = {}
        for pattern_type, data in self.communication_patterns.items():
            if data:
                comm_analysis[pattern_type] = {
                    'frequency': len(data),
                    'average_value': np.mean(data) if isinstance(data[0], (int, float)) else None,
                    'trend': self._calculate_trend(data) if isinstance(data[0], (int, float)) else None
                }
        
        return {
            'action_strategies': strategy_data,
            'communication_patterns': comm_analysis,
            'strategy_diversity': self._calculate_strategy_diversity()
        }
    
    def _generate_social_analysis(self) -> Dict:
        """Generate social deduction analysis"""
        
        if not self.deduction_accuracy:
            return {"error": "No social deduction data available"}
        
        return {
            'deduction_accuracy': {
                'current': np.mean(list(self.deduction_accuracy)[-20:]) if len(self.deduction_accuracy) >= 20 else np.mean(self.deduction_accuracy),
                'overall': np.mean(self.deduction_accuracy),
                'trend': self._calculate_trend(list(self.deduction_accuracy)),
                'improvement': self._calculate_improvement_rate(list(self.deduction_accuracy))
            },
            'voting_analysis': self._analyze_voting_patterns(),
            'trust_dynamics': self._analyze_trust_evolution()
        }
    
    def _generate_curriculum_analysis(self) -> Dict:
        """Generate curriculum learning analysis"""
        
        if not self.curriculum_progress:
            return {"error": "No curriculum data available"}
        
        current_stage = self.curriculum_progress[-1] if self.curriculum_progress else {}
        
        return {
            'current_stage': current_stage.get('stage', 'Unknown'),
            'stage_progress': current_stage.get('progress', 0),
            'stages_completed': len([s for s in self.curriculum_progress if s.get('completed', False)]),
            'stage_history': self.curriculum_progress,
            'learning_efficiency': self._calculate_learning_efficiency()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate training recommendations based on analysis"""
        
        recommendations = []
        
        # Performance-based recommendations
        if len(self.win_rates['Impostor']) > 50:
            imp_win_rate = np.mean(list(self.win_rates['Impostor'])[-50:])
            if imp_win_rate < 0.3:
                recommendations.append("Impostor win rate is low. Consider adjusting reward weights for deception and kill timing.")
        
        if len(self.win_rates['Crewmate']) > 50:
            crew_win_rate = np.mean(list(self.win_rates['Crewmate'])[-50:])
            if crew_win_rate < 0.4:
                recommendations.append("Crewmate win rate is low. Focus on task completion and crisis management training.")
        
        # Learning curve recommendations
        if self.reward_history:
            recent_rewards = list(self.reward_history)[-100:]
            if len(recent_rewards) > 50:
                trend = self._calculate_trend(recent_rewards)
                if trend < -0.01:
                    recommendations.append("Reward trend is declining. Consider adjusting learning rate or exploration strategy.")
        
        # Social deduction recommendations
        if self.deduction_accuracy:
            accuracy = np.mean(list(self.deduction_accuracy)[-50:]) if len(self.deduction_accuracy) >= 50 else np.mean(self.deduction_accuracy)
            if accuracy < 0.6:
                recommendations.append("Social deduction accuracy is low. Increase communication training and behavioral analysis.")
        
        return recommendations
    
    def create_training_visualizations(self):
        """Create comprehensive training visualizations"""
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 15))
        
        # 1. Win Rate Evolution
        plt.subplot(3, 3, 1)
        self._plot_win_rate_evolution()
        
        # 2. Reward History
        plt.subplot(3, 3, 2)
        self._plot_reward_history()
        
        # 3. Loss Evolution
        plt.subplot(3, 3, 3)
        self._plot_loss_evolution()
        
        # 4. Action Distribution
        plt.subplot(3, 3, 4)
        self._plot_action_distribution()
        
        # 5. Social Deduction Accuracy
        plt.subplot(3, 3, 5)
        self._plot_social_accuracy()
        
        # 6. Curriculum Progress
        plt.subplot(3, 3, 6)
        self._plot_curriculum_progress()
        
        # 7. Performance by Opponent
        plt.subplot(3, 3, 7)
        self._plot_opponent_performance()
        
        # 8. Communication Patterns
        plt.subplot(3, 3, 8)
        self._plot_communication_patterns()
        
        # 9. Strategy Evolution
        plt.subplot(3, 3, 9)
        self._plot_strategy_evolution()
        
        plt.tight_layout()
        
        # Save visualization
        viz_path = os.path.join(self.save_dir, f"training_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Training visualizations saved to {viz_path}")
    
    def _plot_win_rate_evolution(self):
        """Plot win rate evolution over time"""
        for role in self.win_rates:
            if self.win_rates[role]:
                win_rates = list(self.win_rates[role])
                moving_avg = self._calculate_moving_average(win_rates, window=20)
                plt.plot(moving_avg, label=f'{role} Win Rate', linewidth=2)
        
        plt.title('Win Rate Evolution')
        plt.xlabel('Episodes')
        plt.ylabel('Win Rate')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    def _plot_reward_history(self):
        """Plot reward history"""
        if self.reward_history:
            rewards = list(self.reward_history)
            moving_avg = self._calculate_moving_average(rewards, window=50)
            plt.plot(moving_avg, color='green', linewidth=2)
            plt.title('Average Reward Evolution')
            plt.xlabel('Episodes')
            plt.ylabel('Average Reward')
            plt.grid(True, alpha=0.3)
    
    def _plot_loss_evolution(self):
        """Plot training loss evolution"""
        for loss_type, losses in self.loss_history.items():
            if losses:
                loss_values = list(losses)
                moving_avg = self._calculate_moving_average(loss_values, window=20)
                plt.plot(moving_avg, label=f'{loss_type.title()} Loss', linewidth=2)
        
        plt.title('Training Loss Evolution')
        plt.xlabel('Training Steps')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.yscale('log')
    
    def _plot_action_distribution(self):
        """Plot action distribution by role"""
        # This is a simplified version - would need more complex logic for full implementation
        plt.title('Action Distribution Analysis')
        plt.text(0.5, 0.5, 'Action Distribution\n(Implementation Pending)', 
                ha='center', va='center', transform=plt.gca().transAxes)
    
    def _plot_social_accuracy(self):
        """Plot social deduction accuracy"""
        if self.deduction_accuracy:
            accuracy = list(self.deduction_accuracy)
            moving_avg = self._calculate_moving_average(accuracy, window=20)
            plt.plot(moving_avg, color='purple', linewidth=2)
            plt.title('Social Deduction Accuracy')
            plt.xlabel('Episodes')
            plt.ylabel('Accuracy')
            plt.grid(True, alpha=0.3)
    
    def _plot_curriculum_progress(self):
        """Plot curriculum learning progress"""
        plt.title('Curriculum Progress')
        plt.text(0.5, 0.5, 'Curriculum Progress\n(Implementation Pending)', 
                ha='center', va='center', transform=plt.gca().transAxes)
    
    def _plot_opponent_performance(self):
        """Plot performance against different opponents"""
        plt.title('Performance vs Opponents')
        plt.text(0.5, 0.5, 'Opponent Analysis\n(Implementation Pending)', 
                ha='center', va='center', transform=plt.gca().transAxes)
    
    def _plot_communication_patterns(self):
        """Plot communication pattern analysis"""
        plt.title('Communication Patterns')
        plt.text(0.5, 0.5, 'Communication Analysis\n(Implementation Pending)', 
                ha='center', va='center', transform=plt.gca().transAxes)
    
    def _plot_strategy_evolution(self):
        """Plot strategy evolution over time"""
        plt.title('Strategy Evolution')
        plt.text(0.5, 0.5, 'Strategy Evolution\n(Implementation Pending)', 
                ha='center', va='center', transform=plt.gca().transAxes)
    
    # Helper methods
    def _calculate_moving_average(self, data: List[float], window: int = 20) -> List[float]:
        """Calculate moving average"""
        if len(data) < window:
            return data
        
        moving_avg = []
        for i in range(len(data)):
            start_idx = max(0, i - window + 1)
            moving_avg.append(np.mean(data[start_idx:i+1]))
        
        return moving_avg
    
    def _calculate_trend(self, data: List[float]) -> float:
        """Calculate trend (slope) of data"""
        if len(data) < 2:
            return 0.0
        
        x = np.arange(len(data))
        return np.polyfit(x, data, 1)[0]
    
    def _calculate_improvement_rate(self, data: List[float]) -> float:
        """Calculate improvement rate"""
        if len(data) < 10:
            return 0.0
        
        first_half = np.mean(data[:len(data)//2])
        second_half = np.mean(data[len(data)//2:])
        
        return (second_half - first_half) / first_half if first_half != 0 else 0.0
    
    def _calculate_training_duration(self) -> str:
        """Calculate total training duration"""
        if len(self.episode_data) < 2:
            return "0 minutes"
        
        start_time = datetime.fromisoformat(self.episode_data[0]['timestamp'])
        end_time = datetime.fromisoformat(self.episode_data[-1]['timestamp'])
        duration = end_time - start_time
        
        hours = duration.total_seconds() / 3600
        return f"{hours:.1f} hours"
    
    def _calculate_strategy_diversity(self) -> float:
        """Calculate strategy diversity score"""
        # Simplified diversity calculation
        total_diversity = 0.0
        count = 0
        
        for role, actions in self.action_distributions.items():
            if actions:
                total_actions = sum(actions.values())
                probabilities = [count / total_actions for count in actions.values()]
                entropy = -sum(p * np.log(p) for p in probabilities if p > 0)
                total_diversity += entropy
                count += 1
        
        return total_diversity / count if count > 0 else 0.0
    
    def _analyze_voting_patterns(self) -> Dict:
        """Analyze voting patterns"""
        # Simplified voting analysis
        return {"analysis": "Voting pattern analysis pending implementation"}
    
    def _analyze_trust_evolution(self) -> Dict:
        """Analyze trust evolution"""
        # Simplified trust analysis
        return {"analysis": "Trust evolution analysis pending implementation"}
    
    def _calculate_learning_efficiency(self) -> float:
        """Calculate learning efficiency score"""
        if not self.curriculum_progress:
            return 0.0
        
        # Simplified efficiency calculation
        completed_stages = len([s for s in self.curriculum_progress if s.get('completed', False)])
        total_episodes = sum(s.get('episodes', 0) for s in self.curriculum_progress)
        
        return completed_stages / max(total_episodes / 100, 1) if total_episodes > 0 else 0.0
