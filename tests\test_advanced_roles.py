#!/usr/bin/env python3
"""
Test the advanced role system for Among Us AI training
"""

from core.roles import ALL_ROLES, RoleType, RoleRarity, create_role_distribution, validate_role_distribution
from training.role_manager import <PERSON><PERSON><PERSON><PERSON>, RoleConfig, RoleComplexity, create_role_config_for_stage
from training.curriculum_learning import AdaptiveCurriculumLearning, CurriculumStage
from agents.ultimate_rl_agent import UltimateAmongUsAgent

def test_role_definitions():
    """Test all role definitions"""
    print("🎭 Testing Role Definitions")
    print("=" * 40)
    
    # Test role categories
    crewmate_roles = [role for role in ALL_ROLES.values() if role.role_type == RoleType.CREWMATE]
    impostor_roles = [role for role in ALL_ROLES.values() if role.role_type == RoleType.IMPOSTOR]
    neutral_roles = [role for role in ALL_ROLES.values() if role.role_type == RoleType.NEUTRAL]
    
    print(f"📋 Role Categories:")
    print(f"   Crewmate Roles ({len(crewmate_roles)}):")
    for role in crewmate_roles:
        abilities = ", ".join([ability.name for ability in role.abilities]) or "None"
        print(f"      {role.name:15s} - {abilities}")
    
    print(f"\n   Impostor Roles ({len(impostor_roles)}):")
    for role in impostor_roles:
        abilities = ", ".join([ability.name for ability in role.abilities]) or "None"
        print(f"      {role.name:15s} - {abilities}")
    
    print(f"\n   Neutral Roles ({len(neutral_roles)}):")
    for role in neutral_roles:
        abilities = ", ".join([ability.name for ability in role.abilities]) or "None"
        print(f"      {role.name:15s} - {abilities}")
    
    # Test role abilities
    print(f"\n🔧 Special Abilities:")
    for role_name, role in ALL_ROLES.items():
        if role.abilities:
            print(f"   {role_name}:")
            for ability in role.abilities:
                cooldown_info = f" (CD: {ability.cooldown}s)" if ability.cooldown > 0 else ""
                uses_info = f" (Uses: {ability.uses})" if ability.uses else ""
                print(f"      - {ability.name}: {ability.description}{cooldown_info}{uses_info}")
    
    print("\n✅ Role definitions working!")

def test_role_complexity_progression():
    """Test role complexity progression"""
    print("\n📈 Testing Role Complexity Progression")
    print("=" * 45)
    
    complexities = [
        RoleComplexity.BASIC,
        RoleComplexity.INTERMEDIATE, 
        RoleComplexity.ADVANCED,
        RoleComplexity.EXPERT
    ]
    
    for complexity in complexities:
        print(f"\n🎯 {complexity.value.title()} Complexity:")
        
        config = RoleConfig(complexity=complexity)
        manager = RoleManager(config)
        
        # Test role assignment for different lobby sizes
        for lobby_size in [6, 10]:
            num_impostors = 2 if lobby_size >= 6 else 1
            roles = manager.assign_roles(lobby_size, num_impostors)
            
            # Count role types
            role_counts = {}
            for role in roles:
                role_name = role.name
                role_counts[role_name] = role_counts.get(role_name, 0) + 1
            
            print(f"   {lobby_size} players: {dict(role_counts)}")
            
            # Validate distribution
            valid, msg = validate_role_distribution([ALL_ROLES[r.name] for r in roles])
            print(f"      Valid: {'✅' if valid else '❌'} {msg}")
    
    print("\n✅ Role complexity progression working!")

def test_curriculum_role_integration():
    """Test role integration with curriculum learning"""
    print("\n🎓 Testing Curriculum Role Integration")
    print("=" * 40)
    
    # Create mock agent
    device = "cpu"
    agent = UltimateAmongUsAgent("Red", "Crewmate", device=device)
    
    # Create curriculum
    curriculum = AdaptiveCurriculumLearning(agent, device)
    
    # Test each curriculum stage
    stages = list(CurriculumStage)
    
    for stage in stages[:4]:  # Test first 4 stages
        print(f"\n📚 Stage: {stage.value}")
        
        # Manually set stage
        curriculum.current_stage = stage
        curriculum._update_role_manager()
        
        # Test role assignment
        if curriculum.role_manager:
            roles = curriculum.role_manager.assign_roles(6, 2)
            
            role_names = [role.name for role in roles]
            unique_roles = set(role_names)
            
            print(f"   Assigned roles: {role_names}")
            print(f"   Unique roles: {list(unique_roles)}")
            print(f"   Role complexity: {curriculum.role_manager.config.complexity.value}")
            
            # Test role abilities
            for role_name in unique_roles:
                abilities = curriculum.role_manager.get_role_abilities(role_name)
                if abilities:
                    print(f"   {role_name} abilities: {abilities}")
        else:
            print("   No role manager (using basic roles)")
    
    print("\n✅ Curriculum role integration working!")

def test_role_performance_tracking():
    """Test role performance tracking"""
    print("\n📊 Testing Role Performance Tracking")
    print("=" * 40)
    
    config = RoleConfig(complexity=RoleComplexity.ADVANCED)
    manager = RoleManager(config)
    
    # Simulate some games
    test_data = [
        ("Crewmate", True, 0.8),
        ("Crewmate", False, 0.6),
        ("Impostor", True, 0.9),
        ("Engineer", True, 0.7),
        ("Shapeshifter", False, 0.5),
        ("Scientist", True, 0.8),
        ("Phantom", True, 0.9),
    ]
    
    print("🎮 Simulating games...")
    for role_name, won, performance in test_data:
        manager.record_role_performance(role_name, won, performance)
        print(f"   {role_name}: {'Won' if won else 'Lost'} (Performance: {performance})")
    
    # Get statistics
    stats = manager.get_role_statistics()
    
    print(f"\n📈 Role Statistics:")
    for role_name, role_stats in stats.items():
        print(f"   {role_name:12s}: "
              f"Win Rate: {role_stats['win_rate']:.1%}, "
              f"Avg Performance: {role_stats['average_performance']:.2f}, "
              f"Games: {role_stats['games_played']}")
    
    # Test complexity advancement
    print(f"\n🔄 Testing complexity advancement:")
    print(f"   Current complexity: {manager.config.complexity.value}")
    
    can_advance = manager.should_increase_complexity(100, performance_threshold=0.6)
    print(f"   Should advance: {'✅' if can_advance else '❌'}")
    
    if can_advance:
        advanced = manager.advance_complexity()
        print(f"   Advanced: {'✅' if advanced else '❌'}")
        print(f"   New complexity: {manager.config.complexity.value}")
    
    print("\n✅ Role performance tracking working!")

def test_role_action_validation():
    """Test role action validation"""
    print("\n🔍 Testing Role Action Validation")
    print("=" * 35)
    
    config = RoleConfig(complexity=RoleComplexity.ADVANCED)
    manager = RoleManager(config)
    
    # Test different roles and actions
    test_cases = [
        ("Crewmate", "kill", False),
        ("Crewmate", "vent", False),
        ("Crewmate", "task", True),
        ("Impostor", "kill", True),
        ("Impostor", "vent", True),
        ("Impostor", "sabotage", True),
        ("Engineer", "vent", True),
        ("Engineer", "kill", False),
        ("Scientist", "task", True),
        ("Shapeshifter", "kill", True),
        ("Phantom", "vent", True),
    ]
    
    print("🧪 Testing role actions:")
    for role_name, action, expected in test_cases:
        can_perform = manager.can_role_perform_action(role_name, action)
        result = "✅" if can_perform == expected else "❌"
        print(f"   {role_name:12s} {action:8s}: {can_perform} {result}")
    
    print("\n✅ Role action validation working!")

def test_training_commands():
    """Show training commands with advanced roles"""
    print("\n🚀 Training Commands with Advanced Roles")
    print("=" * 45)
    
    commands = [
        {
            "name": "Basic Role Training",
            "command": "python train_advanced.py --episodes 100 --mode curriculum --complexity basic",
            "description": "Only Crewmate and Impostor roles"
        },
        {
            "name": "Intermediate Role Training",
            "command": "python train_advanced.py --episodes 200 --mode mixed --complexity intermediate",
            "description": "Add Engineer, Scientist, Shapeshifter"
        },
        {
            "name": "Advanced Role Self-Play",
            "command": "python train_advanced.py --episodes 300 --mode self_play --complexity advanced",
            "description": "All standard roles with special abilities"
        },
        {
            "name": "Expert Role Training",
            "command": "python train_advanced.py --episodes 500 --mode mixed --complexity expert --llm",
            "description": "All roles including neutral roles with LLM"
        },
        {
            "name": "Progressive Role Complexity",
            "command": "python train_advanced.py --episodes 1000 --mode curriculum --adaptive-roles",
            "description": "Automatically increase role complexity as agent improves"
        }
    ]
    
    for cmd in commands:
        print(f"\n🎯 {cmd['name']}:")
        print(f"   Description: {cmd['description']}")
        print(f"   Command: {cmd['command']}")
    
    print(f"\n💡 Benefits of Advanced Role System:")
    print(f"✅ Progressive complexity matching agent skill")
    print(f"✅ All official Among Us roles supported")
    print(f"✅ Role-specific ability training")
    print(f"✅ Performance tracking per role")
    print(f"✅ Automatic complexity advancement")
    print(f"✅ Balanced role distributions")

def main():
    print("🎭 Among Us Advanced Role System Testing")
    print("=" * 60)
    
    try:
        test_role_definitions()
        test_role_complexity_progression()
        test_curriculum_role_integration()
        test_role_performance_tracking()
        test_role_action_validation()
        test_training_commands()
        
        print("\n" + "=" * 60)
        print("🎉 Advanced Role System Fully Operational!")
        print("=" * 60)
        
        print("\n🎭 Key Features:")
        print("✅ All official Among Us roles implemented")
        print("✅ Progressive complexity system (Basic → Expert)")
        print("✅ Role-specific abilities and mechanics")
        print("✅ Curriculum integration with automatic progression")
        print("✅ Performance tracking and statistics")
        print("✅ Balanced role distributions")
        print("✅ Action validation per role")
        print("✅ Support for modded/neutral roles")
        
        print("\n🚀 Ready for advanced role-based training!")
        
    except Exception as e:
        print(f"\n❌ Advanced role system test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
