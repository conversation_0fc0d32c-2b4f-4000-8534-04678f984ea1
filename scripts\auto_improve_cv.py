#!/usr/bin/env python3
"""
Automated Computer Vision Improvement Script.

Runs automated improvement of CV accuracy without human intervention.
Uses temporal consistency and confidence patterns to self-improve.
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.auto_cv_improvement import AutoCVImprovement

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Automated CV Improvement')
    
    parser.add_argument('--duration', type=float, default=1.0,
                       help='Improvement duration in hours')
    
    parser.add_argument('--window-size', type=int, default=10,
                       help='Temporal window size for consistency checking')
    
    parser.add_argument('--load-progress', action='store_true',
                       help='Load previous improvement progress')
    
    return parser.parse_args()


def main():
    """Main function."""
    try:
        print("🤖 Automated Computer Vision Improvement")
        print("=" * 45)
        
        # Parse arguments
        args = parse_arguments()
        
        print(f"Duration: {args.duration} hours")
        print(f"Window size: {args.window_size}")
        print(f"Load progress: {'Yes' if args.load_progress else 'No'}")
        print()
        
        print("This system will automatically improve CV accuracy by:")
        print("  ✓ Analyzing temporal consistency of detections")
        print("  ✓ Adjusting confidence thresholds based on patterns")
        print("  ✓ Learning from detection stability over time")
        print("  ✓ Self-correcting without human intervention")
        print()
        
        input("Make sure Among Us is running and press Enter to start...")
        
        # Create improvement system
        improver = AutoCVImprovement(window_size=args.window_size)
        
        # Load previous progress if requested
        if args.load_progress:
            if improver.load_improvement_progress():
                print("📈 Continuing from previous improvement session")
            else:
                print("🆕 Starting fresh improvement session")
        
        # Show initial performance
        print("\n📊 Initial Performance Estimate:")
        initial_perf = improver.get_current_performance_estimate()
        if initial_perf:
            for metric, value in initial_perf.items():
                if isinstance(value, dict):
                    print(f"  {metric}:")
                    for k, v in value.items():
                        print(f"    {k}: {v:.3f}")
                else:
                    print(f"  {metric}: {value:.3f}")
        else:
            print("  No baseline data available")
        
        print(f"\n🔄 Starting {args.duration} hour improvement session...")
        
        # Run improvement
        success = improver.start_continuous_improvement(duration_hours=args.duration)
        
        if success:
            print("\n📈 Final Performance Estimate:")
            final_perf = improver.get_current_performance_estimate()
            
            if final_perf:
                for metric, value in final_perf.items():
                    if isinstance(value, dict):
                        print(f"  {metric}:")
                        for k, v in value.items():
                            print(f"    {k}: {v:.3f}")
                    else:
                        print(f"  {metric}: {value:.3f}")
                
                # Show improvement
                if initial_perf and 'avg_game_state_confidence' in initial_perf and 'avg_game_state_confidence' in final_perf:
                    improvement = final_perf['avg_game_state_confidence'] - initial_perf['avg_game_state_confidence']
                    print(f"\n🎯 Overall Confidence Improvement: {improvement:+.3f}")
                    
                    if improvement > 0.05:
                        print("🎉 Significant improvement achieved!")
                    elif improvement > 0.01:
                        print("✅ Moderate improvement achieved!")
                    else:
                        print("📊 System is learning - continue for more improvement")
            
            print("\n💡 Next Steps:")
            print("  - Run longer sessions for more improvement")
            print("  - Use --load-progress to continue from where you left off")
            print("  - Test the improved system with scripts/test_cv_basic.py")
            
        else:
            print("❌ Improvement session failed!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Improvement interrupted by user")
        print("Progress has been saved and can be resumed with --load-progress")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
    finally:
        if 'improver' in locals():
            improver.cleanup()


if __name__ == "__main__":
    main()
