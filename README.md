# SOMA: The Ultimate Among Us AI Training System

**State-of-the-Art Multi-Agent Reinforcement Learning for Social Deduction Games**

SOMA (Social deduction Optimization through Multi-Agent learning) is the most advanced Among Us AI training system ever built, featuring wiki-accurate game mechanics, sophisticated neural networks, curriculum learning, and LLM-powered communication.

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run quick demo (20 episodes)
python demo.py

# Start full training
python train.py --episodes 1000 --mode self_play --llm

# View training results
python scripts/view_training_results.py
```

## Project Structure

```
SOMA/
├── soma/                    # Main package
│   ├── core/                # Game mechanics
│   ├── agents/              # AI agents
│   ├── training/            # Training systems
│   ├── analytics/           # Performance analysis
│   └── config/              # Configuration
├── scripts/                 # Training scripts
├── tests/                   # Test files
├── examples/                # Usage examples
├── data/                    # Models & analytics
└── dev/                     # Development tools
```

## Features

### Advanced Multi-Modal RL Architecture
- **Separate Neural Networks** for game actions, social deduction, and communication
- **Role-Specific Learning** with 12+ official Among Us roles
- **Prioritized Experience Replay** with multi-buffer learning
- **Advanced State Encoding** capturing game state and social dynamics

### Self-Play Training
- **Pure Self-Play** - All RL agents learning together
- **Mixed Training** - RL agents + scripted opponents
- **League Play** - Agents compete against past versions
- **Population-Based** - Multiple agent populations

### Variable Rulesets
- **18 Official Colors** - Full Among Us color palette
- **Dynamic Game Rules** - Adaptable lobby configurations
- **Progressive Difficulty** - Rules get harder over time
- **Competitive Settings** - Tournament-style configurations

### Curriculum Learning
- **7-Stage Progression** from basic movement to master level
- **Automatic Advancement** based on performance
- **Role Complexity Scaling** - More roles as agent improves
- **Adaptive Difficulty** - Challenges match skill level

### LLM Communication
- **DeepSeek-R1 Integration** - Natural language chat
- **50-Character Limit** - Authentic Among Us constraints
- **Role-Appropriate Dialogue** - Different strategies per role
- **Context-Aware Messaging** - Responses based on game state

### Computer Vision Training
- **Real Game Integration** - Train on actual Among Us gameplay
- **Screen Capture & Analysis** - Advanced image processing pipeline
- **Movement & Task Learning** - Learn real game mechanics
- **UI Interaction Training** - Master game interface control
- **Safety Features** - Emergency stops and boundary detection

### Comprehensive Analytics
- **Real-Time Performance Tracking** with detailed metrics
- **Advanced Visualizations** showing learning curves
- **Role-Specific Statistics** - Performance by role type
- **Training Recommendations** - Optimization suggestions

## Training Modes

### Basic Training
```bash
# Traditional curriculum learning
python train.py --episodes 500 --mode curriculum

# With LLM communication
python train.py --episodes 500 --mode curriculum --llm
```

### Self-Play Training
```bash
# Pure self-play (6 RL agents)
python train.py --episodes 1000 --mode self_play --num-rl-agents 6

# Large lobby self-play (15 players)
python train.py --episodes 1000 --mode self_play --num-rl-agents 15 --lobby-size 15

# Mixed training (RL + scripted)
python train.py --episodes 1000 --mode mixed --num-rl-agents 8 --num-scripted-agents 2
```

### Advanced Training
```bash
# Progressive role complexity
python train.py --episodes 2000 --mode mixed --complexity progressive

# Competitive tournament settings
python train.py --episodes 1500 --mode self_play --ruleset competitive

# Random rules for adaptability
python train.py --episodes 1000 --mode mixed --ruleset random --difficulty hard
```

### Computer Vision Training
```bash
# System calibration (run first)
python scripts/train_computer_vision.py --calibrate-only --color red

# Observation mode (safe learning)
python scripts/train_computer_vision.py --mode observation_only --color red

# Full automation (AI plays the game)
python scripts/train_computer_vision.py --mode full_automation --color red --max-games 5
```

## Role System

**Crewmate Roles:** Crewmate, Engineer, Scientist, Guardian Angel, Sheriff, Tracker, Noisemaker
**Impostor Roles:** Impostor, Shapeshifter, Phantom
**Neutral Roles:** Jester, Survivor (modded)

## Performance

After full training, SOMA agents achieve:
- **70%+ Win Rate** as both Impostor and Crewmate
- **Advanced Social Deduction** with 80%+ accuracy
- **Strategic Communication** with context-aware messaging
- **Human-Level Performance** in complex scenarios
- **Real Game Competency** through computer vision training

## Contributing

We welcome contributions! See the `dev/` directory for development tools and guidelines.

## License

MIT License - See LICENSE file for details.

---

**SOMA: Where AI Learns to Deceive, Deduce, and Dominate**