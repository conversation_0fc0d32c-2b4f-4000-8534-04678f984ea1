"""
Computer Vision Agent Bridge for Among Us.

Connects computer vision system with existing RL agents to enable
real-game training and decision making.
"""

import numpy as np
import cv2
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from .screen_capture import ScreenCapture
from .game_detector import GameStateDetector, GameState, GameStateInfo
from .player_detector import <PERSON>Dete<PERSON>, PlayerInfo, UIElement, PlayerColor
from .input_controller import InputController, InputAction, InputType

# Import existing agent classes
from ..agents.agents.ultimate_rl_agent import UltimateAmongUsAgent
from ..core.core.action import Action
from ..core.core.player import Player

logger = logging.getLogger(__name__)


@dataclass
class CVObservation:
    """Computer vision observation for RL agents."""
    game_state: GameState
    players: List[PlayerInfo]
    ui_elements: List[UIElement]
    own_position: Optional[Tuple[int, int]]
    minimap_data: Optional[Dict[str, Any]]
    task_locations: List[Tuple[int, int]]
    emergency_available: bool
    can_vent: bool
    timestamp: float


class CVAgentBridge:
    """Bridge between computer vision system and RL agents."""
    
    def __init__(self, agent: UltimateAmongUsAgent, player_color: PlayerColor):
        """
        Initialize CV-Agent bridge.
        
        Args:
            agent: The RL agent to control
            player_color: The color of the player this agent controls
        """
        self.agent = agent
        self.player_color = player_color
        
        # Computer vision components
        self.screen_capture = ScreenCapture(target_fps=10)
        self.game_detector = GameStateDetector()
        self.player_detector = PlayerDetector()
        self.input_controller = InputController(safety_enabled=True)
        
        # State tracking
        self.current_observation = None
        self.last_action_time = 0
        self.action_cooldown = 0.5  # Minimum time between actions
        
        # Game state mapping
        self.cv_to_sim_state_map = {
            GameState.LOBBY: "lobby",
            GameState.GAMEPLAY: "gameplay", 
            GameState.MEETING: "meeting",
            GameState.VOTING: "voting",
            GameState.TASK: "task",
            GameState.GAME_OVER: "game_over"
        }
        
        # Action mapping from RL agent actions to CV actions
        self.action_map = {
            "move": self._handle_move_action,
            "task": self._handle_task_action,
            "kill": self._handle_kill_action,
            "report": self._handle_report_action,
            "vent": self._handle_vent_action,
            "button": self._handle_button_action,
            "sabotage_lights": self._handle_sabotage_action,
            "sabotage_oxygen": self._handle_sabotage_action,
            "sabotage_reactor": self._handle_sabotage_action,
            "sabotage_comms": self._handle_sabotage_action,
            "vote": self._handle_vote_action,
            "idle": self._handle_idle_action
        }
        
        # Performance tracking
        self.performance_stats = {
            'total_observations': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'avg_processing_time': 0.0
        }
    
    def initialize(self) -> bool:
        """Initialize the computer vision system."""
        try:
            # Find and configure Among Us window
            if not self.screen_capture.find_among_us_window():
                logger.error("Could not find Among Us window")
                return False
            
            # Set safety bounds for input controller
            if self.screen_capture.window_region:
                bounds = (
                    self.screen_capture.window_region['left'],
                    self.screen_capture.window_region['top'],
                    self.screen_capture.window_region['width'],
                    self.screen_capture.window_region['height']
                )
                self.input_controller.set_safety_bounds(bounds)
            
            # Start input controller
            self.input_controller.start()
            
            logger.info("Computer vision system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing CV system: {e}")
            return False
    
    def get_observation(self) -> Optional[CVObservation]:
        """Get current computer vision observation."""
        try:
            start_time = time.time()
            
            # Capture frame
            frame = self.screen_capture.capture_frame()
            if frame is None:
                return None
            
            # Detect game state
            game_state_info = self.game_detector.detect_game_state(frame)
            
            # Detect players and UI elements
            players = self.player_detector.detect_players(frame)
            ui_elements = self.player_detector.detect_ui_elements(frame)
            
            # Find own position (player with matching color)
            own_position = None
            for player in players:
                if player.color == self.player_color:
                    own_position = player.position
                    break
            
            # Extract additional information from game state details
            details = game_state_info.details
            task_locations = details.get('task_locations', [])
            emergency_available = details.get('emergency_available', False)
            
            # Check if venting is available (simplified)
            can_vent = any(ui.element_type == 'vent' for ui in ui_elements)
            
            # Create observation
            observation = CVObservation(
                game_state=game_state_info.state,
                players=players,
                ui_elements=ui_elements,
                own_position=own_position,
                minimap_data=None,  # Would need more sophisticated extraction
                task_locations=task_locations,
                emergency_available=emergency_available,
                can_vent=can_vent,
                timestamp=time.time()
            )
            
            # Update performance stats
            processing_time = time.time() - start_time
            self.performance_stats['total_observations'] += 1
            total_obs = self.performance_stats['total_observations']
            self.performance_stats['avg_processing_time'] = (
                (self.performance_stats['avg_processing_time'] * (total_obs - 1) + processing_time) / total_obs
            )
            
            self.current_observation = observation
            return observation
            
        except Exception as e:
            logger.error(f"Error getting observation: {e}")
            return None
    
    def convert_cv_to_sim_observation(self, cv_obs: CVObservation) -> Dict[str, Any]:
        """Convert CV observation to format expected by RL agent."""
        try:
            # Create simulated observation dictionary
            sim_obs = {
                'game_state': self.cv_to_sim_state_map.get(cv_obs.game_state, 'unknown'),
                'room': 'Unknown',  # Would need room detection
                'players_in_room': [p.color.value for p in cv_obs.players if not p.is_dead],
                'dead_bodies': [p.color.value for p in cv_obs.players if p.is_dead],
                'can_kill': False,  # Would need proximity and role detection
                'can_vent': cv_obs.can_vent,
                'can_sabotage': False,  # Would need role detection
                'can_emergency': cv_obs.emergency_available,
                'task_here': len(cv_obs.task_locations) > 0,
                'active_sabotages': [],  # Would need sabotage detection
                'room_features': {
                    'security_cameras': any(ui.element_type == 'security' for ui in cv_obs.ui_elements),
                    'admin_table': any(ui.element_type == 'admin' for ui in cv_obs.ui_elements),
                    'vitals': any(ui.element_type == 'vitals' for ui in cv_obs.ui_elements)
                },
                'timestamp': cv_obs.timestamp
            }
            
            return sim_obs
            
        except Exception as e:
            logger.error(f"Error converting CV observation: {e}")
            return {}
    
    def execute_agent_action(self, action: Action) -> bool:
        """Execute an action from the RL agent using computer vision."""
        try:
            current_time = time.time()
            
            # Check action cooldown
            if current_time - self.last_action_time < self.action_cooldown:
                return False
            
            # Get action handler
            handler = self.action_map.get(action.action_type)
            if not handler:
                logger.warning(f"No handler for action type: {action.action_type}")
                return False
            
            # Execute action
            success = handler(action)
            
            # Update stats and timing
            if success:
                self.performance_stats['successful_actions'] += 1
            else:
                self.performance_stats['failed_actions'] += 1
            
            self.last_action_time = current_time
            return success
            
        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return False
    
    def _handle_move_action(self, action: Action) -> bool:
        """Handle movement action."""
        try:
            # Use keyboard controls for movement
            if hasattr(action, 'target') and action.target:
                # If target specified, determine direction
                if self.current_observation and self.current_observation.own_position:
                    own_x, own_y = self.current_observation.own_position
                    target_x, target_y = action.target

                    # Calculate direction
                    dx = target_x - own_x
                    dy = target_y - own_y

                    # Move in the appropriate direction
                    if abs(dx) > abs(dy):
                        if dx > 0:
                            self.input_controller.move_right(0.5)
                        else:
                            self.input_controller.move_left(0.5)
                    else:
                        if dy > 0:
                            self.input_controller.move_down(0.5)
                        else:
                            self.input_controller.move_up(0.5)

                    return True
            else:
                # Random movement if no target
                import random
                directions = ['w', 'a', 's', 'd']
                direction = random.choice(directions)
                self.input_controller.hold_key(direction, 0.5)
                return True

            return False

        except Exception as e:
            logger.error(f"Error handling move action: {e}")
            return False
    
    def _handle_task_action(self, action: Action) -> bool:
        """Handle task action."""
        try:
            # Use keyboard interaction for tasks
            self.input_controller.interact()  # Press E to interact
            return True

        except Exception as e:
            logger.error(f"Error handling task action: {e}")
            return False
    
    def _handle_kill_action(self, action: Action) -> bool:
        """Handle kill action."""
        try:
            # Use keyboard kill command
            self.input_controller.kill()  # Press Q to kill
            return True

        except Exception as e:
            logger.error(f"Error handling kill action: {e}")
            return False
    
    def _handle_report_action(self, action: Action) -> bool:
        """Handle report body action."""
        try:
            # Use keyboard report command
            self.input_controller.report()  # Press R to report
            return True

        except Exception as e:
            logger.error(f"Error handling report action: {e}")
            return False

    def _handle_vent_action(self, action: Action) -> bool:
        """Handle vent action."""
        try:
            # Use keyboard vent command
            self.input_controller.vent()  # Press V to vent
            return True

        except Exception as e:
            logger.error(f"Error handling vent action: {e}")
            return False

    def _handle_button_action(self, action: Action) -> bool:
        """Handle emergency button action."""
        try:
            # Use keyboard emergency meeting command
            self.input_controller.emergency_meeting()  # Press Space for emergency meeting
            return True

        except Exception as e:
            logger.error(f"Error handling button action: {e}")
            return False

    def _handle_sabotage_action(self, action: Action) -> bool:
        """Handle sabotage actions."""
        try:
            # Sabotage actions would require opening sabotage menu
            # This is a simplified implementation

            # Press sabotage key (typically 'Q' or click sabotage button)
            self.input_controller.press_key('q')

            # Wait a bit for menu to open
            time.sleep(0.2)

            # Click on specific sabotage based on action type
            # This would need specific UI detection for sabotage menu
            # For now, just click in center of screen
            if self.screen_capture.window_region:
                center_x = self.screen_capture.window_region['left'] + self.screen_capture.window_region['width'] // 2
                center_y = self.screen_capture.window_region['top'] + self.screen_capture.window_region['height'] // 2
                self.input_controller.click((center_x, center_y))
                return True

            return False

        except Exception as e:
            logger.error(f"Error handling sabotage action: {e}")
            return False

    def _handle_vote_action(self, action: Action) -> bool:
        """Handle voting action."""
        try:
            if not self.current_observation:
                return False

            # Look for vote buttons (red buttons in voting interface)
            vote_buttons = [ui for ui in self.current_observation.ui_elements
                          if ui.element_type == 'vote_button']

            if vote_buttons:
                # Vote for first available option (simplified)
                button = vote_buttons[0]
                self.input_controller.click(button.position)
                return True

            return False

        except Exception as e:
            logger.error(f"Error handling vote action: {e}")
            return False

    def _handle_idle_action(self, action: Action) -> bool:
        """Handle idle action (do nothing)."""
        return True

    def run_training_step(self) -> bool:
        """Run one training step with computer vision."""
        try:
            # Get current observation
            cv_obs = self.get_observation()
            if not cv_obs:
                return False

            # Convert to format expected by RL agent
            sim_obs = self.convert_cv_to_sim_observation(cv_obs)
            if not sim_obs:
                return False

            # Get action from RL agent
            action = self.agent.get_action(sim_obs)
            if not action:
                return False

            # Execute action using computer vision
            success = self.execute_agent_action(action)

            # Provide feedback to agent (simplified)
            # In a full implementation, this would include reward calculation
            if hasattr(self.agent, 'update_from_cv'):
                self.agent.update_from_cv(sim_obs, action, success)

            return success

        except Exception as e:
            logger.error(f"Error in training step: {e}")
            return False

    def cleanup(self):
        """Clean up resources."""
        try:
            self.input_controller.stop()
            self.screen_capture.cleanup()
            logger.info("CV Agent Bridge cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = self.performance_stats.copy()

        # Add component stats
        stats['screen_capture'] = self.screen_capture.get_capture_stats()
        stats['input_controller'] = self.input_controller.get_stats()
        stats['player_detector'] = self.player_detector.get_detection_stats()

        # Calculate success rate
        total_actions = stats['successful_actions'] + stats['failed_actions']
        if total_actions > 0:
            stats['action_success_rate'] = stats['successful_actions'] / total_actions
        else:
            stats['action_success_rate'] = 0.0

        return stats

    def reset_stats(self):
        """Reset all performance statistics."""
        self.performance_stats = {
            'total_observations': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'avg_processing_time': 0.0
        }

        self.screen_capture.reset_stats()
        self.input_controller.reset_stats()

    def is_game_active(self) -> bool:
        """Check if game is currently active."""
        if not self.current_observation:
            return False

        active_states = [GameState.GAMEPLAY, GameState.MEETING, GameState.VOTING, GameState.TASK]
        return self.current_observation.game_state in active_states

    def wait_for_game_start(self, timeout: float = 60.0) -> bool:
        """Wait for game to start."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            cv_obs = self.get_observation()
            if cv_obs and cv_obs.game_state == GameState.GAMEPLAY:
                return True
            time.sleep(1.0)

        return False

    def __enter__(self):
        """Context manager entry."""
        if self.initialize():
            return self
        else:
            raise RuntimeError("Failed to initialize CV Agent Bridge")

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
