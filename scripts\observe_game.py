#!/usr/bin/env python3
"""
Among Us Game Observer Script.

Observes Among Us gameplay and collects data for training.
This is a safe mode that only watches the game without any control.
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.screen_capture import ScreenCapture
from soma.computer_vision.game_detector import GameStateDetector, GameState
from soma.computer_vision.player_detector import PlayerDetector, PlayerColor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GameObserver:
    """Observes Among Us gameplay and collects training data."""
    
    def __init__(self, player_color: PlayerColor = PlayerColor.RED, fps: int = 5):
        self.player_color = player_color
        self.fps = fps
        
        # Initialize components
        self.screen_capture = ScreenCapture(target_fps=fps)
        self.game_detector = GameStateDetector()
        self.player_detector = PlayerDetector()
        
        # Data collection
        self.observations = []
        self.game_sessions = []
        self.current_session = None
        
        # Statistics
        self.stats = {
            'total_observations': 0,
            'game_states_seen': set(),
            'players_detected': 0,
            'ui_elements_detected': 0,
            'start_time': None,
            'end_time': None
        }
    
    def initialize(self) -> bool:
        """Initialize the observer."""
        try:
            if not self.screen_capture.find_among_us_window():
                print("❌ Among Us window not found")
                print("   Make sure Among Us is running and visible")
                return False
            
            print("✅ Among Us window found")
            print(f"   Window: {self.screen_capture.window_region}")
            print(f"   Scale factor: {self.screen_capture.scale_factor}")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def start_observation(self, duration_minutes: int = 10):
        """Start observing the game."""
        try:
            print(f"🔍 Starting game observation for {duration_minutes} minutes...")
            print("   Press Ctrl+C to stop early")
            print()
            
            self.stats['start_time'] = time.time()
            end_time = self.stats['start_time'] + (duration_minutes * 60)
            
            last_state = None
            state_start_time = time.time()
            
            while time.time() < end_time:
                try:
                    # Capture frame
                    frame = self.screen_capture.capture_frame()
                    if frame is None:
                        time.sleep(0.1)
                        continue
                    
                    # Detect game state
                    state_info = self.game_detector.detect_game_state(frame)
                    current_state = state_info.state
                    
                    # Detect players and UI
                    players = self.player_detector.detect_players(frame)
                    ui_elements = self.player_detector.detect_ui_elements(frame)
                    
                    # Track state changes
                    if current_state != last_state:
                        if last_state is not None:
                            state_duration = time.time() - state_start_time
                            print(f"   {last_state.value} -> {current_state.value} (lasted {state_duration:.1f}s)")
                        else:
                            print(f"   Initial state: {current_state.value}")
                        
                        last_state = current_state
                        state_start_time = time.time()
                        
                        # Start new session if entering gameplay
                        if current_state == GameState.GAMEPLAY and self.current_session is None:
                            self.start_game_session()
                        
                        # End session if leaving gameplay
                        elif current_state != GameState.GAMEPLAY and self.current_session is not None:
                            self.end_game_session()
                    
                    # Create observation
                    observation = {
                        'timestamp': time.time(),
                        'game_state': current_state.value,
                        'confidence': state_info.confidence,
                        'players': [
                            {
                                'color': p.color.value,
                                'position': p.position,
                                'confidence': p.confidence,
                                'is_dead': p.is_dead,
                                'is_moving': p.is_moving
                            }
                            for p in players
                        ],
                        'ui_elements': [
                            {
                                'type': ui.element_type,
                                'position': ui.position,
                                'size': ui.size,
                                'confidence': ui.confidence
                            }
                            for ui in ui_elements
                        ],
                        'details': state_info.details
                    }
                    
                    # Store observation
                    self.observations.append(observation)
                    
                    # Update statistics
                    self.stats['total_observations'] += 1
                    self.stats['game_states_seen'].add(current_state.value)
                    self.stats['players_detected'] += len(players)
                    self.stats['ui_elements_detected'] += len(ui_elements)
                    
                    # Add to current session if active
                    if self.current_session is not None:
                        self.current_session['observations'].append(observation)
                    
                    # Show periodic updates
                    if self.stats['total_observations'] % 50 == 0:
                        elapsed = time.time() - self.stats['start_time']
                        remaining = (end_time - time.time()) / 60
                        print(f"   📊 {self.stats['total_observations']} observations, {remaining:.1f}min remaining")
                    
                    # Wait for next frame
                    time.sleep(1.0 / self.fps)
                    
                except KeyboardInterrupt:
                    print("\n⏹️  Observation stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error during observation: {e}")
                    time.sleep(1.0)
            
            self.stats['end_time'] = time.time()
            
            # End any active session
            if self.current_session is not None:
                self.end_game_session()
            
            print("\n✅ Observation completed!")
            self.show_summary()
            
        except Exception as e:
            print(f"❌ Observation failed: {e}")
    
    def start_game_session(self):
        """Start a new game session."""
        self.current_session = {
            'start_time': time.time(),
            'end_time': None,
            'observations': [],
            'initial_players': [],
            'final_state': None
        }
        print("   🎮 Game session started")
    
    def end_game_session(self):
        """End the current game session."""
        if self.current_session is not None:
            self.current_session['end_time'] = time.time()
            duration = self.current_session['end_time'] - self.current_session['start_time']
            
            self.game_sessions.append(self.current_session)
            print(f"   🏁 Game session ended (duration: {duration:.1f}s)")
            
            self.current_session = None
    
    def show_summary(self):
        """Show observation summary."""
        if self.stats['start_time'] is None:
            return
        
        total_time = self.stats['end_time'] - self.stats['start_time']
        
        print("\n📊 Observation Summary")
        print("=" * 30)
        print(f"Total time: {total_time/60:.1f} minutes")
        print(f"Total observations: {self.stats['total_observations']}")
        print(f"Average FPS: {self.stats['total_observations']/total_time:.1f}")
        print(f"Game states seen: {', '.join(sorted(self.stats['game_states_seen']))}")
        print(f"Total players detected: {self.stats['players_detected']}")
        print(f"Total UI elements detected: {self.stats['ui_elements_detected']}")
        print(f"Game sessions: {len(self.game_sessions)}")
        
        if self.game_sessions:
            avg_session_duration = sum(
                s['end_time'] - s['start_time'] for s in self.game_sessions
            ) / len(self.game_sessions)
            print(f"Average game duration: {avg_session_duration:.1f}s")
    
    def save_data(self, filepath: str = None):
        """Save collected data to file."""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"game_observations_{timestamp}.json"
        
        try:
            data = {
                'metadata': {
                    'player_color': self.player_color.value,
                    'fps': self.fps,
                    'total_observations': len(self.observations),
                    'game_sessions': len(self.game_sessions),
                    'observation_duration': self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] else 0
                },
                'statistics': {
                    'total_observations': self.stats['total_observations'],
                    'game_states_seen': list(self.stats['game_states_seen']),
                    'players_detected': self.stats['players_detected'],
                    'ui_elements_detected': self.stats['ui_elements_detected']
                },
                'observations': self.observations,
                'game_sessions': self.game_sessions
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"💾 Data saved to: {filepath}")
            print(f"   File size: {os.path.getsize(filepath) / 1024:.1f} KB")
            
        except Exception as e:
            print(f"❌ Failed to save data: {e}")
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.screen_capture.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Main observation function."""
    print("👁️  Among Us Game Observer")
    print("=" * 30)
    print("This script observes Among Us gameplay and collects training data.")
    print("It's completely safe - no game control, just observation.")
    print()
    
    # Get settings
    color_input = input("Player color to focus on (red/blue/green/etc.) [red]: ").strip().lower()
    if not color_input:
        color_input = "red"
    
    duration_input = input("Observation duration in minutes [10]: ").strip()
    try:
        duration = int(duration_input) if duration_input else 10
    except ValueError:
        duration = 10
    
    fps_input = input("Observation FPS [5]: ").strip()
    try:
        fps = int(fps_input) if fps_input else 5
    except ValueError:
        fps = 5
    
    # Map color
    color_map = {
        'red': PlayerColor.RED,
        'blue': PlayerColor.BLUE,
        'green': PlayerColor.GREEN,
        'pink': PlayerColor.PINK,
        'orange': PlayerColor.ORANGE,
        'yellow': PlayerColor.YELLOW,
        'black': PlayerColor.BLACK,
        'white': PlayerColor.WHITE,
        'purple': PlayerColor.PURPLE,
        'brown': PlayerColor.BROWN,
        'cyan': PlayerColor.CYAN,
        'lime': PlayerColor.LIME
    }
    
    player_color = color_map.get(color_input, PlayerColor.RED)
    
    print(f"\nSettings:")
    print(f"  Player color: {player_color.value}")
    print(f"  Duration: {duration} minutes")
    print(f"  FPS: {fps}")
    print()
    
    input("Make sure Among Us is running and press Enter to start...")
    print()
    
    # Start observation
    observer = GameObserver(player_color, fps)
    
    try:
        if observer.initialize():
            observer.start_observation(duration)
            observer.save_data()
        else:
            print("❌ Failed to initialize observer")
    
    except KeyboardInterrupt:
        print("\n⏹️  Observation interrupted")
    except Exception as e:
        print(f"❌ Observation error: {e}")
    finally:
        observer.cleanup()


if __name__ == "__main__":
    main()
