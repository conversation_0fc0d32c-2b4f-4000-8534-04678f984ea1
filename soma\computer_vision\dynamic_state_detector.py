"""
Dynamic State and Room Detection System.

This system automatically discovers new game states and room locations
without manual programming. It learns patterns and creates new categories
dynamically based on visual features.
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from collections import defaultdict, Counter
import json
import os
from sklearn.cluster import DBSCAN
from sklearn.feature_extraction.text import TfidfVectorizer

logger = logging.getLogger(__name__)


@dataclass
class VisualSignature:
    """Visual signature for a game state or room."""
    id: str
    name: str
    color_histogram: np.ndarray
    edge_density: float
    text_regions: List[Tuple[int, int, int, int]]
    dominant_colors: List[Tuple[int, int, int]]
    layout_features: Dict[str, float]
    confidence: float = 0.0
    sample_count: int = 0


@dataclass
class DynamicState:
    """Dynamically discovered game state."""
    state_id: str
    name: str
    visual_signature: VisualSignature
    transition_patterns: Dict[str, int]  # What states this transitions to/from
    duration_stats: Dict[str, float]  # min, max, avg duration
    frequency: int = 0
    last_seen: float = 0.0


class DynamicStateDetector:
    """Automatically discovers and learns game states and rooms."""
    
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
        
        # Dynamic state storage
        self.discovered_states: Dict[str, DynamicState] = {}
        self.room_signatures: Dict[str, VisualSignature] = {}
        
        # Learning data
        self.frame_history = []
        self.state_transitions = []
        self.current_state_id = None
        self.state_start_time = None
        
        # Feature extraction parameters
        self.color_bins = 32  # For color histograms
        self.min_samples_for_state = 5  # Minimum samples to confirm new state
        self.max_history_size = 1000
        
        # Room detection features
        self.room_features = {
            'wall_patterns': [],
            'floor_colors': [],
            'unique_objects': [],
            'lighting_patterns': []
        }
        
        # Known room indicators (can be expanded dynamically)
        self.room_indicators = {
            'electrical': ['wires', 'yellow_panels', 'dark_background'],
            'medbay': ['scanner', 'green_panels', 'medical_bed'],
            'security': ['cameras', 'monitors', 'blue_screens'],
            'reactor': ['reactor_core', 'red_panels', 'circular_room'],
            'o2': ['oxygen_tanks', 'blue_atmosphere', 'tree'],
            'navigation': ['steering_wheel', 'star_map', 'blue_interface'],
            'weapons': ['asteroids_game', 'targeting_system', 'red_interface'],
            'shields': ['hexagonal_panels', 'energy_field', 'purple_glow'],
            'communications': ['radio_equipment', 'antenna', 'communication_panels'],
            'storage': ['boxes', 'crates', 'storage_containers'],
            'admin': ['admin_table', 'holographic_display', 'central_table'],
            'cafeteria': ['tables', 'vending_machine', 'food_area']
        }
    
    def analyze_frame(self, frame: np.ndarray) -> Tuple[str, str, float]:
        """
        Analyze frame and return (state_id, room_id, confidence).
        Automatically discovers new states and rooms.
        """
        try:
            # Extract visual features
            signature = self._extract_visual_signature(frame)
            
            # Try to match existing states
            best_state_match = self._find_best_state_match(signature)
            
            if best_state_match and best_state_match[1] > self.similarity_threshold:
                state_id, confidence = best_state_match
                self._update_existing_state(state_id, signature)
            else:
                # Discover new state
                state_id = self._create_new_state(signature, frame)
                confidence = 0.5  # New state starts with medium confidence
            
            # Detect room
            room_id = self._detect_room(frame, signature)
            
            # Update state transitions
            self._update_state_transitions(state_id)
            
            # Store frame for learning
            self._store_frame_data(frame, signature, state_id, room_id)
            
            return state_id, room_id, confidence
            
        except Exception as e:
            logger.error(f"Error analyzing frame: {e}")
            return "unknown", "unknown", 0.0
    
    def _extract_visual_signature(self, frame: np.ndarray) -> VisualSignature:
        """Extract comprehensive visual signature from frame."""
        try:
            # Color histogram
            color_hist = self._compute_color_histogram(frame)
            
            # Edge density
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # Text regions
            text_regions = self._detect_text_regions(gray)
            
            # Dominant colors
            dominant_colors = self._extract_dominant_colors(frame)
            
            # Layout features
            layout_features = self._extract_layout_features(frame)
            
            return VisualSignature(
                id="temp",
                name="temp",
                color_histogram=color_hist,
                edge_density=edge_density,
                text_regions=text_regions,
                dominant_colors=dominant_colors,
                layout_features=layout_features
            )
            
        except Exception as e:
            logger.error(f"Error extracting visual signature: {e}")
            return VisualSignature("error", "error", np.array([]), 0.0, [], [], {})
    
    def _compute_color_histogram(self, frame: np.ndarray) -> np.ndarray:
        """Compute color histogram for the frame."""
        try:
            # Convert to HSV for better color representation
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Compute 3D histogram
            hist = cv2.calcHist([hsv], [0, 1, 2], None, 
                              [self.color_bins, self.color_bins, self.color_bins],
                              [0, 180, 0, 256, 0, 256])
            
            # Normalize
            hist = hist.flatten()
            hist = hist / (np.sum(hist) + 1e-7)
            
            return hist
            
        except Exception as e:
            logger.error(f"Error computing color histogram: {e}")
            return np.array([])
    
    def _detect_text_regions(self, gray: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect text regions in the image."""
        try:
            # Use morphological operations to find text-like regions
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            
            # Threshold
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Morphological operations
            morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
            
            # Find contours
            contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter for text-like regions
            text_regions = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                aspect_ratio = w / h if h > 0 else 0
                
                # Text-like criteria
                if 100 < area < 10000 and 0.2 < aspect_ratio < 10:
                    text_regions.append((x, y, w, h))
            
            return text_regions
            
        except Exception as e:
            logger.error(f"Error detecting text regions: {e}")
            return []
    
    def _extract_dominant_colors(self, frame: np.ndarray, k: int = 5) -> List[Tuple[int, int, int]]:
        """Extract dominant colors using k-means clustering."""
        try:
            # Reshape frame to list of pixels
            pixels = frame.reshape(-1, 3)
            
            # Sample pixels for efficiency
            if len(pixels) > 10000:
                indices = np.random.choice(len(pixels), 10000, replace=False)
                pixels = pixels[indices]
            
            # K-means clustering
            from sklearn.cluster import KMeans
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(pixels)
            
            # Get dominant colors
            colors = kmeans.cluster_centers_.astype(int)
            return [tuple(color) for color in colors]
            
        except Exception as e:
            logger.error(f"Error extracting dominant colors: {e}")
            return []
    
    def _extract_layout_features(self, frame: np.ndarray) -> Dict[str, float]:
        """Extract layout and structural features."""
        try:
            h, w = frame.shape[:2]
            
            # Divide frame into regions
            regions = {
                'top_left': frame[0:h//2, 0:w//2],
                'top_right': frame[0:h//2, w//2:w],
                'bottom_left': frame[h//2:h, 0:w//2],
                'bottom_right': frame[h//2:h, w//2:w],
                'center': frame[h//4:3*h//4, w//4:3*w//4]
            }
            
            features = {}
            
            for region_name, region in regions.items():
                # Average brightness
                gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
                features[f'{region_name}_brightness'] = np.mean(gray_region) / 255.0
                
                # Color variance
                features[f'{region_name}_color_variance'] = np.var(region) / (255.0 ** 2)
            
            # Overall features
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            features['overall_contrast'] = np.std(gray) / 255.0
            features['brightness_distribution'] = np.std(np.mean(frame, axis=2)) / 255.0
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting layout features: {e}")
            return {}
    
    def _find_best_state_match(self, signature: VisualSignature) -> Optional[Tuple[str, float]]:
        """Find best matching existing state."""
        try:
            best_match = None
            best_similarity = 0.0
            
            for state_id, state in self.discovered_states.items():
                similarity = self._calculate_signature_similarity(signature, state.visual_signature)
                
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = (state_id, similarity)
            
            return best_match if best_similarity > 0.5 else None
            
        except Exception as e:
            logger.error(f"Error finding best state match: {e}")
            return None
    
    def _calculate_signature_similarity(self, sig1: VisualSignature, sig2: VisualSignature) -> float:
        """Calculate similarity between two visual signatures."""
        try:
            similarities = []
            
            # Color histogram similarity
            if len(sig1.color_histogram) > 0 and len(sig2.color_histogram) > 0:
                color_sim = cv2.compareHist(sig1.color_histogram, sig2.color_histogram, cv2.HISTCMP_CORREL)
                similarities.append(max(0, color_sim))
            
            # Edge density similarity
            edge_diff = abs(sig1.edge_density - sig2.edge_density)
            edge_sim = max(0, 1.0 - edge_diff)
            similarities.append(edge_sim)
            
            # Layout features similarity
            layout_sim = self._compare_layout_features(sig1.layout_features, sig2.layout_features)
            similarities.append(layout_sim)
            
            # Dominant colors similarity
            color_sim = self._compare_dominant_colors(sig1.dominant_colors, sig2.dominant_colors)
            similarities.append(color_sim)
            
            # Weighted average
            weights = [0.4, 0.2, 0.2, 0.2]  # Color histogram is most important
            return np.average(similarities, weights=weights)
            
        except Exception as e:
            logger.error(f"Error calculating signature similarity: {e}")
            return 0.0
    
    def _compare_layout_features(self, features1: Dict[str, float], features2: Dict[str, float]) -> float:
        """Compare layout features between two signatures."""
        try:
            if not features1 or not features2:
                return 0.0
            
            common_keys = set(features1.keys()) & set(features2.keys())
            if not common_keys:
                return 0.0
            
            similarities = []
            for key in common_keys:
                diff = abs(features1[key] - features2[key])
                sim = max(0, 1.0 - diff)
                similarities.append(sim)
            
            return np.mean(similarities)
            
        except Exception as e:
            logger.error(f"Error comparing layout features: {e}")
            return 0.0
    
    def _compare_dominant_colors(self, colors1: List[Tuple[int, int, int]], 
                                colors2: List[Tuple[int, int, int]]) -> float:
        """Compare dominant colors between two signatures."""
        try:
            if not colors1 or not colors2:
                return 0.0
            
            # Find best matches between color sets
            similarities = []
            
            for c1 in colors1:
                best_match = 0.0
                for c2 in colors2:
                    # Color distance in RGB space
                    distance = np.sqrt(sum((a - b) ** 2 for a, b in zip(c1, c2)))
                    similarity = max(0, 1.0 - distance / (255 * np.sqrt(3)))
                    best_match = max(best_match, similarity)
                similarities.append(best_match)
            
            return np.mean(similarities)
            
        except Exception as e:
            logger.error(f"Error comparing dominant colors: {e}")
            return 0.0
    
    def _create_new_state(self, signature: VisualSignature, frame: np.ndarray) -> str:
        """Create a new dynamically discovered state."""
        try:
            # Generate unique state ID
            state_id = f"dynamic_state_{len(self.discovered_states) + 1}_{int(time.time())}"
            
            # Generate descriptive name based on features
            name = self._generate_state_name(signature, frame)
            
            # Create new state
            signature.id = state_id
            signature.name = name
            signature.sample_count = 1
            
            new_state = DynamicState(
                state_id=state_id,
                name=name,
                visual_signature=signature,
                transition_patterns={},
                duration_stats={'min': 0, 'max': 0, 'avg': 0},
                frequency=1,
                last_seen=time.time()
            )
            
            self.discovered_states[state_id] = new_state
            
            logger.info(f"Discovered new state: {name} (ID: {state_id})")
            return state_id
            
        except Exception as e:
            logger.error(f"Error creating new state: {e}")
            return "unknown"
    
    def _generate_state_name(self, signature: VisualSignature, frame: np.ndarray) -> str:
        """Generate descriptive name for a new state based on its features."""
        try:
            name_parts = []
            
            # Analyze dominant colors
            if signature.dominant_colors:
                primary_color = signature.dominant_colors[0]
                color_name = self._color_to_name(primary_color)
                name_parts.append(color_name)
            
            # Analyze edge density
            if signature.edge_density > 0.15:
                name_parts.append("complex")
            elif signature.edge_density < 0.05:
                name_parts.append("simple")
            
            # Analyze text regions
            if len(signature.text_regions) > 5:
                name_parts.append("text_heavy")
            elif len(signature.text_regions) > 0:
                name_parts.append("with_text")
            
            # Analyze layout
            if signature.layout_features:
                center_brightness = signature.layout_features.get('center_brightness', 0.5)
                if center_brightness > 0.7:
                    name_parts.append("bright_center")
                elif center_brightness < 0.3:
                    name_parts.append("dark_center")
            
            # Create name
            if name_parts:
                name = "_".join(name_parts[:3])  # Limit to 3 parts
            else:
                name = f"state_{int(time.time() % 10000)}"
            
            return name
            
        except Exception as e:
            logger.error(f"Error generating state name: {e}")
            return "unnamed_state"
    
    def _color_to_name(self, color: Tuple[int, int, int]) -> str:
        """Convert RGB color to descriptive name."""
        r, g, b = color
        
        # Simple color classification
        if r > 200 and g < 100 and b < 100:
            return "red"
        elif g > 200 and r < 100 and b < 100:
            return "green"
        elif b > 200 and r < 100 and g < 100:
            return "blue"
        elif r > 200 and g > 200 and b < 100:
            return "yellow"
        elif r > 200 and g < 100 and b > 200:
            return "purple"
        elif r < 100 and g > 200 and b > 200:
            return "cyan"
        elif r > 150 and g > 150 and b > 150:
            return "light"
        elif r < 100 and g < 100 and b < 100:
            return "dark"
        else:
            return "mixed"
    
    def _detect_room(self, frame: np.ndarray, signature: VisualSignature) -> str:
        """Detect which room the player is in."""
        try:
            # Try to match known room patterns
            best_room = "unknown"
            best_confidence = 0.0
            
            for room_name, indicators in self.room_indicators.items():
                confidence = self._calculate_room_confidence(frame, signature, indicators)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_room = room_name
            
            # If no good match, try to learn new room
            if best_confidence < 0.6:
                best_room = self._learn_new_room(frame, signature)
            
            return best_room
            
        except Exception as e:
            logger.error(f"Error detecting room: {e}")
            return "unknown"
    
    def _calculate_room_confidence(self, frame: np.ndarray, signature: VisualSignature, 
                                 indicators: List[str]) -> float:
        """Calculate confidence that frame shows a specific room."""
        try:
            # This is a simplified implementation
            # In practice, you'd have more sophisticated pattern matching
            
            confidence = 0.0
            
            # Check color patterns
            dominant_colors = signature.dominant_colors
            if dominant_colors:
                primary_color = dominant_colors[0]
                
                # Room-specific color matching
                if 'red' in indicators and primary_color[0] > 150:
                    confidence += 0.3
                elif 'green' in indicators and primary_color[1] > 150:
                    confidence += 0.3
                elif 'blue' in indicators and primary_color[2] > 150:
                    confidence += 0.3
            
            # Check layout patterns
            if signature.layout_features:
                # Room-specific layout checks would go here
                pass
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating room confidence: {e}")
            return 0.0
    
    def _learn_new_room(self, frame: np.ndarray, signature: VisualSignature) -> str:
        """Learn a new room pattern."""
        try:
            # Generate room ID based on visual features
            room_id = f"room_{len(self.room_signatures) + 1}"
            
            # Store room signature
            room_signature = signature
            room_signature.id = room_id
            room_signature.name = f"discovered_room_{room_id}"
            
            self.room_signatures[room_id] = room_signature
            
            logger.info(f"Discovered new room: {room_id}")
            return room_id
            
        except Exception as e:
            logger.error(f"Error learning new room: {e}")
            return "unknown"
    
    def _update_existing_state(self, state_id: str, signature: VisualSignature):
        """Update an existing state with new sample."""
        try:
            state = self.discovered_states[state_id]
            
            # Update sample count
            state.visual_signature.sample_count += 1
            
            # Update confidence (more samples = higher confidence)
            state.visual_signature.confidence = min(1.0, state.visual_signature.sample_count / 10.0)
            
            # Update frequency and last seen
            state.frequency += 1
            state.last_seen = time.time()
            
        except Exception as e:
            logger.error(f"Error updating existing state: {e}")
    
    def _update_state_transitions(self, current_state_id: str):
        """Update state transition patterns."""
        try:
            if self.current_state_id and self.current_state_id != current_state_id:
                # Record transition
                if self.current_state_id in self.discovered_states:
                    prev_state = self.discovered_states[self.current_state_id]
                    if current_state_id not in prev_state.transition_patterns:
                        prev_state.transition_patterns[current_state_id] = 0
                    prev_state.transition_patterns[current_state_id] += 1
                
                # Update duration stats
                if self.state_start_time:
                    duration = time.time() - self.state_start_time
                    self._update_duration_stats(self.current_state_id, duration)
            
            # Update current state
            self.current_state_id = current_state_id
            self.state_start_time = time.time()
            
        except Exception as e:
            logger.error(f"Error updating state transitions: {e}")
    
    def _update_duration_stats(self, state_id: str, duration: float):
        """Update duration statistics for a state."""
        try:
            if state_id in self.discovered_states:
                state = self.discovered_states[state_id]
                stats = state.duration_stats
                
                if stats['min'] == 0 or duration < stats['min']:
                    stats['min'] = duration
                if duration > stats['max']:
                    stats['max'] = duration
                
                # Update average
                count = state.frequency
                stats['avg'] = ((stats['avg'] * (count - 1)) + duration) / count
                
        except Exception as e:
            logger.error(f"Error updating duration stats: {e}")
    
    def _store_frame_data(self, frame: np.ndarray, signature: VisualSignature, 
                         state_id: str, room_id: str):
        """Store frame data for learning."""
        try:
            frame_data = {
                'timestamp': time.time(),
                'state_id': state_id,
                'room_id': room_id,
                'signature_summary': {
                    'edge_density': signature.edge_density,
                    'text_regions_count': len(signature.text_regions),
                    'dominant_colors_count': len(signature.dominant_colors)
                }
            }
            
            self.frame_history.append(frame_data)
            
            # Limit history size
            if len(self.frame_history) > self.max_history_size:
                self.frame_history.pop(0)
                
        except Exception as e:
            logger.error(f"Error storing frame data: {e}")
    
    def get_discovered_states(self) -> Dict[str, Dict[str, Any]]:
        """Get all discovered states with their information."""
        try:
            states_info = {}
            
            for state_id, state in self.discovered_states.items():
                states_info[state_id] = {
                    'name': state.name,
                    'frequency': state.frequency,
                    'confidence': state.visual_signature.confidence,
                    'sample_count': state.visual_signature.sample_count,
                    'last_seen': state.last_seen,
                    'transitions': state.transition_patterns,
                    'duration_stats': state.duration_stats
                }
            
            return states_info
            
        except Exception as e:
            logger.error(f"Error getting discovered states: {e}")
            return {}
    
    def get_discovered_rooms(self) -> Dict[str, Dict[str, Any]]:
        """Get all discovered rooms with their information."""
        try:
            rooms_info = {}
            
            for room_id, signature in self.room_signatures.items():
                rooms_info[room_id] = {
                    'name': signature.name,
                    'confidence': signature.confidence,
                    'sample_count': signature.sample_count
                }
            
            return rooms_info
            
        except Exception as e:
            logger.error(f"Error getting discovered rooms: {e}")
            return {}
    
    def save_discoveries(self, filepath: str):
        """Save discovered states and rooms to file."""
        try:
            discoveries = {
                'states': self.get_discovered_states(),
                'rooms': self.get_discovered_rooms(),
                'total_states': len(self.discovered_states),
                'total_rooms': len(self.room_signatures),
                'timestamp': time.time()
            }
            
            with open(filepath, 'w') as f:
                json.dump(discoveries, f, indent=2)
            
            logger.info(f"Saved discoveries to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving discoveries: {e}")
    
    def load_discoveries(self, filepath: str) -> bool:
        """Load previously discovered states and rooms."""
        try:
            if not os.path.exists(filepath):
                return False
            
            with open(filepath, 'r') as f:
                discoveries = json.load(f)
            
            # This would require more complex deserialization
            # For now, just log that we found previous discoveries
            logger.info(f"Found {discoveries.get('total_states', 0)} previous states and "
                       f"{discoveries.get('total_rooms', 0)} previous rooms")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading discoveries: {e}")
            return False
