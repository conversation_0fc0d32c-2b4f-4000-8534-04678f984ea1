import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import deque, defaultdict
import random
import json
import math

from agents.base_agent import BaseAgent
from core.action import Action, VALID_ACTIONS
from core.communication import Message, MessageType
from core.llm_communication import LLMCommunicationStrategy, OllamaClient

class SocialDeductionNet(nn.Module):
    """Advanced neural network for social deduction and behavioral analysis"""
    
    def __init__(self, input_dim: int = 128, hidden_dim: int = 256):
        super(SocialDeductionNet, self).__init__()
        
        # Player behavior analysis
        self.behavior_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Suspicion prediction for each player
        self.suspicion_head = nn.Linear(hidden_dim // 2, 8)  # Max 8 other players
        
        # Trust/alliance prediction
        self.trust_head = nn.Linear(hidden_dim // 2, 8)
        
        # Voting prediction (who will vote for whom)
        self.voting_prediction_head = nn.Linear(hidden_dim // 2, 64)  # 8x8 voting matrix
        
    def forward(self, social_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        behavior_features = self.behavior_encoder(social_features)
        
        return {
            'suspicion_logits': self.suspicion_head(behavior_features),
            'trust_logits': self.trust_head(behavior_features),
            'voting_prediction': self.voting_prediction_head(behavior_features).view(-1, 8, 8)
        }

class CommunicationNet(nn.Module):
    """Neural network for strategic communication decisions"""
    
    def __init__(self, context_dim: int = 200, hidden_dim: int = 256):
        super(CommunicationNet, self).__init__()
        
        # Communication context encoder
        self.context_encoder = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Message type selection
        self.message_type_head = nn.Linear(hidden_dim // 2, len(MessageType))
        
        # Target selection (who to accuse/defend/question)
        self.target_selection_head = nn.Linear(hidden_dim // 2, 8)
        
        # Confidence/aggression level
        self.confidence_head = nn.Linear(hidden_dim // 2, 1)
        
        # Timing decision (when to speak)
        self.timing_head = nn.Linear(hidden_dim // 2, 3)  # Early/Middle/Late
        
    def forward(self, context_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        context_encoded = self.context_encoder(context_features)
        
        return {
            'message_type_logits': self.message_type_head(context_encoded),
            'target_logits': self.target_selection_head(context_encoded),
            'confidence': torch.sigmoid(self.confidence_head(context_encoded)),
            'timing_logits': self.timing_head(context_encoded)
        }

class GameActionNet(nn.Module):
    """Neural network for game actions (movement, tasks, kills, etc.)"""
    
    def __init__(self, state_dim: int = 100, action_dim: int = 20, hidden_dim: int = 512):
        super(GameActionNet, self).__init__()
        
        # Game state encoder
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Role-specific action heads
        self.impostor_action_head = nn.Linear(hidden_dim // 2, action_dim)
        self.crewmate_action_head = nn.Linear(hidden_dim // 2, action_dim)
        
        # Value estimation
        self.value_head = nn.Linear(hidden_dim // 2, 1)
        
        # Action masking (which actions are valid)
        self.action_mask_head = nn.Linear(hidden_dim // 2, action_dim)
        
    def forward(self, state_features: torch.Tensor, role: str) -> Dict[str, torch.Tensor]:
        state_encoded = self.state_encoder(state_features)
        
        # Select appropriate action head based on role
        if role in ["Impostor", "Shapeshifter", "Phantom"]:
            action_logits = self.impostor_action_head(state_encoded)
        else:
            action_logits = self.crewmate_action_head(state_encoded)
        
        return {
            'action_logits': action_logits,
            'state_value': self.value_head(state_encoded),
            'action_mask': torch.sigmoid(self.action_mask_head(state_encoded))
        }

class UltimateAmongUsAgent(BaseAgent):
    """Ultimate Among Us RL Agent with multi-modal learning"""
    
    def __init__(self, player_color: str, role_name: str, 
                 learning_rate: float = 1e-4, device: str = "cpu",
                 ollama_client: Optional[OllamaClient] = None):
        super().__init__(player_color)
        self.role_name = role_name
        self.device = torch.device(device)
        self.ollama_client = ollama_client
        
        # Neural networks
        self.game_net = GameActionNet().to(self.device)
        self.social_net = SocialDeductionNet().to(self.device)
        self.comm_net = CommunicationNet().to(self.device)
        
        # Optimizers
        self.game_optimizer = torch.optim.AdamW(self.game_net.parameters(), lr=learning_rate)
        self.social_optimizer = torch.optim.AdamW(self.social_net.parameters(), lr=learning_rate)
        self.comm_optimizer = torch.optim.AdamW(self.comm_net.parameters(), lr=learning_rate)
        
        # Action mapping
        self.action_list = list(VALID_ACTIONS)
        self.action_to_idx = {action: idx for idx, action in enumerate(self.action_list)}
        self.idx_to_action = {idx: action for action, idx in self.action_to_idx.items()}
        
        # Advanced experience replay with multiple buffers
        self.game_buffer = PrioritizedReplayBuffer(capacity=20000)
        self.social_buffer = PrioritizedReplayBuffer(capacity=10000)
        self.comm_buffer = PrioritizedReplayBuffer(capacity=5000)
        
        # Training parameters
        self.epsilon = 0.3
        self.epsilon_decay = 0.9995
        self.epsilon_min = 0.05
        self.gamma = 0.99
        self.batch_size = 64
        
        # Social deduction tracking
        self.player_behaviors = defaultdict(list)  # Track other players' behaviors
        self.suspicion_history = defaultdict(list)  # Track suspicion over time
        self.voting_patterns = defaultdict(list)    # Track voting patterns
        self.communication_patterns = defaultdict(list)  # Track communication styles
        
        # Performance tracking
        self.episode_rewards = []
        self.win_rates = {"Impostor": [], "Crewmate": []}
        self.strategy_evolution = []
        
        # LLM integration
        if ollama_client:
            self.llm_comm_strategy = LLMCommunicationStrategy(
                player_color, role_name, ollama_client
            )
        
        # Training state
        self.training = True
        self.last_observation = None
        self.last_action = None
        self.current_episode_reward = 0
        
    def encode_game_state(self, observation: Dict) -> torch.Tensor:
        """Encode game state for action network"""
        features = []
        
        # Basic game state
        features.extend([
            observation.get("tick", 0) / 200.0,
            len(observation.get("players_in_room", [])) / 10.0,
            len(observation.get("connected_rooms", [])) / 15.0,
            len(observation.get("dead_bodies", [])) / 5.0,
            observation.get("task_progress", 0.0),
            observation.get("completed_tasks", 0) / 50.0,
            observation.get("total_tasks", 0) / 50.0,
            observation.get("living_players", 0) / 10.0,
            observation.get("living_impostors", 0) / 3.0,
        ])
        
        # Player abilities
        abilities = ["can_kill", "can_vent", "can_sabotage", "can_shapeshift", "can_protect", "can_emergency"]
        for ability in abilities:
            features.append(1.0 if observation.get(ability, False) else 0.0)
        
        # Room features
        room_features = observation.get("room_features", {})
        room_abilities = ["emergency_button", "admin_table", "security_cameras", "vitals", "lights_on", "doors_open"]
        for feature in room_abilities:
            features.append(1.0 if room_features.get(feature, False) else 0.0)
        
        # Sabotage state
        sabotage_types = ["lights", "oxygen", "reactor", "communications", "doors"]
        active_sabotages = observation.get("active_sabotages", [])
        for sabotage in sabotage_types:
            features.append(1.0 if sabotage in active_sabotages else 0.0)
        
        # Crisis timers
        crisis_timers = observation.get("crisis_timers", {})
        for sabotage in ["oxygen", "reactor"]:
            timer = crisis_timers.get(sabotage, 0)
            features.append(timer / 30.0 if timer else 0.0)
        
        # Game phase
        features.extend([
            1.0 if observation.get("in_meeting", False) else 0.0,
            1.0 if observation.get("in_discussion", False) else 0.0,
            1.0 if observation.get("in_voting", False) else 0.0,
        ])
        
        # Strategic features
        features.extend([
            observation.get("isolation_score", 0.0),  # How isolated we are
            observation.get("witness_count", 0) / 5.0,  # How many witnesses around
            observation.get("suspicion_level", 0.0),   # How suspicious others think we are
        ])
        
        # Pad to fixed size
        while len(features) < 100:
            features.append(0.0)
        
        return torch.FloatTensor(features[:100]).unsqueeze(0).to(self.device)
    
    def encode_social_state(self, observation: Dict) -> torch.Tensor:
        """Encode social deduction features"""
        features = []
        
        # Player behavior patterns
        for i in range(8):  # Max 8 other players
            if i < len(self.player_behaviors):
                behaviors = list(self.player_behaviors.values())[i]
                if behaviors:
                    # Recent behavior statistics
                    features.extend([
                        np.mean([b.get('aggression', 0) for b in behaviors[-5:]]),
                        np.mean([b.get('defensiveness', 0) for b in behaviors[-5:]]),
                        np.mean([b.get('activity', 0) for b in behaviors[-5:]]),
                        len([b for b in behaviors[-10:] if b.get('voted_someone', False)]) / 10.0,
                    ])
                else:
                    features.extend([0.0, 0.0, 0.0, 0.0])
            else:
                features.extend([0.0, 0.0, 0.0, 0.0])
        
        # Voting patterns
        features.extend([
            len(self.voting_patterns) / 10.0,  # Total votes cast
            observation.get("votes_against_me", 0) / 5.0,  # Votes against us
        ])
        
        # Communication patterns
        features.extend([
            len(self.communication_patterns) / 20.0,  # Total messages
            observation.get("accusations_made", 0) / 5.0,  # Accusations we made
            observation.get("accusations_received", 0) / 5.0,  # Accusations against us
        ])
        
        # Meeting dynamics
        features.extend([
            observation.get("meeting_count", 0) / 10.0,
            observation.get("emergency_meetings_called", 0) / 3.0,
            observation.get("bodies_reported", 0) / 5.0,
        ])
        
        # Pad to fixed size
        while len(features) < 128:
            features.append(0.0)
        
        return torch.FloatTensor(features[:128]).unsqueeze(0).to(self.device)
    
    def encode_communication_context(self, observation: Dict, discussion_context: Dict) -> torch.Tensor:
        """Encode communication context for strategic messaging"""
        features = []
        
        # Discussion state
        features.extend([
            discussion_context.get("discussion_phase", 0) / 3.0,  # Early/Mid/Late
            len(discussion_context.get("messages", [])) / 20.0,   # Message count
            discussion_context.get("time_remaining", 0) / 120.0,  # Time left
        ])
        
        # Recent accusations and defenses
        messages = discussion_context.get("messages", [])
        recent_accusations = len([m for m in messages[-10:] if m.get("type") == "accusation"])
        recent_defenses = len([m for m in messages[-10:] if m.get("type") == "defense"])
        
        features.extend([
            recent_accusations / 5.0,
            recent_defenses / 5.0,
        ])
        
        # Our involvement
        our_messages = [m for m in messages if m.get("sender") == self.player_color]
        features.extend([
            len(our_messages) / 10.0,
            1.0 if any(m.get("target") == self.player_color for m in messages[-5:]) else 0.0,  # Recently accused
        ])
        
        # Social dynamics
        features.extend([
            observation.get("suspicion_level", 0.0),
            observation.get("trust_level", 0.5),
            observation.get("alliance_strength", 0.0),
        ])
        
        # Game context
        features.extend([
            observation.get("living_players", 0) / 10.0,
            observation.get("living_impostors", 0) / 3.0,
            observation.get("task_progress", 0.0),
        ])
        
        # Pad to fixed size
        while len(features) < 200:
            features.append(0.0)
        
        return torch.FloatTensor(features[:200]).unsqueeze(0).to(self.device)

    def choose_action(self, observation: Dict) -> Action:
        """Choose action using advanced multi-modal decision making"""

        # Encode different aspects of the game state
        game_state = self.encode_game_state(observation)
        social_state = self.encode_social_state(observation)

        # Get network predictions
        with torch.no_grad():
            game_outputs = self.game_net(game_state, self.role_name)
            social_outputs = self.social_net(social_state)

        action_logits = game_outputs['action_logits']
        action_mask = game_outputs['action_mask']

        # Apply action masking (only consider valid actions)
        masked_logits = action_logits + (action_mask - 1) * 1e9

        # Advanced action selection with role-specific strategies
        if self.training and random.random() < self.epsilon:
            action_idx = self._strategic_exploration(observation, social_outputs)
        else:
            # Greedy action with social considerations
            action_idx = self._strategic_action_selection(masked_logits, social_outputs, observation)

        # Convert to action (ensure valid index)
        action_idx = min(action_idx, len(self.action_list) - 1)
        action_type = self.idx_to_action[action_idx]
        target = self._get_strategic_target(action_type, observation, social_outputs)

        # Store for learning
        if self.training:
            self.last_observation = observation
            self.last_action = action_idx

        # Update behavioral tracking
        self._update_behavioral_tracking(observation, action_type)

        return Action(action_type, target=target)

    def _strategic_exploration(self, observation: Dict, social_outputs: Dict) -> int:
        """Strategic exploration based on role and social context"""

        if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
            # Impostor exploration strategy
            if observation.get("isolation_score", 0) > 0.7:  # High isolation
                # Prefer aggressive actions when isolated
                preferred_actions = ["kill", "sabotage_lights", "sabotage_oxygen", "vent"]
            elif observation.get("suspicion_level", 0) > 0.6:  # High suspicion
                # Play defensively when suspicious
                preferred_actions = ["task", "move", "idle"]
            else:
                # Balanced impostor play
                preferred_actions = ["kill", "sabotage_lights", "vent", "task"]
        else:
            # Crewmate exploration strategy
            if len(observation.get("active_sabotages", [])) > 0:
                # Prioritize fixing sabotages
                preferred_actions = ["fix_lights", "fix_oxygen", "fix_reactor", "fix_comms"]
            elif observation.get("dead_bodies", []):
                # Report bodies
                preferred_actions = ["report", "emergency"]
            else:
                # Normal crewmate tasks
                preferred_actions = ["task", "use_admin", "use_security", "move"]

        # Create weighted distribution
        weights = []
        for action in self.action_list:
            if action in preferred_actions:
                weights.append(3.0)
            elif observation.get(f"can_{action.split('_')[0]}", False):
                weights.append(1.0)
            else:
                weights.append(0.1)

        # Sample from weighted distribution
        weights = np.array(weights)
        weights = weights / weights.sum()
        return np.random.choice(len(self.action_list), p=weights)

    def _strategic_action_selection(self, action_logits: torch.Tensor,
                                  social_outputs: Dict, observation: Dict) -> int:
        """Strategic action selection considering social dynamics"""

        # Get base action preferences
        action_probs = F.softmax(action_logits, dim=-1)

        # Modify based on social context
        suspicion_logits = social_outputs['suspicion_logits']
        trust_logits = social_outputs['trust_logits']

        # If we're highly suspected, avoid suspicious actions
        if observation.get("suspicion_level", 0) > 0.7:
            suspicious_actions = ["kill", "vent", "sabotage_lights", "sabotage_oxygen"]
            for action in suspicious_actions:
                if action in self.action_to_idx:
                    idx = self.action_to_idx[action]
                    action_probs[0, idx] *= 0.1  # Heavily penalize suspicious actions

        # If there's a crisis, prioritize fixing it
        active_sabotages = observation.get("active_sabotages", [])
        if "oxygen" in active_sabotages or "reactor" in active_sabotages:
            fix_actions = ["fix_oxygen", "fix_reactor"]
            for action in fix_actions:
                if action in self.action_to_idx:
                    idx = self.action_to_idx[action]
                    action_probs[0, idx] *= 5.0  # Boost crisis fix actions

        return action_probs.argmax().item()

    def _get_strategic_target(self, action_type: str, observation: Dict,
                            social_outputs: Dict) -> Optional[str]:
        """Get strategic target based on social deduction"""

        if action_type == "move":
            connected_rooms = observation.get("connected_rooms", [])
            if connected_rooms:
                # Strategic movement based on role
                if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
                    # Impostors prefer isolated areas or areas with targets
                    isolated_rooms = [r for r in connected_rooms if observation.get(f"{r}_player_count", 0) <= 1]
                    return random.choice(isolated_rooms) if isolated_rooms else random.choice(connected_rooms)
                else:
                    # Crewmates prefer populated areas for safety
                    populated_rooms = [r for r in connected_rooms if observation.get(f"{r}_player_count", 0) > 1]
                    return random.choice(populated_rooms) if populated_rooms else random.choice(connected_rooms)

        elif action_type == "kill":
            players_in_room = observation.get("players_in_room", [])
            if players_in_room:
                # Use social deduction to pick best target
                suspicion_logits = social_outputs.get('suspicion_logits', torch.zeros(8))
                # Target least suspicious players (they're less likely to be voted out)
                target_scores = []
                for player in players_in_room:
                    if player != self.player_color:
                        # Lower suspicion = better target
                        player_idx = hash(player) % 8  # Simple mapping
                        suspicion_score = suspicion_logits[0, player_idx].item()
                        target_scores.append((player, -suspicion_score))  # Negative for least suspicious

                if target_scores:
                    target_scores.sort(key=lambda x: x[1], reverse=True)
                    return target_scores[0][0]

        elif action_type in ["accusation", "vote"]:
            # Use social deduction for accusations/voting
            living_players = observation.get("living_players", [])
            if living_players:
                suspicion_logits = social_outputs.get('suspicion_logits', torch.zeros(8))
                # Target most suspicious player
                best_target = None
                best_score = -float('inf')

                for player in living_players:
                    if player != self.player_color:
                        player_idx = hash(player) % 8
                        suspicion_score = suspicion_logits[0, player_idx].item()
                        if suspicion_score > best_score:
                            best_score = suspicion_score
                            best_target = player

                return best_target

        # Default target selection
        return self._get_default_target(action_type, observation)

    def _get_default_target(self, action_type: str, observation: Dict) -> Optional[str]:
        """Default target selection logic"""

        if action_type == "move":
            connected_rooms = observation.get("connected_rooms", [])
            return random.choice(connected_rooms) if connected_rooms else None
        elif action_type in ["kill", "vote", "accusation"]:
            players_in_room = observation.get("players_in_room", [])
            valid_targets = [p for p in players_in_room if p != self.player_color]
            return random.choice(valid_targets) if valid_targets else None
        elif action_type == "vent":
            vents = observation.get("vents", [])
            return random.choice(vents) if vents else None

        return None

    def _update_behavioral_tracking(self, observation: Dict, action_type: str):
        """Update behavioral tracking for social deduction"""

        # Track our own behavior patterns
        behavior_data = {
            'tick': observation.get('tick', 0),
            'action': action_type,
            'room': observation.get('current_room', 'Unknown'),
            'players_present': len(observation.get('players_in_room', [])),
            'suspicion_level': observation.get('suspicion_level', 0),
        }

        self.player_behaviors[self.player_color].append(behavior_data)

        # Keep only recent behaviors
        if len(self.player_behaviors[self.player_color]) > 50:
            self.player_behaviors[self.player_color] = self.player_behaviors[self.player_color][-50:]

    def generate_strategic_communication(self, observation: Dict, discussion_context: Dict) -> List[Message]:
        """Generate strategic communication using neural networks and LLM"""

        # Encode communication context
        comm_context = self.encode_communication_context(observation, discussion_context)

        with torch.no_grad():
            comm_outputs = self.comm_net(comm_context)
            social_outputs = self.social_net(self.encode_social_state(observation))

        # Decide whether to speak
        timing_probs = F.softmax(comm_outputs['timing_logits'], dim=-1)
        should_speak = timing_probs[0, 1].item() > 0.4  # Middle timing threshold

        if not should_speak:
            return []

        # Select message type strategically
        message_type_probs = F.softmax(comm_outputs['message_type_logits'], dim=-1)
        message_type_idx = message_type_probs.argmax().item()
        message_types = list(MessageType)
        message_type = message_types[min(message_type_idx, len(message_types) - 1)]

        # Select target
        target_probs = F.softmax(comm_outputs['target_logits'], dim=-1)
        living_players = [p for p in observation.get("living_players", []) if p != self.player_color]

        target = None
        if living_players and message_type in [MessageType.ACCUSATION, MessageType.QUESTION]:
            # Use suspicion levels to pick target
            suspicion_logits = social_outputs['suspicion_logits']
            best_target_idx = suspicion_logits.argmax().item()
            if best_target_idx < len(living_players):
                target = living_players[best_target_idx]

        # Get confidence level
        confidence = comm_outputs['confidence'][0, 0].item()

        # Generate message content
        if self.llm_comm_strategy:
            # Use LLM for natural language generation
            game_state = self._build_llm_game_state(observation)
            llm_messages = self.llm_comm_strategy.generate_messages(game_state, discussion_context)
            if llm_messages:
                return llm_messages

        # Fallback to rule-based message generation
        content = self._generate_strategic_message_content(message_type, target, observation, confidence)

        if content:
            return [Message(self.player_color, message_type, content, target=target, confidence=confidence)]

        return []

    def _generate_strategic_message_content(self, message_type: MessageType, target: Optional[str],
                                          observation: Dict, confidence: float) -> Optional[str]:
        """Generate strategic message content based on game state"""

        if message_type == MessageType.ACCUSATION and target:
            if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
                # Impostor accusations - deflect suspicion
                accusations = [
                    f"{target} was acting suspicious near the body",
                    f"I saw {target} in a vent earlier",
                    f"{target} has been following people around",
                    f"{target} was nowhere near their tasks"
                ]
            else:
                # Crewmate accusations - based on observations
                accusations = [
                    f"{target} was in the area when the lights went out",
                    f"I haven't seen {target} doing any tasks",
                    f"{target} was acting strange during the last meeting",
                    f"{target} keeps changing their story"
                ]
            return random.choice(accusations)

        elif message_type == MessageType.ALIBI:
            # Provide alibi based on role
            if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
                alibis = [
                    "I was doing wires in Electrical",
                    "I was scanning in MedBay",
                    "I was doing card swipe in Admin",
                    "I was fixing the reactor"
                ]
            else:
                alibis = [
                    "I was doing my tasks in Navigation",
                    "I was monitoring cameras in Security",
                    "I was checking vitals in Office",
                    "I was calibrating in Reactor"
                ]
            return random.choice(alibis)

        elif message_type == MessageType.DEFENSE:
            defenses = [
                "That's not true, I was nowhere near there",
                "I can prove I was doing tasks",
                "Someone else must have done it",
                "I would never hurt anyone"
            ]
            return random.choice(defenses)

        elif message_type == MessageType.QUESTION and target:
            questions = [
                f"Where were you when the body was found, {target}?",
                f"Can anyone vouch for {target}?",
                f"What tasks were you doing, {target}?",
                f"Did anyone see {target} recently?"
            ]
            return random.choice(questions)

        elif message_type == MessageType.INFORMATION:
            # Share strategic information
            if len(observation.get("active_sabotages", [])) > 0:
                return "We need to fix the sabotage quickly!"
            elif observation.get("dead_bodies", []):
                return "I found the body and reported it immediately"
            else:
                return "I've been doing my tasks and staying alert"

        return None

    def _build_llm_game_state(self, observation: Dict) -> Dict:
        """Build game state for LLM communication"""
        return {
            'role': self.role_name,
            'living_players': observation.get('living_players', 0),
            'dead_bodies': len(observation.get('dead_bodies', [])),
            'active_sabotages': observation.get('active_sabotages', []),
            'task_progress': observation.get('task_progress', 0),
            'suspicion_level': observation.get('suspicion_level', 0),
            'in_meeting': observation.get('in_meeting', False)
        }

    def learn_from_experience(self, reward: float, next_observation: Dict, done: bool):
        """Advanced multi-modal learning from experience"""

        if not self.training or self.last_observation is None:
            return

        # Update episode reward
        self.current_episode_reward += reward

        # Store experience in appropriate buffers
        game_experience = {
            'state': self.encode_game_state(self.last_observation),
            'action': self.last_action,
            'reward': reward,
            'next_state': self.encode_game_state(next_observation),
            'done': done
        }

        social_experience = {
            'state': self.encode_social_state(self.last_observation),
            'reward': reward,
            'next_state': self.encode_social_state(next_observation),
            'done': done
        }

        # Add to replay buffers
        self.game_buffer.push(game_experience, abs(reward) + 0.1)
        self.social_buffer.push(social_experience, abs(reward) + 0.1)

        # Train networks if we have enough experience
        if len(self.game_buffer) >= self.batch_size:
            self._train_game_network()

        if len(self.social_buffer) >= self.batch_size:
            self._train_social_network()

        # Update exploration
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        # Episode end processing
        if done:
            self.episode_rewards.append(self.current_episode_reward)
            self._update_performance_metrics(next_observation)
            self.current_episode_reward = 0

    def _train_game_network(self):
        """Train the game action network"""
        try:
            batch = self.game_buffer.sample(self.batch_size)
            if batch is None:
                return

            experiences, indices, weights = batch

            states = torch.cat([exp['state'] for exp in experiences])
            actions = torch.LongTensor([exp['action'] for exp in experiences]).to(self.device)
            rewards = torch.FloatTensor([exp['reward'] for exp in experiences]).to(self.device)
            next_states = torch.cat([exp['next_state'] for exp in experiences])
            dones = torch.BoolTensor([exp['done'] for exp in experiences]).to(self.device)
            weights = torch.FloatTensor(weights).to(self.device)

            # Current Q values
            current_outputs = self.game_net(states, self.role_name)
            # Clamp actions to valid range to prevent index errors
            actions_clamped = torch.clamp(actions, 0, current_outputs['action_logits'].size(1) - 1)
            current_q_values = current_outputs['action_logits'].gather(1, actions_clamped.unsqueeze(1))

            # Target Q values
            with torch.no_grad():
                next_outputs = self.game_net(next_states, self.role_name)
                next_q_values = next_outputs['action_logits'].max(1)[0]
                target_q_values = rewards + (self.gamma * next_q_values * ~dones)

            # Compute loss with importance sampling
            td_errors = target_q_values - current_q_values.squeeze()
            loss = (weights * td_errors.pow(2)).mean()

            # Optimize
            self.game_optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.game_net.parameters(), 1.0)
            self.game_optimizer.step()

            # Update priorities (detach from computation graph)
            self.game_buffer.update_priorities(indices, td_errors.abs().detach().cpu().numpy())

        except Exception as e:
            print(f"⚠️  Game network training error: {e}")
            # Continue training even if one step fails

    def _train_social_network(self):
        """Train the social deduction network"""
        try:
            batch = self.social_buffer.sample(self.batch_size)
            if batch is None:
                return

            experiences, _, weights = batch  # indices not used in social training

            # Check if we have valid experiences
            if not experiences or len(experiences) == 0:
                return

            states = torch.cat([exp['state'] for exp in experiences])
            rewards = torch.FloatTensor([exp['reward'] for exp in experiences]).to(self.device)
            weights = torch.FloatTensor(weights).to(self.device)

            # Train social prediction with proper gradient handling
            social_outputs = self.social_net(states)

            # Simple reward-based learning for social features
            # Use a small constant loss to maintain gradients
            social_loss = torch.tensor(0.01, requires_grad=True, device=self.device)
            social_loss = social_loss * (weights * rewards).mean()

            if social_loss.requires_grad:
                self.social_optimizer.zero_grad()
                social_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.social_net.parameters(), 1.0)
                self.social_optimizer.step()

        except Exception as e:
            print(f"⚠️  Social network training error: {e}")
            # Continue training even if one step fails

    def _update_performance_metrics(self, final_observation: Dict):
        """Update performance tracking metrics"""

        winner = final_observation.get('winner', 'None')

        # Update role-specific win rates
        if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
            won = (winner == "Impostors")
            self.win_rates["Impostor"].append(1.0 if won else 0.0)
        else:
            won = (winner == "Crewmates")
            self.win_rates["Crewmate"].append(1.0 if won else 0.0)

        # Keep only recent performance
        for role in self.win_rates:
            if len(self.win_rates[role]) > 100:
                self.win_rates[role] = self.win_rates[role][-100:]

    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        stats = {
            'total_episodes': len(self.episode_rewards),
            'average_reward': np.mean(self.episode_rewards[-100:]) if self.episode_rewards else 0,
            'epsilon': self.epsilon,
        }

        for role in self.win_rates:
            if self.win_rates[role]:
                stats[f'{role.lower()}_win_rate'] = np.mean(self.win_rates[role])
                stats[f'{role.lower()}_games'] = len(self.win_rates[role])

        return stats

    def save_model(self, filepath: str):
        """Save all model components"""
        torch.save({
            'game_net': self.game_net.state_dict(),
            'social_net': self.social_net.state_dict(),
            'comm_net': self.comm_net.state_dict(),
            'game_optimizer': self.game_optimizer.state_dict(),
            'social_optimizer': self.social_optimizer.state_dict(),
            'comm_optimizer': self.comm_optimizer.state_dict(),
            'epsilon': self.epsilon,
            'performance_stats': self.get_performance_stats(),
            'player_behaviors': dict(self.player_behaviors),
        }, filepath)

    def load_model(self, filepath: str):
        """Load all model components"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.game_net.load_state_dict(checkpoint['game_net'])
        self.social_net.load_state_dict(checkpoint['social_net'])
        self.comm_net.load_state_dict(checkpoint['comm_net'])
        self.game_optimizer.load_state_dict(checkpoint['game_optimizer'])
        self.social_optimizer.load_state_dict(checkpoint['social_optimizer'])
        self.comm_optimizer.load_state_dict(checkpoint['comm_optimizer'])
        self.epsilon = checkpoint['epsilon']

        if 'player_behaviors' in checkpoint:
            self.player_behaviors = defaultdict(list, checkpoint['player_behaviors'])

class PrioritizedReplayBuffer:
    """Prioritized experience replay buffer for advanced RL training"""

    def __init__(self, capacity: int = 50000, alpha: float = 0.6):
        self.capacity = capacity
        self.alpha = alpha
        self.buffer = []
        self.priorities = []
        self.position = 0

    def push(self, experience: Dict, priority: float = 1.0):
        """Add experience with priority"""
        priority = (priority + 1e-6) ** self.alpha

        if len(self.buffer) < self.capacity:
            self.buffer.append(experience)
            self.priorities.append(priority)
        else:
            self.buffer[self.position] = experience
            self.priorities[self.position] = priority

        self.position = (self.position + 1) % self.capacity

    def sample(self, batch_size: int, beta: float = 0.4):
        """Sample batch with importance sampling"""
        if len(self.buffer) < batch_size:
            return None

        priorities = np.array(self.priorities[:len(self.buffer)])
        probs = priorities / priorities.sum()

        indices = np.random.choice(len(self.buffer), batch_size, p=probs)
        experiences = [self.buffer[idx] for idx in indices]

        # Importance sampling weights
        weights = (len(self.buffer) * probs[indices]) ** (-beta)
        weights = weights / weights.max()

        return experiences, indices, weights

    def update_priorities(self, indices: List[int], priorities: np.ndarray):
        """Update priorities based on TD errors"""
        for idx, priority in zip(indices, priorities):
            # Ensure priority is a scalar value
            priority_val = float(priority) if hasattr(priority, 'item') else priority
            self.priorities[idx] = (priority_val + 1e-6) ** self.alpha

    def __len__(self):
        return len(self.buffer)
