from core.action import Action

class BaseAgent:
    def __init__(self, player_color: str):
        self.player_color = player_color

    def observe(self, observation: dict):
        """Optional hook to process the observation before deciding."""
        self.last_observation = observation

    def choose_action(self, observation: dict) -> Action:
        """
        Must be implemented by subclasses.
        Returns an Action object representing the agent's decision.
        """
        raise NotImplementedError("choose_action must be implemented by subclass")