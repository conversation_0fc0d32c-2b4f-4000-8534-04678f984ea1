class Role:
    def __init__(self, name, can_vent=False, has_fake_tasks=False, has_real_tasks=True,
                 special_abilities=None, vision_modifier=1.0, can_sabotage=False):
        self.name = name
        self.can_vent = can_vent
        self.has_fake_tasks = has_fake_tasks
        self.has_real_tasks = has_real_tasks
        self.special_abilities = special_abilities or []
        self.vision_modifier = vision_modifier  # Multiplier for vision range
        self.can_sabotage = can_sabotage

    def __repr__(self):
        return f"Role({self.name})"

# Basic Roles
CREWMATE = Role("Crewmate", can_vent=False, has_fake_tasks=False, has_real_tasks=True)
IMPOSTOR = Role("Impostor", can_vent=True, has_fake_tasks=True, has_real_tasks=False,
                special_abilities=["kill", "sabotage", "vent"], can_sabotage=True)

# Advanced Crewmate Roles
ENGINEER = Role("Engineer", can_vent=True, has_fake_tasks=False, has_real_tasks=True,
                special_abilities=["vent"])
SCIENTIST = Role("Scientist", can_vent=False, has_fake_tasks=False, has_real_tasks=True,
                 special_abilities=["vitals_anywhere"])
GUARDIAN_ANGEL = Role("Guardian Angel", can_vent=False, has_fake_tasks=False, has_real_tasks=False,
                      special_abilities=["protect"])
NOISEMAKER = Role("Noisemaker", can_vent=False, has_fake_tasks=False, has_real_tasks=True,
                  special_abilities=["alert_on_death"])
TRACKER = Role("Tracker", can_vent=False, has_fake_tasks=False, has_real_tasks=True,
               special_abilities=["track_player"])

# Advanced Impostor Roles
SHAPESHIFTER = Role("Shapeshifter", can_vent=True, has_fake_tasks=True, has_real_tasks=False,
                    special_abilities=["kill", "sabotage", "vent", "shapeshift"], can_sabotage=True)
PHANTOM = Role("Phantom", can_vent=True, has_fake_tasks=True, has_real_tasks=False,
               special_abilities=["kill", "sabotage", "vent", "vanish"], can_sabotage=True,
               vision_modifier=0.5)  # Reduced vision