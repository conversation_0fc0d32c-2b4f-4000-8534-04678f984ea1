#!/usr/bin/env python3
"""
CPU-Safe Among Us AI Training
Simplified version that avoids CUDA issues
"""

import os
import sys
# Fix OpenMP warning
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import torch
import argparse
from datetime import datetime
from typing import Dict

from soma.agents.agents.ultimate_rl_agent import UltimateAmongUsAgent
from soma.training.training.curriculum_learning import AdaptiveCurriculumLearning
from soma.analytics.advanced_analytics import AdvancedTrainingAnalytics

class SafeAmongUsTrainer:
    """Safe CPU-based trainer for Among Us AI"""

    def __init__(self, episodes: int = 100, enable_llm: bool = False):
        self.episodes = episodes
        self.device = "cpu"  # Force CPU to avoid CUDA issues
        self.enable_llm = enable_llm

        # Initialize LLM client if enabled
        self.ollama_client = None
        if enable_llm:
            try:
                from soma.core.core.llm_communication import OllamaClient
                self.ollama_client = OllamaClient(model="deepseek-r1:latest")
                print("✅ DeepSeek-R1 LLM communication enabled")
            except Exception as e:
                print(f"⚠️  LLM communication disabled: {e}")

        # Create main RL agent
        self.agent = UltimateAmongUsAgent(
            player_color="Red",
            role_name="Crewmate",
            learning_rate=1e-3,  # Slightly higher LR for CPU
            device=self.device,
            ollama_client=self.ollama_client
        )
        
        # Initialize curriculum learning
        self.curriculum = AdaptiveCurriculumLearning(self.agent, self.device)
        
        # Initialize analytics
        self.analytics = AdvancedTrainingAnalytics("data/analytics")
        
        print(f"🚀 Safe CPU Training Initialized")
        print(f"📊 Device: {self.device}")
        print(f"🎯 Episodes: {self.episodes}")
    
    def train(self):
        """Safe training loop"""
        
        episode = 0
        successful_episodes = 0
        
        try:
            while episode < self.episodes:
                try:
                    # Get current curriculum configuration
                    curriculum_config = self.curriculum.get_current_config()
                    
                    # Create training game
                    game = self.curriculum.create_training_game(curriculum_config)
                    
                    # Update agent's role
                    agent_player = next(p for p in game.players if p.agent == self.agent)
                    self.agent.role_name = agent_player.role.name
                    
                    # Run simplified episode
                    episode_data = self._run_safe_episode(game, curriculum_config)
                    
                    # Record results
                    self.analytics.record_episode(episode_data)
                    self.curriculum.record_episode_result(
                        episode_data['won'], 
                        episode_data.get('performance_metrics', {})
                    )
                    
                    successful_episodes += 1
                    
                    # Progress logging
                    if episode % 5 == 0:
                        self._log_progress(episode, episode_data)
                    
                    # Curriculum advancement
                    if self.curriculum.should_advance_stage():
                        self.curriculum.advance_to_next_stage()
                    
                except Exception as e:
                    print(f"⚠️  Episode {episode} failed: {e}")
                    # Continue with next episode
                
                episode += 1
        
        except KeyboardInterrupt:
            print("\n⏸️  Training interrupted by user")
        
        print(f"\n🏁 Training completed!")
        print(f"📊 Successful episodes: {successful_episodes}/{episode}")
        
        # Generate final report
        try:
            report = self.analytics.generate_comprehensive_report()
            print(f"📈 Final report generated with {len(report)} sections")
        except Exception as e:
            print(f"⚠️  Report generation failed: {e}")
    
    def _run_safe_episode(self, game, curriculum_config) -> Dict:
        """Run a safe episode with error handling"""
        
        episode_start_time = datetime.now()
        actions_taken = []
        
        # Get agent player
        agent_player = next(p for p in game.players if p.agent == self.agent)
        
        step_count = 0
        max_steps = 50  # Shorter episodes for safety
        
        try:
            while not game.is_game_over() and step_count < max_steps:
                # Store previous observation
                try:
                    prev_observation = game._get_observation(agent_player)
                except:
                    prev_observation = self._get_safe_observation()
                
                # Run game tick
                game.run_tick()
                
                # Get new observation and calculate reward
                try:
                    new_observation = game._get_observation(agent_player)
                    reward = self._calculate_safe_reward(agent_player, game)
                    
                    # Safe learning step
                    self.agent.learn_from_experience(reward, new_observation, game.is_game_over())
                    
                except Exception as e:
                    print(f"⚠️  Learning step failed: {e}")
                    # Use default values
                    new_observation = self._get_safe_observation()
                    reward = 0.0
                
                # Track actions safely
                if hasattr(self.agent, 'last_action') and self.agent.last_action is not None:
                    try:
                        action_name = self.agent.idx_to_action.get(self.agent.last_action, 'idle')
                        actions_taken.append(action_name)
                    except:
                        actions_taken.append('unknown')
                
                step_count += 1
        
        except Exception as e:
            print(f"⚠️  Episode simulation failed: {e}")
        
        # Calculate results safely
        try:
            winner = game.get_winner() if game.is_game_over() else "None"
            won = self._did_agent_win(agent_player, winner)
        except:
            winner = "None"
            won = False
        
        episode_data = {
            'episode_duration': (datetime.now() - episode_start_time).total_seconds(),
            'steps': step_count,
            'role': getattr(agent_player.role, 'name', 'Unknown'),
            'won': won,
            'winner': winner,
            'reward': getattr(self.agent, 'current_episode_reward', 0.0),
            'actions_taken': actions_taken,
            'performance_metrics': {'steps': step_count, 'actions': len(actions_taken)},
            'curriculum_stage': curriculum_config.stage.value,
        }
        
        return episode_data
    
    def _get_safe_observation(self) -> Dict:
        """Get a safe default observation"""
        return {
            "tick": 0,
            "players_in_room": ["Red"],
            "connected_rooms": ["Cafeteria"],
            "dead_bodies": [],
            "task_progress": 0.0,
            "completed_tasks": 0,
            "total_tasks": 5,
            "living_players": 6,
            "living_impostors": 2,
            "can_kill": False,
            "can_vent": False,
            "can_sabotage": False,
            "active_sabotages": [],
            "in_meeting": False
        }
    
    def _calculate_safe_reward(self, player, game) -> float:
        """Calculate a safe reward"""
        try:
            base_reward = 0.1 if player.alive else -1.0
            
            if game.is_game_over():
                winner = game.get_winner()
                if self._did_agent_win(player, winner):
                    base_reward += 5.0
                else:
                    base_reward -= 2.0
            
            return base_reward
        except:
            return 0.0
    
    def _did_agent_win(self, agent_player, winner: str) -> bool:
        """Check if agent won"""
        try:
            if winner == "None":
                return False
            
            role_name = getattr(agent_player.role, 'name', 'Unknown')
            if role_name in ["Impostor", "Shapeshifter", "Phantom"]:
                return winner == "Impostors"
            else:
                return winner == "Crewmates"
        except:
            return False
    
    def _log_progress(self, episode: int, episode_data: Dict):
        """Log training progress"""
        
        curriculum_status = self.curriculum.get_curriculum_status()
        performance_stats = self.agent.get_performance_stats()
        
        print(f"Episode {episode:3d} | "
              f"Stage: {curriculum_status['current_stage'][:12]:12s} | "
              f"Role: {episode_data['role'][:8]:8s} | "
              f"Won: {'✅' if episode_data['won'] else '❌'} | "
              f"Reward: {episode_data['reward']:5.1f} | "
              f"Steps: {episode_data['steps']:2d}")

def main():
    parser = argparse.ArgumentParser(description="Safe CPU Training for Among Us AI")
    parser.add_argument("--episodes", type=int, default=100, help="Number of episodes")
    parser.add_argument("--llm", action="store_true", help="Enable DeepSeek-R1 LLM communication")

    args = parser.parse_args()

    print("🛡️  Safe CPU Among Us AI Training")
    print("=" * 40)
    print(f"Episodes: {args.episodes}")
    print(f"Device: CPU (safe mode)")
    print(f"LLM Communication: {'DeepSeek-R1' if args.llm else 'Disabled'}")
    print("=" * 40)

    trainer = SafeAmongUsTrainer(args.episodes, args.llm)
    trainer.train()

if __name__ == "__main__":
    main()
