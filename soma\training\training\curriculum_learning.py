import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import random

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.ultimate_rl_agent import UltimateAmongUsAgent
from agents.scripted_agent import ScriptedAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS
from training.role_manager import Role<PERSON><PERSON><PERSON>, create_role_config_for_stage

class CurriculumStage(Enum):
    """Different stages of curriculum learning"""
    BASIC_MOVEMENT = "basic_movement"
    TASK_COMPLETION = "task_completion"
    SABOTAGE_BASICS = "sabotage_basics"
    CRISIS_MANAGEMENT = "crisis_management"
    BASIC_SOCIAL = "basic_social"
    ADVANCED_DEDUCTION = "advanced_deduction"
    MASTER_LEVEL = "master_level"

@dataclass
class CurriculumConfig:
    """Configuration for a curriculum stage"""
    stage: CurriculumStage
    min_episodes: int
    success_threshold: float
    max_episodes: int
    game_settings: Dict
    opponent_types: List[str]
    reward_weights: Dict[str, float]
    description: str

class AdaptiveCurriculumLearning:
    """Adaptive curriculum learning system for Among Us AI"""
    
    def __init__(self, agent: UltimateAmongUsAgent, device: str = "cpu"):
        self.agent = agent
        self.device = device
        self.current_stage = CurriculumStage.BASIC_MOVEMENT
        self.stage_episodes = 0
        self.stage_performance = []

        # Define curriculum stages
        self.curriculum = self._build_curriculum()
        self.stage_history = []

        # Role management
        self.role_manager = None
        self._update_role_manager()
        
    def _build_curriculum(self) -> Dict[CurriculumStage, CurriculumConfig]:
        """Build the complete curriculum"""
        
        return {
            CurriculumStage.BASIC_MOVEMENT: CurriculumConfig(
                stage=CurriculumStage.BASIC_MOVEMENT,
                min_episodes=50,
                success_threshold=0.6,
                max_episodes=200,
                game_settings={
                    "enable_llm_communication": False,
                    "enable_sabotages": False,
                    "enable_security_systems": False,
                    "enable_advanced_roles": False,
                    "task_completion_win": True,
                },
                opponent_types=["passive", "basic"],
                reward_weights={
                    "movement": 2.0,
                    "task_completion": 3.0,
                    "survival": 1.0,
                    "win": 5.0
                },
                description="Learn basic movement and task completion"
            ),
            
            CurriculumStage.TASK_COMPLETION: CurriculumConfig(
                stage=CurriculumStage.TASK_COMPLETION,
                min_episodes=100,
                success_threshold=0.7,
                max_episodes=300,
                game_settings={
                    "enable_llm_communication": False,
                    "enable_sabotages": False,
                    "enable_security_systems": True,
                    "enable_advanced_roles": False,
                },
                opponent_types=["task_focused_crewmate", "basic"],
                reward_weights={
                    "task_completion": 4.0,
                    "security_usage": 2.0,
                    "survival": 1.5,
                    "win": 5.0
                },
                description="Master task completion and security systems"
            ),
            
            CurriculumStage.SABOTAGE_BASICS: CurriculumConfig(
                stage=CurriculumStage.SABOTAGE_BASICS,
                min_episodes=150,
                success_threshold=0.65,
                max_episodes=400,
                game_settings={
                    "enable_llm_communication": False,
                    "enable_sabotages": True,
                    "enable_security_systems": True,
                    "enable_advanced_roles": False,
                },
                opponent_types=["aggressive_impostor", "sneaky_impostor", "balanced"],
                reward_weights={
                    "sabotage_usage": 3.0,
                    "sabotage_fixing": 3.0,
                    "kill_success": 4.0,
                    "survival": 2.0,
                    "win": 6.0
                },
                description="Learn sabotage mechanics and basic impostor play"
            ),
            
            CurriculumStage.CRISIS_MANAGEMENT: CurriculumConfig(
                stage=CurriculumStage.CRISIS_MANAGEMENT,
                min_episodes=100,
                success_threshold=0.7,
                max_episodes=300,
                game_settings={
                    "enable_llm_communication": False,
                    "enable_sabotages": True,
                    "enable_security_systems": True,
                    "crisis_frequency": 0.8,  # High crisis frequency
                },
                opponent_types=["crisis_specialist", "balanced"],
                reward_weights={
                    "crisis_fix": 5.0,
                    "crisis_survival": 4.0,
                    "teamwork": 3.0,
                    "win": 6.0
                },
                description="Master crisis management and teamwork"
            ),
            
            CurriculumStage.BASIC_SOCIAL: CurriculumConfig(
                stage=CurriculumStage.BASIC_SOCIAL,
                min_episodes=200,
                success_threshold=0.6,
                max_episodes=500,
                game_settings={
                    "enable_llm_communication": True,
                    "enable_sabotages": True,
                    "enable_security_systems": True,
                    "meeting_frequency": 0.7,  # More meetings
                },
                opponent_types=["detective_crewmate", "social_impostor", "balanced"],
                reward_weights={
                    "communication_success": 3.0,
                    "voting_accuracy": 4.0,
                    "deception_success": 4.0,
                    "social_survival": 3.0,
                    "win": 7.0
                },
                description="Learn basic social deduction and communication"
            ),
            
            CurriculumStage.ADVANCED_DEDUCTION: CurriculumConfig(
                stage=CurriculumStage.ADVANCED_DEDUCTION,
                min_episodes=300,
                success_threshold=0.65,
                max_episodes=600,
                game_settings={
                    "enable_llm_communication": True,
                    "enable_sabotages": True,
                    "enable_security_systems": True,
                    "enable_advanced_roles": True,
                },
                opponent_types=["master_detective", "master_impostor", "adaptive"],
                reward_weights={
                    "deduction_accuracy": 5.0,
                    "strategic_communication": 4.0,
                    "advanced_tactics": 4.0,
                    "role_mastery": 3.0,
                    "win": 8.0
                },
                description="Master advanced social deduction and role-specific strategies"
            ),
            
            CurriculumStage.MASTER_LEVEL: CurriculumConfig(
                stage=CurriculumStage.MASTER_LEVEL,
                min_episodes=500,
                success_threshold=0.7,
                max_episodes=1000,
                game_settings={
                    "enable_llm_communication": True,
                    "enable_sabotages": True,
                    "enable_security_systems": True,
                    "enable_advanced_roles": True,
                    "difficulty": "master",
                },
                opponent_types=["grandmaster", "adaptive_expert"],
                reward_weights={
                    "perfect_play": 6.0,
                    "meta_strategy": 5.0,
                    "adaptation": 4.0,
                    "consistency": 4.0,
                    "win": 10.0
                },
                description="Achieve master-level play against expert opponents"
            )
        }
    
    def get_current_config(self) -> CurriculumConfig:
        """Get current curriculum configuration"""
        return self.curriculum[self.current_stage]
    
    def create_training_game(self, config: CurriculumConfig) -> SimulatedGame:
        """Create a training game for the current curriculum stage"""
        
        # Setup game settings
        settings = DEFAULT_SETTINGS.copy()
        settings.update(config.game_settings)
        
        # Create map
        rooms = create_skeld_map()
        cafeteria = rooms["Cafeteria"]
        
        # Determine roles
        num_players = 6  # Standard game size
        num_impostors = 2

        # All 18 official Among Us colors
        all_colors = [
            "Red", "Blue", "Green", "Pink", "Orange", "Yellow",
            "Black", "White", "Purple", "Brown", "Cyan", "Lime",
            "Maroon", "Rose", "Banana", "Gray", "Tan", "Coral"
        ]
        colors = all_colors[:num_players]
        
        # Assign roles using role manager
        if self.role_manager:
            assigned_roles = self.role_manager.assign_roles(num_players, num_impostors)
        else:
            # Fallback to basic roles
            assigned_roles = []
            impostor_colors = random.sample(colors, num_impostors)
            for color in colors:
                assigned_roles.append(IMPOSTOR if color in impostor_colors else CREWMATE)

        players = []
        rl_agent_added = False

        for i, color in enumerate(colors):
            role = assigned_roles[i] if i < len(assigned_roles) else CREWMATE
            
            if not rl_agent_added and color == "Red":
                # Add our RL agent
                player = Player(color, role, cafeteria, self.agent)
                rl_agent_added = True
            else:
                # Add scripted opponent
                strategy = self._select_opponent_strategy(config.opponent_types, role.name)
                agent = ScriptedAgent(color, strategy=strategy)
                player = Player(color, role, cafeteria, agent)
            
            players.append(player)
        
        return SimulatedGame(players, settings)
    
    def _select_opponent_strategy(self, opponent_types: List[str], role: str) -> str:
        """Select appropriate opponent strategy for current curriculum"""
        
        role_strategies = {
            "Impostor": ["aggressive_impostor", "sneaky_impostor", "social_impostor", "master_impostor"],
            "Crewmate": ["task_focused_crewmate", "detective_crewmate", "balanced", "master_detective"]
        }
        
        # Filter strategies based on curriculum requirements
        available_strategies = []
        for strategy in role_strategies[role]:
            if any(opponent_type in strategy for opponent_type in opponent_types):
                available_strategies.append(strategy)
        
        if not available_strategies:
            # Fallback to basic strategies
            available_strategies = ["balanced"]
        
        return random.choice(available_strategies)
    
    def calculate_curriculum_reward(self, base_reward: float, game_result: Dict, 
                                  config: CurriculumConfig) -> float:
        """Calculate reward with curriculum-specific weighting"""
        
        weighted_reward = base_reward
        
        # Apply curriculum-specific reward weights
        for reward_type, weight in config.reward_weights.items():
            if reward_type in game_result:
                weighted_reward += game_result[reward_type] * weight
        
        return weighted_reward
    
    def should_advance_stage(self) -> bool:
        """Check if agent should advance to next curriculum stage"""
        
        config = self.get_current_config()
        
        # Need minimum episodes
        if self.stage_episodes < config.min_episodes:
            return False
        
        # Check performance threshold
        if len(self.stage_performance) >= 20:  # Need at least 20 episodes for evaluation
            recent_performance = np.mean(self.stage_performance[-20:])
            if recent_performance >= config.success_threshold:
                return True
        
        # Force advancement if max episodes reached
        if self.stage_episodes >= config.max_episodes:
            return True
        
        return False
    
    def advance_to_next_stage(self):
        """Advance to the next curriculum stage"""
        
        stages = list(CurriculumStage)
        current_idx = stages.index(self.current_stage)
        
        if current_idx < len(stages) - 1:
            # Record stage completion
            self.stage_history.append({
                'stage': self.current_stage,
                'episodes': self.stage_episodes,
                'final_performance': np.mean(self.stage_performance[-20:]) if len(self.stage_performance) >= 20 else 0,
                'success': len(self.stage_performance) >= 20 and np.mean(self.stage_performance[-20:]) >= self.get_current_config().success_threshold
            })
            
            # Advance to next stage
            self.current_stage = stages[current_idx + 1]
            self.stage_episodes = 0
            self.stage_performance = []

            # Update role manager for new stage
            self._update_role_manager()

            print(f"🎓 Advanced to curriculum stage: {self.current_stage.value}")
            print(f"📚 Description: {self.get_current_config().description}")
        else:
            print("🏆 Curriculum completed! Agent has reached master level.")

    def _update_role_manager(self):
        """Update role manager based on current stage"""
        stage_name = self.current_stage.value
        role_config = create_role_config_for_stage(stage_name, enable_special_roles=True)
        self.role_manager = RoleManager(role_config)
    
    def record_episode_result(self, won: bool, performance_metrics: Dict):
        """Record the result of a training episode"""
        
        self.stage_episodes += 1
        
        # Calculate stage-specific performance score
        performance_score = self._calculate_performance_score(won, performance_metrics)
        self.stage_performance.append(performance_score)
        
        # Keep only recent performance for evaluation
        if len(self.stage_performance) > 100:
            self.stage_performance = self.stage_performance[-100:]
    
    def _calculate_performance_score(self, won: bool, metrics: Dict) -> float:
        """Calculate performance score for current curriculum stage"""
        
        config = self.get_current_config()
        score = 0.0
        
        # Base win/loss score
        score += 1.0 if won else 0.0
        
        # Stage-specific metrics
        if config.stage == CurriculumStage.BASIC_MOVEMENT:
            score += metrics.get('rooms_visited', 0) / 10.0
            score += metrics.get('tasks_attempted', 0) / 5.0
        
        elif config.stage == CurriculumStage.TASK_COMPLETION:
            score += metrics.get('tasks_completed', 0) / 3.0
            score += metrics.get('security_usage', 0) / 2.0
        
        elif config.stage == CurriculumStage.SABOTAGE_BASICS:
            score += metrics.get('sabotages_used', 0) / 3.0
            score += metrics.get('sabotages_fixed', 0) / 2.0
            score += metrics.get('kills_made', 0) / 2.0
        
        elif config.stage == CurriculumStage.CRISIS_MANAGEMENT:
            score += metrics.get('crises_fixed', 0) / 2.0
            score += 1.0 if metrics.get('survived_crisis', False) else 0.0
        
        elif config.stage in [CurriculumStage.BASIC_SOCIAL, CurriculumStage.ADVANCED_DEDUCTION]:
            score += metrics.get('correct_votes', 0) / 3.0
            score += metrics.get('successful_accusations', 0) / 2.0
            score += 1.0 if metrics.get('avoided_ejection', True) else 0.0
        
        return min(score, 2.0)  # Cap at 2.0 for normalization
    
    def get_curriculum_status(self) -> Dict:
        """Get current curriculum learning status"""
        
        config = self.get_current_config()
        
        return {
            'current_stage': self.current_stage.value,
            'stage_description': config.description,
            'episodes_completed': self.stage_episodes,
            'min_episodes': config.min_episodes,
            'max_episodes': config.max_episodes,
            'success_threshold': config.success_threshold,
            'current_performance': np.mean(self.stage_performance[-20:]) if len(self.stage_performance) >= 20 else 0,
            'ready_to_advance': self.should_advance_stage(),
            'stage_history': self.stage_history,
            'total_stages': len(CurriculumStage),
            'completed_stages': len(self.stage_history)
        }
