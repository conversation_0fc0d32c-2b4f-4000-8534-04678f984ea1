"""
Complete Among Us Role System
All official roles with their abilities and mechanics
"""

from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import random

class RoleType(Enum):
    """Role categories"""
    CREWMATE = "Crewmate"
    IMPOSTOR = "Impostor"
    NEUTRAL = "Neutral"

class RoleRarity(Enum):
    """Role availability"""
    ALWAYS = "always"          # Base roles always available
    COMMON = "common"          # Standard DLC roles
    UNCOMMON = "uncommon"      # Less common roles
    RARE = "rare"             # Special event/newer roles
    MODDED = "modded"         # Community/modded roles

@dataclass
class RoleAbility:
    """Represents a role ability"""
    name: str
    description: str
    cooldown: float = 0.0
    uses: Optional[int] = None  # None = unlimited
    range: float = 1.0
    requires_target: bool = False
    can_use_in_vents: bool = False
    visible_to_others: bool = False

@dataclass
class Role:
    """Complete role definition"""
    name: str
    role_type: RoleType
    rarity: RoleRarity
    description: str
    abilities: List[RoleAbility]
    passive_abilities: List[str]
    win_condition: str
    can_vent: bool = False
    can_sabotage: bool = False
    can_kill: bool = False
    vision_modifier: float = 1.0
    speed_modifier: float = 1.0
    special_mechanics: List[str] = None

    def __post_init__(self):
        if self.special_mechanics is None:
            self.special_mechanics = []

# Define all abilities
ABILITIES = {
    # Impostor abilities
    "kill": RoleAbility("Kill", "Eliminate a crewmate", cooldown=45.0, range=1.0, requires_target=True),
    "sabotage": RoleAbility("Sabotage", "Sabotage ship systems", cooldown=30.0),
    "vent": RoleAbility("Vent", "Travel through vents", cooldown=0.0),
    
    # Shapeshifter abilities
    "shapeshift": RoleAbility("Shapeshift", "Take another player's appearance", cooldown=30.0, uses=None, requires_target=True),
    
    # Engineer abilities
    "engineer_vent": RoleAbility("Vent", "Use vents like impostors", cooldown=0.0, can_use_in_vents=True),
    
    # Scientist abilities
    "check_vitals": RoleAbility("Check Vitals", "See who is alive remotely", cooldown=0.0),
    
    # Guardian Angel abilities
    "protect": RoleAbility("Protect", "Shield a player from attacks", cooldown=35.0, uses=None, requires_target=True),
    
    # Sheriff abilities
    "sheriff_kill": RoleAbility("Kill", "Execute suspected impostors", cooldown=30.0, requires_target=True),
    
    # Tracker abilities
    "track": RoleAbility("Track", "See a player's location", cooldown=25.0, requires_target=True),
    
    # Noisemaker abilities
    "alert": RoleAbility("Alert", "Make noise when killed", cooldown=0.0),
    
    # Phantom abilities
    "phantom_vanish": RoleAbility("Vanish", "Become invisible after killing", cooldown=0.0),
    
    # And many more...
}

# Define all roles
ALL_ROLES = {
    # === CREWMATE ROLES ===
    "Crewmate": Role(
        name="Crewmate",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.ALWAYS,
        description="Complete tasks and find the impostors",
        abilities=[],
        passive_abilities=["Complete tasks", "Call emergency meetings", "Vote"],
        win_condition="Complete all tasks or eliminate all impostors",
        vision_modifier=1.0
    ),
    
    "Engineer": Role(
        name="Engineer",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.COMMON,
        description="Can use vents like impostors",
        abilities=[ABILITIES["engineer_vent"]],
        passive_abilities=["Complete tasks", "Use vents"],
        win_condition="Complete all tasks or eliminate all impostors",
        can_vent=True,
        special_mechanics=["Limited vent time", "Vent cooldown"]
    ),
    
    "Scientist": Role(
        name="Scientist",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.COMMON,
        description="Can check vitals from anywhere",
        abilities=[ABILITIES["check_vitals"]],
        passive_abilities=["Complete tasks", "Remote vitals access"],
        win_condition="Complete all tasks or eliminate all impostors",
        special_mechanics=["Battery system for vitals"]
    ),
    
    "Guardian Angel": Role(
        name="Guardian Angel",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.COMMON,
        description="Protect other players from attacks",
        abilities=[ABILITIES["protect"]],
        passive_abilities=["Complete tasks", "Protect players"],
        win_condition="Complete all tasks or eliminate all impostors",
        special_mechanics=["Only appears when dead", "Limited protection uses"]
    ),
    
    "Sheriff": Role(
        name="Sheriff",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.UNCOMMON,
        description="Can kill suspected impostors",
        abilities=[ABILITIES["sheriff_kill"]],
        passive_abilities=["Complete tasks", "Execute impostors"],
        win_condition="Complete all tasks or eliminate all impostors",
        can_kill=True,
        special_mechanics=["Dies if kills innocent", "Limited kills"]
    ),
    
    "Tracker": Role(
        name="Tracker",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.UNCOMMON,
        description="Track other players' locations",
        abilities=[ABILITIES["track"]],
        passive_abilities=["Complete tasks", "Track players"],
        win_condition="Complete all tasks or eliminate all impostors",
        special_mechanics=["Arrow pointing to tracked player"]
    ),
    
    "Noisemaker": Role(
        name="Noisemaker",
        role_type=RoleType.CREWMATE,
        rarity=RoleRarity.UNCOMMON,
        description="Makes noise when killed",
        abilities=[ABILITIES["alert"]],
        passive_abilities=["Complete tasks", "Alert on death"],
        win_condition="Complete all tasks or eliminate all impostors",
        special_mechanics=["Sound alert when killed"]
    ),
    
    # === IMPOSTOR ROLES ===
    "Impostor": Role(
        name="Impostor",
        role_type=RoleType.IMPOSTOR,
        rarity=RoleRarity.ALWAYS,
        description="Eliminate crewmates and sabotage the ship",
        abilities=[ABILITIES["kill"], ABILITIES["sabotage"], ABILITIES["vent"]],
        passive_abilities=["Kill", "Sabotage", "Vent", "Fake tasks"],
        win_condition="Eliminate enough crewmates or sabotage victory",
        can_vent=True,
        can_sabotage=True,
        can_kill=True,
        vision_modifier=1.5
    ),
    
    "Shapeshifter": Role(
        name="Shapeshifter",
        role_type=RoleType.IMPOSTOR,
        rarity=RoleRarity.COMMON,
        description="Can disguise as other players",
        abilities=[ABILITIES["kill"], ABILITIES["sabotage"], ABILITIES["vent"], ABILITIES["shapeshift"]],
        passive_abilities=["Kill", "Sabotage", "Vent", "Shapeshift"],
        win_condition="Eliminate enough crewmates or sabotage victory",
        can_vent=True,
        can_sabotage=True,
        can_kill=True,
        vision_modifier=1.5,
        special_mechanics=["Leave evidence when shifting", "Shift duration limit"]
    ),
    
    "Phantom": Role(
        name="Phantom",
        role_type=RoleType.IMPOSTOR,
        rarity=RoleRarity.UNCOMMON,
        description="Becomes invisible after killing",
        abilities=[ABILITIES["kill"], ABILITIES["sabotage"], ABILITIES["vent"], ABILITIES["phantom_vanish"]],
        passive_abilities=["Kill", "Sabotage", "Vent", "Vanish"],
        win_condition="Eliminate enough crewmates or sabotage victory",
        can_vent=True,
        can_sabotage=True,
        can_kill=True,
        vision_modifier=1.5,
        special_mechanics=["Invisible after kills", "Appears on security briefly"]
    ),
    
    # === NEUTRAL ROLES (Modded/Community) ===
    "Jester": Role(
        name="Jester",
        role_type=RoleType.NEUTRAL,
        rarity=RoleRarity.MODDED,
        description="Win by getting voted out",
        abilities=[],
        passive_abilities=["Fake tasks", "Act suspicious"],
        win_condition="Get voted out during a meeting",
        special_mechanics=["Wins if ejected", "Cannot be killed"]
    ),
    
    "Survivor": Role(
        name="Survivor",
        role_type=RoleType.NEUTRAL,
        rarity=RoleRarity.MODDED,
        description="Survive until the end",
        abilities=[],
        passive_abilities=["Complete tasks", "Survive"],
        win_condition="Survive until game ends",
        special_mechanics=["Wins with any faction"]
    ),
}

def get_roles_by_type(role_type: RoleType) -> List[Role]:
    """Get all roles of a specific type"""
    return [role for role in ALL_ROLES.values() if role.role_type == role_type]

def get_roles_by_rarity(rarity: RoleRarity) -> List[Role]:
    """Get all roles of a specific rarity"""
    return [role for role in ALL_ROLES.values() if role.rarity == rarity]

def get_available_roles(include_modded: bool = False) -> List[Role]:
    """Get roles available for gameplay"""
    if include_modded:
        return list(ALL_ROLES.values())
    else:
        return [role for role in ALL_ROLES.values() if role.rarity != RoleRarity.MODDED]

def create_role_distribution(num_players: int, num_impostors: int, 
                           enable_special_roles: bool = True,
                           role_probabilities: Optional[Dict[str, float]] = None) -> List[Role]:
    """Create a balanced role distribution for a game"""
    
    if role_probabilities is None:
        role_probabilities = {
            "Engineer": 0.3,
            "Scientist": 0.3,
            "Guardian Angel": 0.2,
            "Sheriff": 0.1,
            "Tracker": 0.15,
            "Noisemaker": 0.1,
            "Shapeshifter": 0.4,
            "Phantom": 0.2,
        }
    
    roles = []
    
    # Assign impostor roles
    impostor_roles = ["Impostor", "Shapeshifter", "Phantom"]
    for i in range(num_impostors):
        if enable_special_roles and random.random() < 0.5:  # 50% chance for special impostor
            role_name = random.choices(
                ["Shapeshifter", "Phantom", "Impostor"],
                weights=[0.3, 0.2, 0.5]
            )[0]
        else:
            role_name = "Impostor"
        
        roles.append(ALL_ROLES[role_name])
    
    # Assign crewmate roles
    num_crewmates = num_players - num_impostors
    crewmate_roles = []
    
    for i in range(num_crewmates):
        if enable_special_roles and random.random() < 0.6:  # 60% chance for special crewmate
            # Choose special crewmate role based on probabilities
            special_roles = ["Engineer", "Scientist", "Guardian Angel", "Sheriff", "Tracker", "Noisemaker"]
            weights = [role_probabilities.get(role, 0.1) for role in special_roles]
            
            role_name = random.choices(special_roles, weights=weights)[0]
        else:
            role_name = "Crewmate"
        
        crewmate_roles.append(ALL_ROLES[role_name])
    
    roles.extend(crewmate_roles)
    
    # Shuffle to randomize assignment
    random.shuffle(roles)
    
    return roles

def validate_role_distribution(roles: List[Role]) -> Tuple[bool, str]:
    """Validate that role distribution is balanced"""
    
    impostor_count = sum(1 for role in roles if role.role_type == RoleType.IMPOSTOR)
    crewmate_count = sum(1 for role in roles if role.role_type == RoleType.CREWMATE)
    neutral_count = sum(1 for role in roles if role.role_type == RoleType.NEUTRAL)
    
    total_players = len(roles)
    
    # Check minimum players
    if total_players < 4:
        return False, "Minimum 4 players required"
    
    # Check impostor ratio
    if impostor_count == 0:
        return False, "At least 1 impostor required"
    
    if impostor_count >= crewmate_count:
        return False, "Impostors cannot equal or exceed crewmates"
    
    # Check for conflicting roles
    role_names = [role.name for role in roles]
    
    # Sheriff and Guardian Angel shouldn't both be present (balance)
    if "Sheriff" in role_names and role_names.count("Sheriff") > 1:
        return False, "Only one Sheriff allowed per game"
    
    # Check for too many special roles
    special_crewmate_roles = [r for r in roles if r.role_type == RoleType.CREWMATE and r.name != "Crewmate"]
    if len(special_crewmate_roles) > crewmate_count * 0.7:  # Max 70% special roles
        return False, "Too many special crewmate roles"
    
    return True, "Role distribution valid"

def get_role_info(role_name: str) -> Optional[Role]:
    """Get detailed information about a role"""
    return ALL_ROLES.get(role_name)

def get_role_abilities(role_name: str) -> List[RoleAbility]:
    """Get all abilities for a role"""
    role = ALL_ROLES.get(role_name)
    return role.abilities if role else []

def can_role_perform_action(role_name: str, action: str) -> bool:
    """Check if a role can perform a specific action"""
    role = ALL_ROLES.get(role_name)
    if not role:
        return False
    
    # Check direct abilities
    if action == "kill" and role.can_kill:
        return True
    if action == "vent" and role.can_vent:
        return True
    if action == "sabotage" and role.can_sabotage:
        return True
    
    # Check specific abilities
    ability_names = [ability.name.lower() for ability in role.abilities]
    return action.lower() in ability_names

# Export commonly used functions
__all__ = [
    'Role', 'RoleAbility', 'RoleType', 'RoleRarity',
    'ALL_ROLES', 'ABILITIES',
    'get_roles_by_type', 'get_roles_by_rarity', 'get_available_roles',
    'create_role_distribution', 'validate_role_distribution',
    'get_role_info', 'get_role_abilities', 'can_role_perform_action'
]
