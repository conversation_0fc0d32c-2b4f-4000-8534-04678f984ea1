import requests
import json
from typing import List, Dict, Optional
from core.communication import Message, MessageType, CommunicationStrategy

class OllamaClient:
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "deepseek-r1:latest"):
        self.base_url = base_url
        self.model = model
        
    def generate(self, prompt: str, max_tokens: int = 100) -> str:
        """Generate text using Ollama"""
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "num_predict": max_tokens,
                        "temperature": 0.7,
                        "top_p": 0.8,
                        "repeat_penalty": 1.1,
                        "stop": ["\n", "</think>", ".", "!", "?", "Message:", "Response:"]
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                print(f"Ollama API error: {response.status_code}")
                return ""
                
        except requests.exceptions.RequestException as e:
            print(f"Failed to connect to Ollama: {e}")
            return ""
        except Exception as e:
            print(f"Ollama generation error: {e}")
            return ""

class LLMCommunicationStrategy(CommunicationStrategy):
    """LLM-powered communication strategy"""
    
    def __init__(self, player_color: str, role_name: str, ollama_client: OllamaClient):
        super().__init__(player_color, role_name)
        self.ollama = ollama_client
        self.conversation_history = []
        
    def generate_messages(self, game_state: Dict, discussion_context: Dict) -> List[Message]:
        """Generate messages using LLM"""
        messages = []
        
        # Build context for the LLM
        context = self._build_context(game_state, discussion_context)
        
        # Generate initial statement
        initial_prompt = self._create_initial_prompt(context)
        response = self.ollama.generate(initial_prompt, max_tokens=80)
        
        if response:
            message = self._parse_llm_response(response)
            if message:
                messages.append(message)
                self.conversation_history.append(f"{self.player_color}: {response}")
        
        # If no LLM message generated, use fallback
        if not messages:
            fallback_msg = self._get_fallback_message(context)
            if fallback_msg:
                messages.append(fallback_msg)

        return messages
    
    def respond_to_message(self, message: Message, game_state: Dict) -> Optional[Message]:
        """Generate response to a message using LLM"""
        
        # Only respond to messages directed at us or accusations
        should_respond = (
            message.target == self.player_color or
            message.message_type == MessageType.ACCUSATION and message.target == self.player_color or
            message.message_type == MessageType.QUESTION and message.target == self.player_color
        )
        
        if not should_respond:
            return None
            
        context = self._build_context(game_state, {})
        response_prompt = self._create_response_prompt(message, context)
        response = self.ollama.generate(response_prompt, max_tokens=60)
        
        if response:
            parsed_message = self._parse_llm_response(response)
            if parsed_message:
                self.conversation_history.append(f"{self.player_color}: {response}")
                return parsed_message
                
        return None
    
    def _build_context(self, game_state: Dict, discussion_context: Dict) -> Dict:
        """Build context information for LLM prompts"""
        return {
            'role': self.role_name,
            'color': self.player_color,
            'living_players': game_state.get('living_players', []),
            'dead_players': game_state.get('dead_players', []),
            'recent_deaths': [p for p in game_state.get('dead_players', []) 
                            if p not in self.memory.get('known_deaths', [])],
            'accusations': discussion_context.get('accusations', []),
            'alibis': discussion_context.get('alibis', []),
            'conversation_history': self.conversation_history[-5:],  # Last 5 messages
        }
    
    def _create_initial_prompt(self, context: Dict) -> str:
        """Create prompt for initial discussion message"""

        recent_deaths = context.get('recent_deaths', [])
        living_players = [p for p in context['living_players'] if p != self.player_color]

        # Get common Among Us locations and tasks
        locations = ["admin", "cafeteria", "electrical", "medbay", "weapons", "navigation", "storage", "security"]
        tasks = ["card swipe", "wires", "asteroids", "scan", "course", "upload", "download", "reactor"]

        prompt = f"""You are {context['color']} in Among Us. Generate a short chat message (max 50 chars).

Recent deaths: {', '.join(recent_deaths) if recent_deaths else 'none'}
Other players: {', '.join(living_players)}

Examples of good messages:
- "i was doing wires in electrical"
- "red sus"
- "where was everyone?"
- "doing scan in medbay"

Generate only the message (no quotes, no explanation):"""

        return prompt
    
    def _create_response_prompt(self, message: Message, context: Dict) -> str:
        """Create prompt for responding to a specific message"""

        prompt = f"""You are {context['color']} in Among Us. {message.sender} said: "{message.content}"

Respond with a short message (max 50 chars):

Examples:
- "i was in medbay doing scan"
- "no i was doing wires"
- "where were you?"
- "that's sus"

Generate only the response:"""

        return prompt
    
    def _get_role_instruction(self) -> str:
        """Get role-specific instructions for the LLM"""
        if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
            return """IMPOSTOR: Lie about tasks/location. Blame others. Act innocent."""
        else:
            return """CREWMATE: Tell truth about tasks. Ask questions. Find impostors."""
    
    def _parse_llm_response(self, response: str) -> Optional[Message]:
        """Parse LLM response into a Message object"""
        if not response or len(response.strip()) == 0:
            return None

        # Clean up the response
        response = response.strip().lower()

        # Remove DeepSeek-R1 thinking tags and artifacts
        response = response.replace('<think>', '').replace('</think>', '')
        response = response.replace('<thinking>', '').replace('</thinking>', '')
        response = response.replace('"', '').replace("'", "")
        response = response.replace('response:', '').replace('message:', '').strip()

        if response.startswith(f"{self.player_color.lower()}:"):
            response = response[len(f"{self.player_color.lower()}:"):].strip()

        # If response is empty or just thinking tags, use fallback
        if not response or response in ['<think>', '</think>', '<thinking>', '</thinking>']:
            return None

        # Enforce strict character limit (Among Us chat limit is 100, we use 50 for safety)
        response = response[:50]

        # Validate it's not too verbose (max 8 words)
        if len(response.split()) > 8:
            # Fallback to simple responses
            fallbacks = ["i was doing tasks", "not me", "where were you?", "sus", "idk"]
            response = fallbacks[hash(response) % len(fallbacks)]

        # Determine message type based on content
        message_type = self._classify_message_type(response)
        target = self._extract_target(response)

        return Message(
            sender=self.player_color,
            message_type=message_type,
            content=response,
            target=target,
            confidence=0.8
        )
    
    def _classify_message_type(self, content: str) -> MessageType:
        """Classify the message type based on content"""
        content_lower = content.lower()
        
        # Check for accusations
        if any(word in content_lower for word in ["sus", "suspicious", "impostor", "imposter", "killed", "murder"]):
            return MessageType.ACCUSATION
        
        # Check for questions
        if "?" in content or any(word in content_lower for word in ["where", "what", "who", "when", "how"]):
            return MessageType.QUESTION
        
        # Check for alibis/information
        if any(word in content_lower for word in ["was", "doing", "task", "in", "at"]):
            return MessageType.ALIBI
        
        # Check for defense
        if any(word in content_lower for word in ["not me", "wasn't me", "innocent", "didn't"]):
            return MessageType.DEFENSE
        
        # Check for agreement/disagreement
        if any(word in content_lower for word in ["agree", "yes", "right", "exactly"]):
            return MessageType.AGREEMENT
        elif any(word in content_lower for word in ["disagree", "no", "wrong", "not true"]):
            return MessageType.DISAGREEMENT
        
        # Default to information
        return MessageType.INFORMATION
    
    def _extract_target(self, content: str) -> Optional[str]:
        """Extract target player from message content"""
        # Simple extraction - look for color names
        colors = ["Red", "Blue", "Green", "Yellow", "Pink", "Cyan", "Orange", "Purple", 
                 "Black", "White", "Brown", "Lime"]
        
        for color in colors:
            if color.lower() in content.lower() and color != self.player_color:
                return color
                
        return None

    def _get_fallback_message(self, context: Dict) -> Optional[Message]:
        """Generate fallback message when LLM fails"""
        import random

        # Among Us appropriate fallback messages
        if self.role_name in ["Impostor", "Shapeshifter", "Phantom"]:
            # Impostor fallbacks - deflect and lie
            fallbacks = [
                "i was doing wires in electrical",
                "saw someone vent but not sure who",
                "was in admin doing card swipe",
                "doing tasks in medbay",
                "where was everyone?",
                "not me i was doing scan"
            ]
        else:
            # Crewmate fallbacks - be helpful
            fallbacks = [
                "i was doing tasks in admin",
                "where was everyone?",
                "who was near the body?",
                "doing wires in electrical",
                "was in medbay doing scan",
                "anyone see anything sus?"
            ]

        message_content = random.choice(fallbacks)

        return Message(
            sender=self.player_color,
            message_type=MessageType.ALIBI,
            content=message_content,
            target=None,
            confidence=0.5
        )

def create_llm_communication_strategy(player_color: str, role_name: str,
                                    ollama_client: OllamaClient) -> LLMCommunicationStrategy:
    """Factory function to create LLM communication strategy"""
    return LLMCommunicationStrategy(player_color, role_name, ollama_client)
