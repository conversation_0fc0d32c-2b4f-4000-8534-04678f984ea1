#!/usr/bin/env python3
"""
Test the ultimate Among Us AI training system
"""

import torch
from agents.ultimate_rl_agent import UltimateAmongUsAgent
from training.curriculum_learning import AdaptiveCurriculumLearning
from analytics.training_analytics import AmongUsTrainingAnalytics

def test_ultimate_agent():
    """Test the ultimate RL agent"""
    print("🧪 Testing Ultimate RL Agent...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    agent = UltimateAmongUsAgent(
        player_color="Red",
        role_name="Crewmate",
        device=device
    )
    
    # Test state encoding
    mock_observation = {
        "tick": 50,
        "players_in_room": ["Red", "Blue"],
        "connected_rooms": ["Cafeteria", "Admin"],
        "dead_bodies": [],
        "task_progress": 0.3,
        "completed_tasks": 2,
        "total_tasks": 8,
        "living_players": 6,
        "living_impostors": 2,
        "can_kill": False,
        "can_vent": False,
        "can_sabotage": False,
        "active_sabotages": [],
        "in_meeting": False
    }
    
    # Test action selection
    action = agent.choose_action(mock_observation)
    print(f"   ✅ Agent chose action: {action.action_type}")
    
    # Test learning
    agent.learn_from_experience(1.5, mock_observation, False)
    print(f"   ✅ Learning step completed")
    
    # Test performance stats
    stats = agent.get_performance_stats()
    print(f"   ✅ Performance stats: {len(stats)} metrics")
    
    print("   ✅ Ultimate RL Agent test passed!")

def test_curriculum_learning():
    """Test curriculum learning system"""
    print("\n🧪 Testing Curriculum Learning...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    agent = UltimateAmongUsAgent("Red", "Crewmate", device=device)
    curriculum = AdaptiveCurriculumLearning(agent, device)
    
    # Test curriculum configuration
    config = curriculum.get_current_config()
    print(f"   ✅ Current stage: {config.stage.value}")
    print(f"   ✅ Description: {config.description}")
    
    # Test game creation
    game = curriculum.create_training_game(config)
    print(f"   ✅ Training game created with {len(game.players)} players")
    
    # Test episode recording
    curriculum.record_episode_result(True, {"tasks_completed": 3})
    print(f"   ✅ Episode result recorded")
    
    # Test curriculum status
    status = curriculum.get_curriculum_status()
    print(f"   ✅ Curriculum status: {len(status)} fields")
    
    print("   ✅ Curriculum Learning test passed!")

def test_analytics():
    """Test analytics system"""
    print("\n🧪 Testing Analytics System...")
    
    analytics = AmongUsTrainingAnalytics("test_analytics")
    
    # Test episode recording
    episode_data = {
        "role": "Crewmate",
        "won": True,
        "reward": 15.5,
        "actions_taken": ["move", "task", "task", "vote"],
        "performance_score": 1.2
    }
    
    analytics.record_episode(episode_data)
    print(f"   ✅ Episode data recorded")
    
    # Test training step recording
    analytics.record_training_step({"game": 0.5, "social": 0.3, "comm": 0.2})
    print(f"   ✅ Training step recorded")
    
    # Test report generation
    report = analytics.generate_comprehensive_report()
    print(f"   ✅ Report generated with {len(report)} sections")
    
    print("   ✅ Analytics System test passed!")

def test_integration():
    """Test system integration"""
    print("\n🧪 Testing System Integration...")
    
    # Test that all components work together
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Create agent
    agent = UltimateAmongUsAgent("Red", "Crewmate", device=device)
    
    # Create curriculum
    curriculum = AdaptiveCurriculumLearning(agent, device)
    
    # Create analytics
    analytics = AmongUsTrainingAnalytics("integration_test")
    
    # Simulate a mini training loop
    config = curriculum.get_current_config()
    
    for episode in range(3):
        # Create game
        game = curriculum.create_training_game(config)
        
        # Simulate episode data
        episode_data = {
            "role": "Crewmate",
            "won": episode % 2 == 0,  # Win every other episode
            "reward": 10.0 + episode * 2,
            "actions_taken": ["move", "task"] * (episode + 1),
            "performance_score": 0.8 + episode * 0.1,
            "curriculum_stage": config.stage.value
        }
        
        # Record in analytics
        analytics.record_episode(episode_data)
        
        # Record in curriculum
        curriculum.record_episode_result(
            episode_data["won"], 
            {"performance_score": episode_data["performance_score"]}
        )
        
        print(f"   ✅ Episode {episode + 1} simulated")
    
    # Test curriculum status
    status = curriculum.get_curriculum_status()
    print(f"   ✅ Curriculum episodes: {status['episodes_completed']}")
    
    # Test analytics report
    report = analytics.generate_comprehensive_report()
    print(f"   ✅ Analytics report sections: {len(report)}")
    
    print("   ✅ System Integration test passed!")

def main():
    print("🚀 Testing Ultimate Among Us AI Training System")
    print("=" * 60)
    
    try:
        test_ultimate_agent()
        test_curriculum_learning()
        test_analytics()
        test_integration()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! Ultimate training system is ready!")
        print("✅ Ultimate RL Agent working")
        print("✅ Curriculum Learning working")
        print("✅ Analytics System working")
        print("✅ System Integration working")
        print("\n🚀 Ready to train the ultimate Among Us AI!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
