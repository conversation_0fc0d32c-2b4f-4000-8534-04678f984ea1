"""
Computer Vision Training Mode for Among Us.

Provides a training mode that uses computer vision to train AI agents
on the actual Among Us game with proper error handling and safety measures.
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import os
from datetime import datetime

from .cv_agent_bridge import CVAgentBridge
from .player_detector import PlayerColor
from ..agents.agents.ultimate_rl_agent import UltimateAmongUsAgent
from ..analytics.analytics.training_analytics import AmongUsTrainingAnalytics

logger = logging.getLogger(__name__)


class CVTrainingMode(Enum):
    """Computer vision training modes."""
    OBSERVATION_ONLY = "observation_only"  # Just observe, don't act
    ASSISTED_PLAY = "assisted_play"        # Human plays, AI observes and learns
    FULL_AUTOMATION = "full_automation"    # AI plays completely
    HYBRID = "hybrid"                      # Mix of human and AI control


@dataclass
class CVTrainingConfig:
    """Configuration for computer vision training."""
    mode: CVTrainingMode = CVTrainingMode.OBSERVATION_ONLY
    player_color: PlayerColor = PlayerColor.RED
    max_games: int = 10
    max_duration: int = 3600  # 1 hour max
    safety_enabled: bool = True
    auto_restart: bool = False
    save_observations: bool = True
    observation_save_path: str = "data/cv_observations"
    
    # Performance settings
    target_fps: int = 5
    action_delay: float = 1.0
    
    # Safety settings
    emergency_stop_on_error: bool = True
    max_consecutive_errors: int = 5
    
    # Learning settings
    update_frequency: int = 10  # Update agent every N observations
    save_frequency: int = 100   # Save progress every N observations


class CVTrainer:
    """Computer Vision trainer for Among Us AI."""
    
    def __init__(self, config: CVTrainingConfig):
        """
        Initialize CV trainer.
        
        Args:
            config: Training configuration
        """
        self.config = config
        self.is_training = False
        self.should_stop = False
        
        # Initialize agent
        self.agent = UltimateAmongUsAgent(
            player_color=config.player_color.value,
            role_name="Unknown",  # Will be determined during gameplay
            device="cpu"  # Use CPU for real-time inference
        )
        
        # Initialize CV bridge
        self.cv_bridge = CVAgentBridge(self.agent, config.player_color)
        
        # Initialize analytics
        self.analytics = AmongUsTrainingAnalytics()
        
        # Training state
        self.current_game = 0
        self.total_observations = 0
        self.consecutive_errors = 0
        self.start_time = None
        
        # Observation storage
        self.observations = []
        self.game_logs = []
        
        # Performance tracking
        self.training_stats = {
            'games_completed': 0,
            'total_actions': 0,
            'successful_actions': 0,
            'avg_game_duration': 0.0,
            'cv_performance': {},
            'agent_performance': {}
        }
        
        # Create save directory
        os.makedirs(config.observation_save_path, exist_ok=True)
    
    def start_training(self) -> bool:
        """Start computer vision training."""
        try:
            logger.info(f"Starting CV training in {self.config.mode.value} mode")
            
            # Initialize CV system
            if not self.cv_bridge.initialize():
                logger.error("Failed to initialize CV bridge")
                return False
            
            self.is_training = True
            self.should_stop = False
            self.start_time = time.time()
            
            # Start training loop
            if self.config.mode == CVTrainingMode.OBSERVATION_ONLY:
                return self._run_observation_mode()
            elif self.config.mode == CVTrainingMode.FULL_AUTOMATION:
                return self._run_automation_mode()
            elif self.config.mode == CVTrainingMode.ASSISTED_PLAY:
                return self._run_assisted_mode()
            elif self.config.mode == CVTrainingMode.HYBRID:
                return self._run_hybrid_mode()
            else:
                logger.error(f"Unknown training mode: {self.config.mode}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting CV training: {e}")
            return False
        finally:
            self.cleanup()
    
    def stop_training(self):
        """Stop training gracefully."""
        logger.info("Stopping CV training...")
        self.should_stop = True
        self.is_training = False
    
    def _run_observation_mode(self) -> bool:
        """Run in observation-only mode."""
        try:
            logger.info("Running in observation-only mode")
            
            while (not self.should_stop and 
                   self.current_game < self.config.max_games and
                   self._check_time_limit()):
                
                # Get observation
                cv_obs = self.cv_bridge.get_observation()
                if cv_obs:
                    self._process_observation(cv_obs)
                
                # Wait for next frame
                time.sleep(1.0 / self.config.target_fps)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in observation mode: {e}")
            return False
    
    def _run_automation_mode(self) -> bool:
        """Run in full automation mode."""
        try:
            logger.info("Running in full automation mode")
            
            while (not self.should_stop and 
                   self.current_game < self.config.max_games and
                   self._check_time_limit()):
                
                # Wait for game to start
                if not self.cv_bridge.wait_for_game_start(timeout=30):
                    logger.warning("Game did not start within timeout")
                    continue
                
                # Run game
                game_success = self._run_automated_game()
                
                if game_success:
                    self.current_game += 1
                    self.training_stats['games_completed'] += 1
                else:
                    self.consecutive_errors += 1
                    if (self.config.emergency_stop_on_error and 
                        self.consecutive_errors >= self.config.max_consecutive_errors):
                        logger.error("Too many consecutive errors, stopping training")
                        break
                
                # Wait between games
                time.sleep(5.0)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in automation mode: {e}")
            return False
    
    def _run_assisted_mode(self) -> bool:
        """Run in assisted play mode."""
        try:
            logger.info("Running in assisted play mode")
            logger.info("Human should play the game while AI observes and learns")
            
            while (not self.should_stop and 
                   self.current_game < self.config.max_games and
                   self._check_time_limit()):
                
                # Get observation and learn from human actions
                cv_obs = self.cv_bridge.get_observation()
                if cv_obs:
                    self._process_observation(cv_obs)
                    
                    # Try to learn from human behavior
                    self._learn_from_human_action(cv_obs)
                
                time.sleep(1.0 / self.config.target_fps)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in assisted mode: {e}")
            return False
    
    def _run_hybrid_mode(self) -> bool:
        """Run in hybrid mode."""
        try:
            logger.info("Running in hybrid mode")
            # Implementation would alternate between human and AI control
            # For now, just run observation mode
            return self._run_observation_mode()
            
        except Exception as e:
            logger.error(f"Error in hybrid mode: {e}")
            return False
    
    def _run_automated_game(self) -> bool:
        """Run one automated game."""
        try:
            game_start_time = time.time()
            actions_this_game = 0
            
            while (self.cv_bridge.is_game_active() and 
                   not self.should_stop and
                   time.time() - game_start_time < 600):  # 10 min max per game
                
                # Run one training step
                success = self.cv_bridge.run_training_step()
                
                if success:
                    actions_this_game += 1
                    self.training_stats['successful_actions'] += 1
                    self.consecutive_errors = 0
                else:
                    self.consecutive_errors += 1
                
                self.training_stats['total_actions'] += 1
                
                # Wait before next action
                time.sleep(self.config.action_delay)
            
            # Update game duration stats
            game_duration = time.time() - game_start_time
            games_completed = self.training_stats['games_completed']
            if games_completed > 0:
                self.training_stats['avg_game_duration'] = (
                    (self.training_stats['avg_game_duration'] * games_completed + game_duration) / 
                    (games_completed + 1)
                )
            else:
                self.training_stats['avg_game_duration'] = game_duration
            
            logger.info(f"Game completed in {game_duration:.1f}s with {actions_this_game} actions")
            return True
            
        except Exception as e:
            logger.error(f"Error running automated game: {e}")
            return False
    
    def _process_observation(self, cv_obs):
        """Process a computer vision observation."""
        try:
            self.total_observations += 1
            
            # Store observation if enabled
            if self.config.save_observations:
                self.observations.append({
                    'timestamp': cv_obs.timestamp,
                    'game_state': cv_obs.game_state.value,
                    'player_count': len(cv_obs.players),
                    'own_position': cv_obs.own_position,
                    'ui_elements': len(cv_obs.ui_elements)
                })
            
            # Update agent if needed
            if self.total_observations % self.config.update_frequency == 0:
                self._update_agent_from_observations()
            
            # Save progress if needed
            if self.total_observations % self.config.save_frequency == 0:
                self._save_progress()
            
        except Exception as e:
            logger.error(f"Error processing observation: {e}")
    
    def _learn_from_human_action(self, cv_obs):
        """Learn from human actions in assisted mode."""
        try:
            # This would analyze what the human did and update the agent
            # For now, just log the observation
            pass
            
        except Exception as e:
            logger.error(f"Error learning from human action: {e}")
    
    def _update_agent_from_observations(self):
        """Update agent based on collected observations."""
        try:
            # This would implement learning updates
            # For now, just log
            logger.debug(f"Updating agent after {self.total_observations} observations")
            
        except Exception as e:
            logger.error(f"Error updating agent: {e}")
    
    def _save_progress(self):
        """Save training progress."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save observations
            if self.observations:
                obs_file = os.path.join(
                    self.config.observation_save_path, 
                    f"observations_{timestamp}.json"
                )
                with open(obs_file, 'w') as f:
                    json.dump(self.observations, f, indent=2)
                
                logger.info(f"Saved {len(self.observations)} observations to {obs_file}")
            
            # Save training stats
            stats_file = os.path.join(
                self.config.observation_save_path,
                f"training_stats_{timestamp}.json"
            )
            
            # Get performance stats from CV bridge
            cv_stats = self.cv_bridge.get_performance_stats()
            self.training_stats['cv_performance'] = cv_stats
            
            with open(stats_file, 'w') as f:
                json.dump(self.training_stats, f, indent=2)
            
            logger.info(f"Saved training stats to {stats_file}")
            
        except Exception as e:
            logger.error(f"Error saving progress: {e}")
    
    def _check_time_limit(self) -> bool:
        """Check if training time limit has been reached."""
        if not self.start_time:
            return True
        
        elapsed = time.time() - self.start_time
        return elapsed < self.config.max_duration
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get current training statistics."""
        stats = self.training_stats.copy()
        
        if self.start_time:
            stats['elapsed_time'] = time.time() - self.start_time
        
        stats['total_observations'] = self.total_observations
        stats['current_game'] = self.current_game
        stats['consecutive_errors'] = self.consecutive_errors
        
        # Add CV bridge stats
        if hasattr(self, 'cv_bridge'):
            stats['cv_performance'] = self.cv_bridge.get_performance_stats()
        
        return stats
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.is_training = False
            
            # Save final progress
            if self.observations or self.training_stats['total_actions'] > 0:
                self._save_progress()
            
            # Cleanup CV bridge
            if hasattr(self, 'cv_bridge'):
                self.cv_bridge.cleanup()
            
            logger.info("CV trainer cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
