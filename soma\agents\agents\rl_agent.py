import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import deque
import random

from agents.base_agent import BaseAgent
from core.action import Action, VALID_ACTIONS
from core.communication import Message, MessageType

class AmongUsNet(nn.Module):
    """Neural network for Among Us RL agent"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 512):
        super(AmongUsNet, self).__init__()
        
        # Game state processing
        self.game_encoder = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Communication history processing (simplified for now)
        self.comm_encoder = nn.Sequential(
            nn.Linear(100, hidden_dim // 4),  # Simplified communication encoding
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim // 4),
            nn.ReLU(),
        )
        
        # Combined processing
        combined_dim = hidden_dim // 2 + hidden_dim // 4
        self.combined_layers = nn.Sequential(
            nn.Linear(combined_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # Action value head
        self.action_head = nn.Linear(hidden_dim // 2, action_dim)
        
        # State value head (for advantage calculation)
        self.value_head = nn.Linear(hidden_dim // 2, 1)
        
    def forward(self, game_state: torch.Tensor, comm_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # Process game state
        game_features = self.game_encoder(game_state)
        
        # Process communication state
        comm_features = self.comm_encoder(comm_state)
        
        # Combine features
        combined = torch.cat([game_features, comm_features], dim=-1)
        combined_features = self.combined_layers(combined)
        
        # Get action values and state value
        action_values = self.action_head(combined_features)
        state_value = self.value_head(combined_features)
        
        return action_values, state_value

class ReplayBuffer:
    """Experience replay buffer for RL training"""
    
    def __init__(self, capacity: int = 10000):
        self.buffer = deque(maxlen=capacity)
        
    def push(self, state: Dict, action: int, reward: float, next_state: Dict, done: bool):
        self.buffer.append((state, action, reward, next_state, done))
        
    def sample(self, batch_size: int) -> List[Tuple]:
        return random.sample(self.buffer, batch_size)
    
    def __len__(self) -> int:
        return len(self.buffer)

class RLAgent(BaseAgent):
    """Reinforcement Learning agent for Among Us"""
    
    def __init__(self, player_color: str, role_name: str, 
                 learning_rate: float = 1e-4, epsilon: float = 0.1,
                 device: str = "cpu"):
        super().__init__(player_color)
        self.role_name = role_name
        self.device = torch.device(device)
        self.epsilon = epsilon
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01
        
        # Action mapping
        self.action_list = list(VALID_ACTIONS)
        self.action_to_idx = {action: idx for idx, action in enumerate(self.action_list)}
        self.idx_to_action = {idx: action for action, idx in self.action_to_idx.items()}
        
        # Neural networks
        self.state_dim = 50  # Will be adjusted based on actual state encoding
        self.action_dim = len(self.action_list)
        
        self.q_network = AmongUsNet(self.state_dim, self.action_dim).to(self.device)
        self.target_network = AmongUsNet(self.state_dim, self.action_dim).to(self.device)
        self.optimizer = torch.optim.Adam(self.q_network.parameters(), lr=learning_rate)
        
        # Copy weights to target network
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Experience replay
        self.replay_buffer = ReplayBuffer(capacity=10000)
        self.batch_size = 32
        self.gamma = 0.99  # Discount factor
        self.target_update_freq = 100  # Update target network every N steps
        self.step_count = 0
        
        # Training state
        self.training = True
        self.last_state = None
        self.last_action = None
        self.episode_rewards = []
        self.current_episode_reward = 0
        
    def encode_state(self, observation: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """Encode observation into neural network input"""
        
        # Game state features
        game_features = []
        
        # Basic game info
        game_features.extend([
            observation.get("tick", 0) / 100.0,  # Normalize tick
            len(observation.get("players_in_room", [])) / 10.0,  # Normalize player count
            len(observation.get("connected_rooms", [])) / 10.0,  # Normalize room count
            len(observation.get("dead_bodies", [])),  # Dead bodies
            observation.get("task_progress", 0.0),  # Task progress
            observation.get("completed_tasks", 0) / 50.0,  # Normalize completed tasks
            observation.get("total_tasks", 0) / 50.0,  # Normalize total tasks
            observation.get("living_players", 0) / 10.0,  # Normalize living players
            observation.get("living_impostors", 0) / 3.0,  # Normalize living impostors
        ])
        
        # Player abilities (binary features)
        abilities = [
            "can_kill", "can_vent", "can_sabotage", "can_shapeshift", 
            "can_protect", "can_emergency"
        ]
        for ability in abilities:
            game_features.append(1.0 if observation.get(ability, False) else 0.0)
        
        # Room features (binary features)
        room_features = observation.get("room_features", {})
        room_abilities = [
            "emergency_button", "admin_table", "security_cameras", 
            "vitals", "lights_on", "doors_open"
        ]
        for feature in room_abilities:
            game_features.append(1.0 if room_features.get(feature, False) else 0.0)
        
        # Active sabotages (one-hot encoding)
        sabotage_types = ["lights", "oxygen", "reactor", "communications", "doors"]
        active_sabotages = observation.get("active_sabotages", [])
        for sabotage in sabotage_types:
            game_features.append(1.0 if sabotage in active_sabotages else 0.0)
        
        # Crisis timers (normalized)
        crisis_timers = observation.get("crisis_timers", {})
        for sabotage in ["oxygen", "reactor"]:
            timer = crisis_timers.get(sabotage, 0)
            game_features.append(timer / 30.0 if timer else 0.0)  # Normalize by max timer
        
        # Pad to fixed size
        while len(game_features) < self.state_dim:
            game_features.append(0.0)
        game_features = game_features[:self.state_dim]
        
        # Communication features (simplified for now)
        comm_features = [0.0] * 100  # Placeholder for communication encoding
        
        game_tensor = torch.FloatTensor(game_features).unsqueeze(0).to(self.device)
        comm_tensor = torch.FloatTensor(comm_features).unsqueeze(0).to(self.device)
        
        return game_tensor, comm_tensor
    
    def choose_action(self, observation: Dict) -> Action:
        """Choose action using epsilon-greedy policy"""
        
        # Encode state
        game_state, comm_state = self.encode_state(observation)
        
        # Epsilon-greedy action selection
        if self.training and random.random() < self.epsilon:
            # Random action
            action_idx = random.randint(0, self.action_dim - 1)
        else:
            # Greedy action
            with torch.no_grad():
                q_values, _ = self.q_network(game_state, comm_state)
                action_idx = q_values.argmax().item()
        
        # Convert to action
        action_type = self.idx_to_action[action_idx]
        
        # Determine target based on action type and observation
        target = self._get_action_target(action_type, observation)
        
        # Store state and action for learning
        if self.training:
            self.last_state = (game_state, comm_state)
            self.last_action = action_idx
        
        return Action(action_type, target=target)
    
    def _get_action_target(self, action_type: str, observation: Dict) -> Optional[str]:
        """Get appropriate target for action based on observation"""
        
        if action_type == "move":
            connected_rooms = observation.get("connected_rooms", [])
            return random.choice(connected_rooms) if connected_rooms else None
            
        elif action_type == "kill":
            players_in_room = observation.get("players_in_room", [])
            return random.choice(players_in_room) if players_in_room else None
            
        elif action_type == "vent":
            vents = observation.get("vents", [])
            return random.choice(vents) if vents else None
            
        elif action_type in ["sabotage_doors"]:
            connected_rooms = observation.get("connected_rooms", [])
            return random.choice(connected_rooms) if connected_rooms else None
            
        elif action_type == "shapeshift":
            living_players = observation.get("living_players", 0)
            # This would need access to actual player list
            return None  # Simplified for now
            
        return None
    
    def learn(self, reward: float, next_observation: Dict, done: bool):
        """Learn from experience"""
        
        if not self.training or self.last_state is None:
            return
            
        # Encode next state
        next_game_state, next_comm_state = self.encode_state(next_observation)
        
        # Store experience
        self.replay_buffer.push(
            self.last_state, self.last_action, reward, 
            (next_game_state, next_comm_state), done
        )
        
        # Update episode reward
        self.current_episode_reward += reward
        
        if done:
            self.episode_rewards.append(self.current_episode_reward)
            self.current_episode_reward = 0
        
        # Train if we have enough experiences
        if len(self.replay_buffer) >= self.batch_size:
            self._train_step()
        
        # Update target network
        self.step_count += 1
        if self.step_count % self.target_update_freq == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
    
    def _train_step(self):
        """Perform one training step"""
        
        # Sample batch
        batch = self.replay_buffer.sample(self.batch_size)
        
        # Unpack batch
        states, actions, rewards, next_states, dones = zip(*batch)
        
        # Convert to tensors
        game_states = torch.cat([s[0] for s in states])
        comm_states = torch.cat([s[1] for s in states])
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_game_states = torch.cat([s[0] for s in next_states])
        next_comm_states = torch.cat([s[1] for s in next_states])
        dones = torch.BoolTensor(dones).to(self.device)
        
        # Current Q values
        current_q_values, _ = self.q_network(game_states, comm_states)
        current_q_values = current_q_values.gather(1, actions.unsqueeze(1))
        
        # Next Q values from target network
        with torch.no_grad():
            next_q_values, _ = self.target_network(next_game_states, next_comm_states)
            next_q_values = next_q_values.max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
    
    def save_model(self, filepath: str):
        """Save model weights"""
        torch.save({
            'q_network': self.q_network.state_dict(),
            'target_network': self.target_network.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'step_count': self.step_count,
        }, filepath)
    
    def load_model(self, filepath: str):
        """Load model weights"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.q_network.load_state_dict(checkpoint['q_network'])
        self.target_network.load_state_dict(checkpoint['target_network'])
        self.optimizer.load_state_dict(checkpoint['optimizer'])
        self.epsilon = checkpoint['epsilon']
        self.step_count = checkpoint['step_count']
    
    def set_training(self, training: bool):
        """Set training mode"""
        self.training = training
        self.q_network.train(training)
