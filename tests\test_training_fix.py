#!/usr/bin/env python3
"""
Test the training fix for gradient issues
"""

import os
# Fix OpenMP warning
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
from agents.ultimate_rl_agent import UltimateAmongUsAgent
from training.curriculum_learning import AdaptiveCurriculumLearning

def test_training_fix():
    """Test that the gradient issue is fixed"""
    print("🧪 Testing Training Fix...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    agent = UltimateAmongUsAgent(
        player_color="Red",
        role_name="Crewmate",
        device=device
    )
    
    # Create mock observations
    mock_obs1 = {
        "tick": 50,
        "players_in_room": ["Red", "Blue"],
        "connected_rooms": ["Cafeteria", "Admin"],
        "dead_bodies": [],
        "task_progress": 0.3,
        "completed_tasks": 2,
        "total_tasks": 8,
        "living_players": 6,
        "living_impostors": 2,
        "can_kill": False,
        "can_vent": False,
        "can_sabotage": False,
        "active_sabotages": [],
        "in_meeting": False
    }
    
    mock_obs2 = mock_obs1.copy()
    mock_obs2["tick"] = 51
    mock_obs2["completed_tasks"] = 3
    
    print("   Testing action selection...")
    action = agent.choose_action(mock_obs1)
    print(f"   ✅ Action selected: {action.action_type}")
    
    print("   Testing learning step...")
    agent.learn_from_experience(2.5, mock_obs2, False)
    print("   ✅ Learning step completed without gradient error")
    
    # Test multiple learning steps to trigger network training
    print("   Testing multiple learning steps...")
    for i in range(10):
        reward = 1.0 + i * 0.1
        agent.learn_from_experience(reward, mock_obs2, False)
    
    print("   ✅ Multiple learning steps completed")
    
    # Test curriculum integration
    print("   Testing curriculum integration...")
    curriculum = AdaptiveCurriculumLearning(agent, device)
    config = curriculum.get_current_config()
    
    # Simulate a few episodes
    for episode in range(3):
        curriculum.record_episode_result(True, {"performance_score": 0.8})
    
    print("   ✅ Curriculum integration working")
    
    print("   ✅ All training fixes verified!")

def main():
    print("🔧 Testing Ultimate Training System Fixes")
    print("=" * 50)
    
    try:
        test_training_fix()
        
        print("\n" + "=" * 50)
        print("🎉 Training fixes successful!")
        print("✅ Gradient tracking issue fixed")
        print("✅ OpenMP warning suppressed")
        print("✅ Error handling added")
        print("✅ Ready for full training!")
        
    except Exception as e:
        print(f"\n❌ Fix test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
