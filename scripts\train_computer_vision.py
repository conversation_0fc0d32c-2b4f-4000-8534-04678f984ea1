#!/usr/bin/env python3
"""
Computer Vision Training Script for Among Us.

This script trains the AI to play Among Us using computer vision on the actual game.
It includes comprehensive learning for movement, tasks, UI interaction, and gameplay.
"""

import os
import sys
import argparse
import logging
import time
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.real_game_learning import RealGameLearner, LearningPhase
from soma.computer_vision.player_detector import PlayerColor
from soma.computer_vision.cv_trainer import CVTrainer, CVTrainingConfig, CVTrainingMode

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cv_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Among Us Computer Vision Training')
    
    parser.add_argument('--mode', type=str, default='observation_only',
                       choices=['observation_only', 'assisted_play', 'full_automation', 'hybrid'],
                       help='Training mode')
    
    parser.add_argument('--color', type=str, default='red',
                       choices=['red', 'blue', 'green', 'pink', 'orange', 'yellow', 
                               'black', 'white', 'purple', 'brown', 'cyan', 'lime'],
                       help='Player color to control')
    
    parser.add_argument('--phase', type=str, default='all',
                       choices=['basic_movement', 'ui_interaction', 'task_learning', 
                               'navigation', 'social_interaction', 'advanced_gameplay', 'all'],
                       help='Learning phase to run')
    
    parser.add_argument('--max-games', type=int, default=5,
                       help='Maximum number of games to play')
    
    parser.add_argument('--max-duration', type=int, default=1800,
                       help='Maximum training duration in seconds (30 minutes default)')
    
    parser.add_argument('--calibrate-only', action='store_true',
                       help='Only run system calibration')
    
    parser.add_argument('--load-knowledge', type=str, default=None,
                       help='Path to load previously learned knowledge')
    
    parser.add_argument('--save-knowledge', type=str, default=None,
                       help='Path to save learned knowledge')
    
    parser.add_argument('--fps', type=int, default=5,
                       help='Target FPS for computer vision')
    
    parser.add_argument('--action-delay', type=float, default=1.0,
                       help='Delay between actions in seconds')
    
    parser.add_argument('--no-safety', action='store_true',
                       help='Disable safety features (not recommended)')
    
    return parser.parse_args()


def setup_training_config(args) -> CVTrainingConfig:
    """Set up training configuration from arguments."""
    
    # Convert string color to PlayerColor enum
    color_map = {
        'red': PlayerColor.RED,
        'blue': PlayerColor.BLUE,
        'green': PlayerColor.GREEN,
        'pink': PlayerColor.PINK,
        'orange': PlayerColor.ORANGE,
        'yellow': PlayerColor.YELLOW,
        'black': PlayerColor.BLACK,
        'white': PlayerColor.WHITE,
        'purple': PlayerColor.PURPLE,
        'brown': PlayerColor.BROWN,
        'cyan': PlayerColor.CYAN,
        'lime': PlayerColor.LIME
    }
    
    # Convert string mode to CVTrainingMode enum
    mode_map = {
        'observation_only': CVTrainingMode.OBSERVATION_ONLY,
        'assisted_play': CVTrainingMode.ASSISTED_PLAY,
        'full_automation': CVTrainingMode.FULL_AUTOMATION,
        'hybrid': CVTrainingMode.HYBRID
    }
    
    config = CVTrainingConfig(
        mode=mode_map[args.mode],
        player_color=color_map[args.color],
        max_games=args.max_games,
        max_duration=args.max_duration,
        safety_enabled=not args.no_safety,
        target_fps=args.fps,
        action_delay=args.action_delay
    )
    
    return config


def run_calibration_only(player_color: PlayerColor) -> bool:
    """Run system calibration only."""
    try:
        logger.info("Running system calibration...")
        
        learner = RealGameLearner(player_color)
        
        if not learner.initialize_learning():
            logger.error("Failed to initialize learning system")
            return False
        
        logger.info("System calibration completed successfully!")
        
        # Show calibration results
        progress = learner.get_learning_progress()
        logger.info(f"Calibration results: {progress['calibration']}")
        
        learner.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"Error during calibration: {e}")
        return False


def run_learning_phase(learner: RealGameLearner, phase_name: str) -> bool:
    """Run a specific learning phase."""
    try:
        phase_map = {
            'basic_movement': LearningPhase.BASIC_MOVEMENT,
            'ui_interaction': LearningPhase.UI_INTERACTION,
            'task_learning': LearningPhase.TASK_LEARNING,
            'navigation': LearningPhase.NAVIGATION,
            'social_interaction': LearningPhase.SOCIAL_INTERACTION,
            'advanced_gameplay': LearningPhase.ADVANCED_GAMEPLAY
        }
        
        if phase_name not in phase_map:
            logger.error(f"Unknown learning phase: {phase_name}")
            return False
        
        phase = phase_map[phase_name]
        return learner.start_learning_phase(phase)
        
    except Exception as e:
        logger.error(f"Error running learning phase {phase_name}: {e}")
        return False


def run_full_training(config: CVTrainingConfig, args) -> bool:
    """Run full computer vision training."""
    try:
        logger.info(f"Starting full CV training in {config.mode.value} mode")
        logger.info(f"Player color: {config.player_color.value}")
        logger.info(f"Max games: {config.max_games}, Max duration: {config.max_duration}s")
        
        # Initialize learner
        learner = RealGameLearner(config.player_color)
        
        # Load previous knowledge if specified
        if args.load_knowledge:
            learner.load_learned_knowledge(args.load_knowledge)
        
        # Initialize learning system
        if not learner.initialize_learning():
            logger.error("Failed to initialize learning system")
            return False
        
        # Run specific phase or all phases
        if args.phase == 'all':
            success = learner.run_full_learning_curriculum()
        else:
            success = run_learning_phase(learner, args.phase)
        
        if not success:
            logger.error("Learning phase(s) failed")
            return False
        
        # Show final progress
        progress = learner.get_learning_progress()
        logger.info("Learning Progress:")
        logger.info(f"  Current Phase: {progress['current_phase']}")
        logger.info(f"  Success Metrics: {progress['success_metrics']}")
        logger.info(f"  Learned Patterns: {progress['learned_patterns']}")
        
        # Save knowledge if specified
        if args.save_knowledge:
            learner.save_learned_knowledge(args.save_knowledge)
        
        # Now run actual training with the learned knowledge
        if config.mode != CVTrainingMode.OBSERVATION_ONLY:
            logger.info("Starting actual game training...")
            trainer = CVTrainer(config)
            training_success = trainer.start_training()
            
            if training_success:
                stats = trainer.get_training_stats()
                logger.info("Training Statistics:")
                logger.info(f"  Games Completed: {stats['games_completed']}")
                logger.info(f"  Total Actions: {stats['total_actions']}")
                logger.info(f"  Success Rate: {stats.get('success_rate', 0):.2%}")
        
        learner.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"Error in full training: {e}")
        return False


def main():
    """Main function."""
    try:
        print("🎮 Among Us Computer Vision Training System")
        print("=" * 50)
        
        # Parse arguments
        args = parse_arguments()
        
        # Set up configuration
        config = setup_training_config(args)
        
        # Show configuration
        print(f"Training Mode: {config.mode.value}")
        print(f"Player Color: {config.player_color.value}")
        print(f"Max Games: {config.max_games}")
        print(f"Max Duration: {config.max_duration}s")
        print(f"Safety Enabled: {config.safety_enabled}")
        print()
        
        # Safety warning for automation modes
        if config.mode in [CVTrainingMode.FULL_AUTOMATION, CVTrainingMode.HYBRID]:
            print("⚠️  WARNING: Automation mode will control your mouse and keyboard!")
            print("   Make sure Among Us is the active window and you're ready.")
            print("   Press Ctrl+Shift+Q to emergency stop at any time.")
            print()
            
            response = input("Continue? (y/N): ")
            if response.lower() != 'y':
                print("Training cancelled.")
                return
        
        # Run calibration only if requested
        if args.calibrate_only:
            success = run_calibration_only(config.player_color)
            if success:
                print("✅ Calibration completed successfully!")
            else:
                print("❌ Calibration failed!")
            return
        
        # Run full training
        print("🚀 Starting training...")
        success = run_full_training(config, args)
        
        if success:
            print("✅ Training completed successfully!")
        else:
            print("❌ Training failed!")
            
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
