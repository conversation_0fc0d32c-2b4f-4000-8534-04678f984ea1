import random
from typing import List, Set, Optional, Dict

class Task:
    def __init__(
            self,
            name: str,
            category: str,
            locations: List[str],
            duration: int,
            is_visual: bool = False,
            is_common: bool = False,
            steps_required: Optional[int] = None,
            randomized_order: bool = False
    ):
        self.name = name
        self.category = category
        self.locations = locations
        self.duration = duration
        self.is_visual = is_visual
        self.is_common = is_common
        self.steps_required = steps_required
        self.randomized_order = randomized_order
        self.completed_by: Set[str] = set()
        self.progress: Dict[str, int] = {}  # Track progress for multi-step tasks
        self.current_step: Dict[str, int] = {}  # Current step for each player
    
    def create_instance(self) -> "Task":
        """Return a personalized version of this task with randomized room order if needed."""
        if self.steps_required and self.randomized_order:
            sampled_locations = random.sample(self.locations, self.steps_required)
        else:
            sampled_locations = self.locations.copy()
        return Task(
            name=self.name,
            category=self.category,
            locations=sampled_locations,
            duration=self.duration,
            is_visual=self.is_visual,
            is_common=self.is_common,
            steps_required=self.steps_required,
            randomized_order=self.randomized_order
        )
    
    def complete(self, player_color: str):
        """Complete a step of the task"""
        if player_color not in self.current_step:
            self.current_step[player_color] = 0
        if player_color not in self.progress:
            self.progress[player_color] = 0

        self.progress[player_color] += 1
        self.current_step[player_color] += 1

        # Check if task is fully complete
        required_steps = self.steps_required or 1
        if self.progress[player_color] >= required_steps:
            self.completed_by.add(player_color)

    def is_complete(self, player_color: str) -> bool:
        return player_color in self.completed_by

    def get_progress(self, player_color: str) -> float:
        """Get completion progress (0.0 to 1.0)"""
        if player_color not in self.progress:
            return 0.0
        required_steps = self.steps_required or 1
        return min(1.0, self.progress[player_color] / required_steps)

    def get_current_location(self, player_color: str) -> Optional[str]:
        """Get the current location for this player's task step"""
        if player_color not in self.current_step:
            self.current_step[player_color] = 0

        step = self.current_step[player_color]
        if step < len(self.locations):
            return self.locations[step]
        return None
    
    def __repr__(self):
        return f"Task({self.name}, {self.category}, {self.locations})"