# Among Us Computer Vision Training Guide

This guide explains how to use the computer vision system to train your AI on the actual Among Us game.

## 🎯 Overview

The computer vision system allows your AI to:
- **See** the actual Among Us game through screen capture
- **Learn** movement, tasks, UI interaction, and gameplay mechanics
- **Control** the game through mouse and keyboard automation
- **Adapt** to real game conditions and timing

## 🚀 Quick Start

### 1. Prerequisites

Make sure you have:
- Among Us installed and running
- Python environment with SOMA dependencies
- Computer vision libraries installed (`pip install -r requirements.txt`)

### 2. Basic Setup

```bash
# Test the system first
python scripts/test_cv_basic.py

# Calibrate for your setup
python scripts/calibrate_cv_system.py

# Start with safe observation mode
python scripts/observe_game.py
```

### 3. Advanced Training (Coming Soon)

The full automation training system is being developed. For now, use the observation mode to collect data and understand how the system works.

```bash
# Advanced training (when available)
python scripts/train_computer_vision.py --mode full_automation --color red --max-games 5
```

## 📋 Training Modes

### 🔍 Observation Only
- **Safe**: No game control, just observes
- **Purpose**: Learn game patterns and mechanics
- **Use case**: Initial learning and data collection

```bash
python scripts/train_computer_vision.py --mode observation_only
```

### 🤝 Assisted Play
- **Interactive**: Human plays, AI learns
- **Purpose**: Learn from human behavior
- **Use case**: Teaching optimal strategies

```bash
python scripts/train_computer_vision.py --mode assisted_play
```

### 🤖 Full Automation
- **Autonomous**: AI controls everything
- **Purpose**: Independent gameplay
- **Use case**: Testing trained models

```bash
python scripts/train_computer_vision.py --mode full_automation
```

### 🔄 Hybrid
- **Mixed**: Alternates between human and AI control
- **Purpose**: Gradual transition to autonomy
- **Use case**: Progressive learning

```bash
python scripts/train_computer_vision.py --mode hybrid
```

## 🎓 Learning Phases

The AI learns in structured phases:

### Phase 1: Basic Movement
- **Goal**: Learn to move around the map
- **Skills**: Click-to-move, pathfinding basics
- **Duration**: 5-10 minutes

### Phase 2: UI Interaction
- **Goal**: Learn to interact with game UI
- **Skills**: Button clicking, menu navigation
- **Duration**: 10-15 minutes

### Phase 3: Task Learning
- **Goal**: Learn to complete tasks
- **Skills**: Task-specific interactions, timing
- **Duration**: 20-30 minutes

### Phase 4: Navigation
- **Goal**: Learn map layout and efficient movement
- **Skills**: Room connections, optimal paths
- **Duration**: 15-20 minutes

### Phase 5: Social Interaction
- **Goal**: Learn meeting and voting mechanics
- **Skills**: Chat, voting, discussion behavior
- **Duration**: 15-25 minutes

### Phase 6: Advanced Gameplay
- **Goal**: Learn role-specific abilities
- **Skills**: Sabotages, kills, advanced strategies
- **Duration**: 20-30 minutes

## ⚙️ Configuration Options

### Player Colors
```bash
--color red|blue|green|pink|orange|yellow|black|white|purple|brown|cyan|lime
```

### Learning Phases
```bash
--phase basic_movement|ui_interaction|task_learning|navigation|social_interaction|advanced_gameplay|all
```

### Performance Settings
```bash
--fps 5              # Computer vision frame rate
--action-delay 1.0   # Delay between actions
--max-games 10       # Maximum games to play
--max-duration 1800  # Maximum time (30 minutes)
```

### Safety Settings
```bash
--no-safety          # Disable safety features (not recommended)
```

## 🛡️ Safety Features

### Emergency Stop
- **Hotkey**: `Ctrl+Shift+Q`
- **Purpose**: Immediately stop all automation
- **Always available**: Even during full automation

### Boundary Detection
- **Game window**: Actions limited to Among Us window
- **Click precision**: Prevents accidental clicks outside game
- **Rate limiting**: Prevents excessive actions

### Error Handling
- **Consecutive errors**: Stops after 5 consecutive failures
- **Timeout protection**: Prevents infinite loops
- **Graceful degradation**: Falls back to safer modes on errors

## 📊 Monitoring Progress

### Real-time Stats
The system provides real-time feedback:
- **Movement success rate**: How well the AI moves
- **Task completion rate**: Task success percentage
- **UI interaction rate**: UI element interaction success
- **Navigation accuracy**: Pathfinding effectiveness

### Saved Data
Training data is automatically saved:
- **Observations**: `data/cv_observations/observations_*.json`
- **Training stats**: `data/cv_observations/training_stats_*.json`
- **Learned knowledge**: Can be saved/loaded for reuse

## 🔧 Troubleshooting

### Common Issues

#### "Among Us window not found"
- Make sure Among Us is running
- Try different window titles in the code
- Check if game is minimized

#### "Movement not working"
- Run calibration first: `--calibrate-only`
- Check if game window is active
- Verify mouse/keyboard permissions

#### "Tasks not completing"
- Increase action delay: `--action-delay 2.0`
- Check task detection accuracy
- Try observation mode first to learn patterns

#### "High error rate"
- Lower FPS: `--fps 3`
- Increase delays between actions
- Check screen resolution and scaling

### Performance Optimization

#### For Better Accuracy
```bash
--fps 3 --action-delay 2.0  # Slower but more accurate
```

#### For Faster Training
```bash
--fps 10 --action-delay 0.5  # Faster but less reliable
```

## 📈 Advanced Usage

### Custom Learning Phases
Run specific learning phases:
```bash
# Focus on movement only
python scripts/train_computer_vision.py --phase basic_movement

# Focus on tasks only
python scripts/train_computer_vision.py --phase task_learning
```

### Knowledge Transfer
Save and reuse learned knowledge:
```bash
# Save knowledge
python scripts/train_computer_vision.py --save-knowledge learned_skills.json

# Load previous knowledge
python scripts/train_computer_vision.py --load-knowledge learned_skills.json
```

### Multi-Session Training
```bash
# Session 1: Learn basics
python scripts/train_computer_vision.py --phase basic_movement --save-knowledge basics.json

# Session 2: Learn tasks (using previous knowledge)
python scripts/train_computer_vision.py --phase task_learning --load-knowledge basics.json --save-knowledge with_tasks.json

# Session 3: Full gameplay
python scripts/train_computer_vision.py --mode full_automation --load-knowledge with_tasks.json
```

## 🎮 Integration with Existing Training

The computer vision system integrates with your existing SOMA training:

### After Simulation Training
1. Train in simulation until 70%+ win rate
2. Switch to computer vision for real-game experience
3. Use learned strategies in actual gameplay

### Hybrid Approach
1. Use simulation for basic strategy learning
2. Use computer vision for real-game mechanics
3. Combine both for comprehensive training

## 📝 Best Practices

### Before Starting
1. **Test calibration** first with `--calibrate-only`
2. **Start with observation mode** to understand the system
3. **Use assisted play** to teach good strategies
4. **Gradually progress** to full automation

### During Training
1. **Monitor the AI** especially in automation mode
2. **Use emergency stop** if something goes wrong
3. **Save progress** regularly with `--save-knowledge`
4. **Adjust settings** based on performance

### After Training
1. **Review saved data** to understand what was learned
2. **Test in different game conditions** (different maps, player counts)
3. **Combine with simulation training** for best results
4. **Share successful configurations** with the community

## 🔮 Future Enhancements

The computer vision system is designed to be extensible:
- **Multi-map support**: Polus, Mira HQ, Airship
- **Advanced task recognition**: More sophisticated task patterns
- **Better social AI**: Improved chat and voting behavior
- **Performance optimization**: Faster, more accurate detection
- **Integration improvements**: Better connection with existing RL agents

---

**Ready to train your AI on the real game? Start with calibration and work your way up to full automation!** 🚀
