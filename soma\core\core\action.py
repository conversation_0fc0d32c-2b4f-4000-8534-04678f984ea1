class Action:
    def __init__(self, action_type: str, target=None, data=None):
        self.action_type = action_type  # e.g., "move", "task", "kill", etc.
        self.target = target            # Could be a Room, Player, or Task name
        self.data = data                # Additional action data (e.g., sabotage type)

    def __repr__(self):
        return f"Action({self.action_type}, {self.target}, {self.data})"

VALID_ACTIONS = {
    # Basic Actions
    "move",       # Move to another connected room
    "task",       # Work on current room task
    "kill",       # Attempt to kill a nearby player
    "report",     # Report a body in current room
    "vent",       # Move via vent to another room
    "button",     # Call emergency meeting
    "idle",       # Do nothing

    # Sabotage Actions (Impostor only)
    "sabotage_lights",      # Turn off lights (reduces vision)
    "sabotage_oxygen",      # Oxygen crisis (time-limited fix required)
    "sabotage_reactor",     # Reactor meltdown (time-limited fix required)
    "sabotage_comms",       # Disable communications (disables security features)
    "sabotage_doors",       # Close doors in specific areas

    # Security/Information Actions
    "use_security",         # View security cameras
    "use_admin",           # Check admin table (player counts per room)
    "use_vitals",          # Check vitals (who's alive/dead)
    "use_doorlog",         # Check door logs (MIRA HQ only)

    # Sabotage Fixes
    "fix_lights",          # Fix lights sabotage
    "fix_oxygen",          # Fix oxygen sabotage
    "fix_reactor",         # Fix reactor sabotage
    "fix_comms",           # Fix communications sabotage
    "fix_doors",           # Open sabotaged doors

    # Advanced Actions
    "shapeshift",          # Shapeshifter ability
    "protect",             # Guardian Angel ability
    "use_vent_engineer",   # Engineer vent usage
}