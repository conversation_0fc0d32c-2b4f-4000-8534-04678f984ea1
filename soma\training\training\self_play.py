import torch
import random
import copy
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from agents.ultimate_rl_agent import UltimateAmongUsAgent
from agents.scripted_agent import ScriptedAgent
from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS
from config.colors import get_lobby_colors, get_recommended_impostors, validate_lobby_configuration

class SelfPlayMode(Enum):
    """Different self-play training modes"""
    PURE_SELF_PLAY = "pure_self_play"           # All RL agents
    MIXED_OPPONENTS = "mixed_opponents"         # RL + scripted agents
    LEAGUE_PLAY = "league_play"                 # Current vs past versions
    POPULATION_BASED = "population_based"       # Multiple agent populations

@dataclass
class SelfPlayConfig:
    """Configuration for self-play training"""
    mode: SelfPlayMode
    num_rl_agents: int = 6                      # Number of RL agents in game
    num_scripted_agents: int = 0                # Number of scripted agents
    agent_diversity: float = 0.1                # How different agents should be
    league_size: int = 10                       # Number of past agents to keep
    update_frequency: int = 100                 # How often to update league
    population_size: int = 4                    # Number of different agent types

class AmongUsRuleset:
    """Configurable Among Us ruleset based on official game options"""
    
    def __init__(self):
        # Player settings
        self.max_players = 10
        self.num_impostors = 2
        
        # Game settings
        self.confirm_ejects = True
        self.emergency_meetings = 1
        self.emergency_cooldown = 15
        self.discussion_time = 15
        self.voting_time = 120
        self.player_speed = 1.0
        self.crewmate_vision = 1.0
        self.impostor_vision = 1.5
        
        # Impostor settings
        self.kill_cooldown = 45
        self.kill_distance = 1  # 0=Short, 1=Normal, 2=Long
        self.visual_tasks = True
        self.common_tasks = 1
        self.long_tasks = 1
        self.short_tasks = 2
        
        # Sabotage settings
        self.sabotage_cooldown = 30
        self.lights_sabotage_vision = 0.25
        self.reactor_countdown = 30
        self.oxygen_countdown = 30
        self.comms_sabotage_disables_admin = True
        
        # Meeting settings
        self.anonymous_votes = False
        self.skip_vote_allowed = True
        
    def randomize_settings(self, difficulty: str = "balanced", lobby_size: int = 10):
        """Randomize settings based on difficulty level and lobby size"""

        # Adjust impostor count based on lobby size (official Among Us ratios)
        if lobby_size <= 6:
            max_impostors = 2
        elif lobby_size <= 9:
            max_impostors = 3
        else:
            max_impostors = 3  # Max 3 impostors in official Among Us

        if difficulty == "easy":
            # Easier for crewmates
            self.num_impostors = random.randint(1, min(2, max_impostors))
            self.kill_cooldown = random.randint(45, 60)
            self.crewmate_vision = random.uniform(1.0, 1.5)
            self.impostor_vision = random.uniform(1.0, 1.25)
            self.emergency_meetings = random.randint(1, 2)

        elif difficulty == "hard":
            # Harder for crewmates
            self.num_impostors = random.randint(2, max_impostors)
            self.kill_cooldown = random.randint(20, 35)
            self.crewmate_vision = random.uniform(0.5, 1.0)
            self.impostor_vision = random.uniform(1.5, 2.0)
            self.emergency_meetings = random.randint(0, 1)

        else:  # balanced
            # Standard competitive settings with some variation
            self.num_impostors = random.randint(1, max_impostors)
            self.kill_cooldown = random.randint(30, 50)
            self.crewmate_vision = random.uniform(0.75, 1.25)
            self.impostor_vision = random.uniform(1.25, 1.75)
            self.emergency_meetings = random.randint(1, 2)

        # Update max players
        self.max_players = lobby_size
        
        # Common randomizations
        self.discussion_time = random.randint(0, 30)
        self.voting_time = random.randint(60, 180)
        self.player_speed = random.uniform(0.75, 1.5)
        self.visual_tasks = random.choice([True, False])
        self.confirm_ejects = random.choice([True, False])
        self.anonymous_votes = random.choice([True, False])
        
        # Task randomization
        self.common_tasks = random.randint(0, 2)
        self.long_tasks = random.randint(1, 3)
        self.short_tasks = random.randint(2, 5)
        
        # Sabotage randomization
        self.sabotage_cooldown = random.randint(15, 45)
        self.reactor_countdown = random.randint(20, 45)
        self.oxygen_countdown = random.randint(20, 45)
    
    def to_game_settings(self) -> Dict:
        """Convert ruleset to game settings dictionary"""
        return {
            "max_players": self.max_players,
            "num_impostors": self.num_impostors,
            "confirm_ejects": self.confirm_ejects,
            "emergency_meetings": self.emergency_meetings,
            "emergency_cooldown": self.emergency_cooldown,
            "discussion_time": self.discussion_time,
            "voting_time": self.voting_time,
            "player_speed": self.player_speed,
            "crewmate_vision": self.crewmate_vision,
            "impostor_vision": self.impostor_vision,
            "kill_cooldown": self.kill_cooldown,
            "kill_distance": self.kill_distance,
            "visual_tasks": self.visual_tasks,
            "common_tasks": self.common_tasks,
            "long_tasks": self.long_tasks,
            "short_tasks": self.short_tasks,
            "sabotage_cooldown": self.sabotage_cooldown,
            "lights_vision": self.lights_sabotage_vision,
            "reactor_countdown": self.reactor_countdown,
            "oxygen_countdown": self.oxygen_countdown,
            "anonymous_votes": self.anonymous_votes,
            "skip_vote_allowed": self.skip_vote_allowed,
        }

class SelfPlayTrainer:
    """Self-play training system for Among Us AI"""
    
    def __init__(self, config: SelfPlayConfig, device: str = "cpu"):
        self.config = config
        self.device = device
        
        # Agent populations
        self.agent_pool = []
        self.league_agents = []  # Past versions for league play
        
        # Initialize agent pool
        self._initialize_agent_pool()
        
        # Ruleset manager
        self.ruleset = AmongUsRuleset()
        
    def _initialize_agent_pool(self):
        """Initialize the pool of RL agents"""

        # Get colors for the agent pool size
        colors = get_lobby_colors(self.config.num_rl_agents, randomize=True)

        for i, color in enumerate(colors):
            # Create agent with slight variations
            agent = UltimateAmongUsAgent(
                player_color=color,
                role_name="Crewmate",  # Will be assigned dynamically
                learning_rate=1e-4 * random.uniform(0.5, 2.0),  # Vary learning rates
                device=self.device
            )

            # Add some diversity to agent parameters
            if self.config.agent_diversity > 0:
                self._add_agent_diversity(agent, self.config.agent_diversity)

            self.agent_pool.append(agent)
    
    def _add_agent_diversity(self, agent: UltimateAmongUsAgent, diversity: float):
        """Add diversity to agent parameters"""

        # Vary exploration rates (if they exist)
        if hasattr(agent, 'epsilon_start'):
            agent.epsilon_start *= random.uniform(1 - diversity, 1 + diversity)
        if hasattr(agent, 'epsilon_end'):
            agent.epsilon_end *= random.uniform(1 - diversity, 1 + diversity)
        if hasattr(agent, 'epsilon'):
            agent.epsilon *= random.uniform(1 - diversity, 1 + diversity)

        # Vary network initialization slightly
        try:
            with torch.no_grad():
                for param in agent.game_net.parameters():
                    param.add_(torch.randn_like(param) * diversity * 0.01)
        except Exception as e:
            print(f"⚠️  Could not add network diversity: {e}")
    
    def create_self_play_game(self, ruleset: Optional[AmongUsRuleset] = None) -> Tuple[SimulatedGame, List[UltimateAmongUsAgent]]:
        """Create a self-play game with selected agents"""
        
        if ruleset is None:
            ruleset = self.ruleset
        
        # Get game settings
        settings = DEFAULT_SETTINGS.copy()
        settings.update(ruleset.to_game_settings())
        
        # Create map
        rooms = create_skeld_map()
        cafeteria = rooms["Cafeteria"]
        
        # Select agents for this game
        selected_agents = self._select_agents_for_game()
        
        # Determine roles
        num_players = len(selected_agents)
        num_impostors = min(ruleset.num_impostors, num_players - 1)
        
        # Randomly assign impostor roles
        impostor_indices = random.sample(range(num_players), num_impostors)
        
        players = []
        rl_agents_in_game = []
        
        for i, agent in enumerate(selected_agents):
            role = IMPOSTOR if i in impostor_indices else CREWMATE
            
            if isinstance(agent, UltimateAmongUsAgent):
                # RL agent
                agent.role_name = role.name
                player = Player(agent.player_color, role, cafeteria, agent)
                rl_agents_in_game.append(agent)
            else:
                # Scripted agent
                color = getattr(agent, 'color', getattr(agent, 'player_color', f'Agent_{i}'))
                player = Player(color, role, cafeteria, agent)
            
            players.append(player)
        
        return SimulatedGame(players, settings), rl_agents_in_game
    
    def _select_agents_for_game(self) -> List:
        """Select agents for a game based on self-play mode"""
        
        if self.config.mode == SelfPlayMode.PURE_SELF_PLAY:
            # All RL agents
            return random.sample(self.agent_pool, min(6, len(self.agent_pool)))
        
        elif self.config.mode == SelfPlayMode.MIXED_OPPONENTS:
            # Mix of RL and scripted agents
            num_rl = min(self.config.num_rl_agents, len(self.agent_pool))
            num_scripted = self.config.num_scripted_agents
            
            selected_agents = random.sample(self.agent_pool, num_rl)
            
            # Add scripted agents
            scripted_strategies = ["balanced", "aggressive_impostor", "detective_crewmate", "task_focused_crewmate"]

            # Use colors not already taken by RL agents
            all_colors = [
                "Red", "Blue", "Green", "Pink", "Orange", "Yellow",
                "Black", "White", "Purple", "Brown", "Cyan", "Lime",
                "Maroon", "Rose", "Banana", "Gray", "Tan", "Coral"
            ]
            used_colors = {agent.player_color for agent in selected_agents if hasattr(agent, 'player_color')}
            available_colors = [c for c in all_colors if c not in used_colors]

            for i in range(min(num_scripted, len(available_colors))):
                strategy = random.choice(scripted_strategies)
                scripted_agent = ScriptedAgent(available_colors[i], strategy=strategy)
                selected_agents.append(scripted_agent)
            
            return selected_agents
        
        elif self.config.mode == SelfPlayMode.LEAGUE_PLAY:
            # Current agents vs past versions
            current_agents = random.sample(self.agent_pool, 3)
            
            if self.league_agents:
                past_agents = random.sample(self.league_agents, min(3, len(self.league_agents)))
                return current_agents + past_agents
            else:
                return current_agents + random.sample(self.agent_pool, 3)
        
        else:  # POPULATION_BASED
            # Select from different populations
            return random.sample(self.agent_pool, min(6, len(self.agent_pool)))
    
    def update_league(self, episode: int):
        """Update the league with current best agents"""
        
        if episode % self.config.update_frequency == 0 and episode > 0:
            # Save current best agents to league
            for agent in self.agent_pool[:2]:  # Top 2 agents
                # Create a copy of the agent
                league_agent = copy.deepcopy(agent)
                league_agent.player_color = f"League_{len(self.league_agents)}"
                self.league_agents.append(league_agent)
            
            # Keep league size manageable
            if len(self.league_agents) > self.config.league_size:
                self.league_agents = self.league_agents[-self.config.league_size:]
            
            print(f"🏆 League updated at episode {episode}. League size: {len(self.league_agents)}")
    
    def get_training_stats(self) -> Dict:
        """Get self-play training statistics"""
        
        stats = {
            "mode": self.config.mode.value,
            "agent_pool_size": len(self.agent_pool),
            "league_size": len(self.league_agents),
            "num_rl_agents": self.config.num_rl_agents,
            "num_scripted_agents": self.config.num_scripted_agents,
        }
        
        # Add agent performance stats
        for i, agent in enumerate(self.agent_pool):
            agent_stats = agent.get_performance_stats()
            stats[f"agent_{i}_performance"] = agent_stats
        
        return stats

def create_random_ruleset(difficulty: str = "balanced") -> AmongUsRuleset:
    """Create a randomized Among Us ruleset"""
    ruleset = AmongUsRuleset()
    ruleset.randomize_settings(difficulty)
    return ruleset

def create_competitive_ruleset() -> AmongUsRuleset:
    """Create a competitive Among Us ruleset"""
    ruleset = AmongUsRuleset()
    
    # Competitive settings
    ruleset.num_impostors = 2
    ruleset.kill_cooldown = 30
    ruleset.crewmate_vision = 1.0
    ruleset.impostor_vision = 1.5
    ruleset.emergency_meetings = 1
    ruleset.discussion_time = 15
    ruleset.voting_time = 120
    ruleset.visual_tasks = False  # More challenging
    ruleset.confirm_ejects = False  # More uncertainty
    ruleset.anonymous_votes = True  # More strategic
    
    return ruleset

def create_casual_ruleset() -> AmongUsRuleset:
    """Create a casual Among Us ruleset"""
    ruleset = AmongUsRuleset()
    
    # Casual settings
    ruleset.num_impostors = 1
    ruleset.kill_cooldown = 45
    ruleset.crewmate_vision = 1.25
    ruleset.impostor_vision = 1.25
    ruleset.emergency_meetings = 2
    ruleset.discussion_time = 30
    ruleset.voting_time = 180
    ruleset.visual_tasks = True  # Easier detection
    ruleset.confirm_ejects = True  # More information
    ruleset.anonymous_votes = False  # Less strategic complexity
    
    return ruleset
