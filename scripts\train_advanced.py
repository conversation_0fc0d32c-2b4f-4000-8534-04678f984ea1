#!/usr/bin/env python3
"""
Advanced Among Us AI Training with Self-Play and Variable Rulesets
"""

import os
import sys
# Fix OpenMP warning
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import torch
import argparse
import random
from datetime import datetime
from typing import Dict, List

from soma.agents.agents.ultimate_rl_agent import UltimateAmongUsAgent
from soma.training.training.curriculum_learning import AdaptiveCurriculumLearning
from soma.training.training.self_play import SelfPlayTrainer, SelfPlayConfig, SelfPlayMode
from soma.training.training.self_play import create_random_ruleset, create_competitive_ruleset, create_casual_ruleset
from soma.analytics.advanced_analytics import AdvancedTrainingAnalytics

class AdvancedAmongUsTrainer:
    """Advanced trainer with self-play and variable rulesets"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = config.get("device", "cpu")
        self.episodes = config.get("episodes", 100)
        
        # Initialize LLM if enabled
        self.ollama_client = None
        if config.get("enable_llm", False):
            try:
                from soma.core.core.llm_communication import OllamaClient
                self.ollama_client = OllamaClient(model="deepseek-r1:latest")
                print("✅ DeepSeek-R1 LLM communication enabled")
            except Exception as e:
                print(f"⚠️  LLM communication disabled: {e}")
        
        # Setup training mode
        self.training_mode = config.get("training_mode", "curriculum")
        
        if self.training_mode == "self_play":
            self._setup_self_play_training(config)
        elif self.training_mode == "mixed":
            self._setup_mixed_training(config)
        else:
            self._setup_curriculum_training(config)
        
        # Initialize advanced analytics
        self.analytics = AdvancedTrainingAnalytics(
            save_dir=config.get("analytics_dir", "data/analytics")
        )
        
        # Ruleset configuration
        self.ruleset_mode = config.get("ruleset_mode", "standard")
        self.ruleset_difficulty = config.get("ruleset_difficulty", "balanced")
        
        print(f"🚀 Advanced Among Us AI Training Initialized")
        print(f"📊 Device: {self.device}")
        print(f"🎯 Episodes: {self.episodes}")
        print(f"🎮 Training Mode: {self.training_mode}")
        print(f"📋 Ruleset Mode: {self.ruleset_mode}")
        print(f"🗣️ LLM: {'DeepSeek-R1' if self.ollama_client else 'Disabled'}")
    
    def _setup_curriculum_training(self, config: Dict):
        """Setup traditional curriculum training"""
        self.agent = UltimateAmongUsAgent(
            player_color="Red",
            role_name="Crewmate",
            learning_rate=config.get("learning_rate", 1e-4),
            device=self.device,
            ollama_client=self.ollama_client
        )
        
        self.curriculum = AdaptiveCurriculumLearning(self.agent, self.device)
        self.self_play_trainer = None
    
    def _setup_self_play_training(self, config: Dict):
        """Setup self-play training"""
        self_play_config = SelfPlayConfig(
            mode=SelfPlayMode.PURE_SELF_PLAY,
            num_rl_agents=config.get("num_rl_agents", 6),
            agent_diversity=config.get("agent_diversity", 0.1),
            league_size=config.get("league_size", 10),
            update_frequency=config.get("league_update_freq", 50)
        )
        
        self.self_play_trainer = SelfPlayTrainer(self_play_config, self.device)
        self.curriculum = None
        self.agent = None  # Multiple agents in self-play
    
    def _setup_mixed_training(self, config: Dict):
        """Setup mixed training (self-play + curriculum)"""
        # Primary agent for curriculum
        self.agent = UltimateAmongUsAgent(
            player_color="Red",
            role_name="Crewmate",
            learning_rate=config.get("learning_rate", 1e-4),
            device=self.device,
            ollama_client=self.ollama_client
        )
        
        self.curriculum = AdaptiveCurriculumLearning(self.agent, self.device)
        
        # Self-play trainer for additional agents
        self_play_config = SelfPlayConfig(
            mode=SelfPlayMode.MIXED_OPPONENTS,
            num_rl_agents=config.get("num_rl_agents", 3),
            num_scripted_agents=config.get("num_scripted_agents", 3),
            agent_diversity=config.get("agent_diversity", 0.1)
        )
        
        self.self_play_trainer = SelfPlayTrainer(self_play_config, self.device)
    
    def _get_ruleset_for_episode(self, episode: int):
        """Get ruleset for current episode"""
        
        if self.ruleset_mode == "random":
            return create_random_ruleset(self.ruleset_difficulty)
        elif self.ruleset_mode == "competitive":
            return create_competitive_ruleset()
        elif self.ruleset_mode == "casual":
            return create_casual_ruleset()
        elif self.ruleset_mode == "progressive":
            # Start casual, progress to competitive
            progress = episode / self.episodes
            if progress < 0.3:
                return create_casual_ruleset()
            elif progress < 0.7:
                return create_random_ruleset("balanced")
            else:
                return create_competitive_ruleset()
        else:  # standard
            return None  # Use default settings
    
    def train(self):
        """Main training loop"""
        
        print("🚀 Starting Advanced Among Us AI Training")
        print("=" * 60)
        
        episode = 0
        successful_episodes = 0
        
        try:
            while episode < self.episodes:
                try:
                    # Get ruleset for this episode
                    ruleset = self._get_ruleset_for_episode(episode)
                    
                    # Run episode based on training mode
                    if self.training_mode == "self_play":
                        episode_data = self._run_self_play_episode(ruleset)
                    elif self.training_mode == "mixed":
                        episode_data = self._run_mixed_episode(ruleset)
                    else:
                        episode_data = self._run_curriculum_episode(ruleset)
                    
                    # Record results
                    if episode_data:
                        self.analytics.record_episode(episode_data)
                        successful_episodes += 1
                        
                        # Progress logging
                        if episode % 10 == 0:
                            self._log_progress(episode, episode_data)
                    
                    # Update training systems
                    if self.curriculum and self.curriculum.should_advance_stage():
                        self.curriculum.advance_to_next_stage()
                    
                    if self.self_play_trainer:
                        self.self_play_trainer.update_league(episode)
                    
                except Exception as e:
                    print(f"⚠️  Episode {episode} failed: {e}")
                
                episode += 1
        
        except KeyboardInterrupt:
            print("\n⏸️  Training interrupted by user")
        
        self._finalize_training(episode, successful_episodes)
    
    def _run_curriculum_episode(self, ruleset):
        """Run curriculum training episode"""
        curriculum_config = self.curriculum.get_current_config()
        
        if ruleset:
            # Override game settings with ruleset
            game_settings = curriculum_config.game_settings.copy()
            game_settings.update(ruleset.to_game_settings())
            curriculum_config.game_settings = game_settings
        
        game = self.curriculum.create_training_game(curriculum_config)
        return self._simulate_episode(game, [self.agent], "curriculum")
    
    def _run_self_play_episode(self, ruleset):
        """Run self-play episode"""
        game, rl_agents = self.self_play_trainer.create_self_play_game(ruleset)
        return self._simulate_episode(game, rl_agents, "self_play")
    
    def _run_mixed_episode(self, ruleset):
        """Run mixed training episode"""
        # Alternate between curriculum and self-play
        if random.random() < 0.5:
            return self._run_curriculum_episode(ruleset)
        else:
            return self._run_self_play_episode(ruleset)
    
    def _simulate_episode(self, game, rl_agents: List[UltimateAmongUsAgent], mode: str):
        """Simulate a game episode"""
        
        episode_start_time = datetime.now()
        step_count = 0
        max_steps = 100
        
        # Track all RL agents
        agent_data = {agent.player_color: {"actions": [], "rewards": 0.0} for agent in rl_agents}
        
        try:
            while not game.is_game_over() and step_count < max_steps:
                # Run game tick
                game.run_tick()
                
                # Update all RL agents
                for agent in rl_agents:
                    try:
                        agent_player = next(p for p in game.players if p.agent == agent)
                        observation = game._get_observation(agent_player)
                        reward = self._calculate_reward(agent_player, game)
                        
                        agent.learn_from_experience(reward, observation, game.is_game_over())
                        agent_data[agent.player_color]["rewards"] += reward
                        
                        if hasattr(agent, 'last_action') and agent.last_action is not None:
                            action_name = agent.idx_to_action.get(agent.last_action, 'unknown')
                            agent_data[agent.player_color]["actions"].append(action_name)
                    
                    except Exception as e:
                        print(f"⚠️  Agent {agent.player_color} update failed: {e}")
                
                step_count += 1
        
        except Exception as e:
            print(f"⚠️  Episode simulation failed: {e}")
        
        # Calculate results
        winner = game.get_winner() if game.is_game_over() else "None"
        
        # Create episode data for primary agent (or first RL agent)
        primary_agent = rl_agents[0] if rl_agents else None
        if primary_agent:
            primary_player = next(p for p in game.players if p.agent == primary_agent)
            won = self._did_agent_win(primary_player, winner)
            
            episode_data = {
                'episode_duration': (datetime.now() - episode_start_time).total_seconds(),
                'steps': step_count,
                'role': getattr(primary_player.role, 'name', 'Unknown'),
                'won': won,
                'winner': winner,
                'reward': agent_data[primary_agent.player_color]["rewards"],
                'actions_taken': agent_data[primary_agent.player_color]["actions"],
                'training_mode': mode,
                'num_rl_agents': len(rl_agents),
                'ruleset_mode': self.ruleset_mode,
                'performance_metrics': {
                    'steps': step_count,
                    'actions': len(agent_data[primary_agent.player_color]["actions"])
                }
            }
            
            return episode_data
        
        return None
    
    def _calculate_reward(self, player, game) -> float:
        """Calculate reward for an agent"""
        try:
            base_reward = 0.1 if player.alive else -1.0
            
            if game.is_game_over():
                winner = game.get_winner()
                if self._did_agent_win(player, winner):
                    base_reward += 5.0
                else:
                    base_reward -= 2.0
            
            return base_reward
        except:
            return 0.0
    
    def _did_agent_win(self, agent_player, winner: str) -> bool:
        """Check if agent won"""
        try:
            if winner == "None":
                return False
            
            role_name = getattr(agent_player.role, 'name', 'Unknown')
            if role_name in ["Impostor", "Shapeshifter", "Phantom"]:
                return winner == "Impostors"
            else:
                return winner == "Crewmates"
        except:
            return False
    
    def _log_progress(self, episode: int, episode_data: Dict):
        """Log training progress"""
        
        mode_info = episode_data.get('training_mode', 'unknown')
        num_agents = episode_data.get('num_rl_agents', 1)
        ruleset = episode_data.get('ruleset_mode', 'standard')
        
        print(f"Episode {episode:3d} | "
              f"Mode: {mode_info:10s} | "
              f"Agents: {num_agents} | "
              f"Rules: {ruleset:8s} | "
              f"Role: {episode_data['role'][:8]:8s} | "
              f"Won: {'✅' if episode_data['won'] else '❌'} | "
              f"Reward: {episode_data['reward']:5.1f}")
    
    def _finalize_training(self, episodes: int, successful: int):
        """Finalize training and generate reports"""
        
        print(f"\n🏁 Advanced Training Completed!")
        print(f"📊 Episodes: {successful}/{episodes} successful")
        
        try:
            report = self.analytics.generate_comprehensive_report()
            print(f"📈 Final report generated with {len(report)} sections")
            
            # Self-play specific stats
            if self.self_play_trainer:
                self_play_stats = self.self_play_trainer.get_training_stats()
                print(f"🤖 Self-play stats: {len(self_play_stats)} metrics")
            
        except Exception as e:
            print(f"⚠️  Report generation failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="Advanced Among Us AI Training")
    
    # Basic settings
    parser.add_argument("--episodes", type=int, default=100, help="Number of episodes")
    parser.add_argument("--device", type=str, default="cpu", help="Device (cpu/cuda)")
    parser.add_argument("--llm", action="store_true", help="Enable DeepSeek-R1 LLM")
    
    # Training mode
    parser.add_argument("--mode", type=str, default="curriculum", 
                       choices=["curriculum", "self_play", "mixed"],
                       help="Training mode")
    
    # Self-play settings
    parser.add_argument("--num-rl-agents", type=int, default=6,
                       help="Number of RL agents in self-play (max 18)")
    parser.add_argument("--num-scripted-agents", type=int, default=0,
                       help="Number of scripted agents in mixed mode")
    parser.add_argument("--agent-diversity", type=float, default=0.1,
                       help="Agent diversity factor")
    parser.add_argument("--lobby-size", type=int, default=10,
                       help="Total lobby size (4-15 players)")
    
    # Ruleset settings
    parser.add_argument("--ruleset", type=str, default="standard",
                       choices=["standard", "random", "competitive", "casual", "progressive"],
                       help="Ruleset mode")
    parser.add_argument("--difficulty", type=str, default="balanced",
                       choices=["easy", "balanced", "hard"],
                       help="Ruleset difficulty for random mode")
    
    args = parser.parse_args()
    
    # Create configuration
    config = {
        "episodes": args.episodes,
        "device": args.device,
        "enable_llm": args.llm,
        "training_mode": args.mode,
        "num_rl_agents": args.num_rl_agents,
        "num_scripted_agents": args.num_scripted_agents,
        "agent_diversity": args.agent_diversity,
        "ruleset_mode": args.ruleset,
        "ruleset_difficulty": args.difficulty,
        "learning_rate": 1e-4,
        "analytics_dir": "advanced_training_analytics"
    }
    
    print("🎮 Advanced Among Us AI Training Configuration:")
    print(f"   Episodes: {config['episodes']}")
    print(f"   Device: {config['device']}")
    print(f"   Training Mode: {config['training_mode']}")
    print(f"   Ruleset: {config['ruleset_mode']} ({config['ruleset_difficulty']})")
    print(f"   RL Agents: {config['num_rl_agents']}")
    print(f"   LLM: {'DeepSeek-R1' if config['enable_llm'] else 'Disabled'}")
    
    # Create and run trainer
    trainer = AdvancedAmongUsTrainer(config)
    trainer.train()

if __name__ == "__main__":
    main()
