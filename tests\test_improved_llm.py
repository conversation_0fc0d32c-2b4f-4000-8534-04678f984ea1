#!/usr/bin/env python3
"""
Test the improved LLM communication system
"""

from core.llm_communication import OllamaClient, LLMCommunicationStrategy
from core.communication import Message, MessageType

def test_llm_communication():
    """Test LLM communication with improved prompts"""
    print("🧪 Testing Improved LLM Communication...")
    
    # Test without actual Ollama (will use fallbacks)
    print("\n📝 Testing Fallback Messages (no LLM):")
    
    # Create mock client that always fails (to test fallbacks)
    class MockOllamaClient:
        def generate(self, prompt, max_tokens=100):
            return ""  # Always fail to test fallbacks
    
    mock_client = MockOllamaClient()
    
    # Test Crewmate fallbacks
    crewmate_strategy = LLMCommunicationStrategy("Red", "Crewmate", mock_client)
    
    game_state = {
        'living_players': ['Red', 'Blue', 'Green', 'Yellow'],
        'dead_players': ['Pink'],
    }
    
    discussion_context = {
        'recent_deaths': ['Pink'],
        'accusations': [],
        'alibis': []
    }
    
    print("   🔵 Crewmate Messages:")
    for i in range(5):
        messages = crewmate_strategy.generate_messages(game_state, discussion_context)
        if messages:
            msg = messages[0]
            print(f"      {i+1}. [{msg.message_type.value}] '{msg.content}' (len: {len(msg.content)})")
    
    # Test Impostor fallbacks
    impostor_strategy = LLMCommunicationStrategy("Blue", "Impostor", mock_client)
    
    print("\n   🔴 Impostor Messages:")
    for i in range(5):
        messages = impostor_strategy.generate_messages(game_state, discussion_context)
        if messages:
            msg = messages[0]
            print(f"      {i+1}. [{msg.message_type.value}] '{msg.content}' (len: {len(msg.content)})")
    
    # Test response generation
    print("\n💬 Testing Response Generation:")
    
    # Create a message to respond to
    accusation = Message(
        sender="Green",
        message_type=MessageType.ACCUSATION,
        content="red is sus i saw them vent",
        target="Red",
        confidence=0.8
    )
    
    response = crewmate_strategy.respond_to_message(accusation, game_state)
    if response:
        print(f"   Response to accusation: '{response.content}' (len: {len(response.content)})")
    
    # Test with actual Ollama if available
    print("\n🤖 Testing with Real LLM (if available):")
    try:
        real_client = OllamaClient()
        real_strategy = LLMCommunicationStrategy("Yellow", "Crewmate", real_client)
        
        # Test with a simple prompt
        test_prompt = "You are Yellow in Among Us. Say 'i was doing wires' (max 20 chars):"
        response = real_client.generate(test_prompt, max_tokens=20)
        
        if response:
            print(f"   ✅ LLM Response: '{response}' (len: {len(response)})")
            
            # Test full message generation
            messages = real_strategy.generate_messages(game_state, discussion_context)
            if messages:
                msg = messages[0]
                print(f"   ✅ Generated Message: '{msg.content}' (len: {len(msg.content)})")
        else:
            print("   ⚠️  LLM not available, using fallbacks only")
            
    except Exception as e:
        print(f"   ⚠️  LLM not available: {e}")
    
    print("\n✅ LLM Communication Test Complete!")

def test_message_validation():
    """Test message length and content validation"""
    print("\n🔍 Testing Message Validation...")
    
    class TestOllamaClient:
        def __init__(self, responses):
            self.responses = responses
            self.call_count = 0
        
        def generate(self, prompt, max_tokens=100):
            if self.call_count < len(self.responses):
                response = self.responses[self.call_count]
                self.call_count += 1
                return response
            return ""
    
    # Test various response types
    test_responses = [
        "i was doing wires in electrical",  # Good response
        "I was in MedBay all day tending to Blues injuries after their accident and clearly someone else must have done it",  # Too long
        "red sus",  # Short and good
        "Im absolutely baffled by the recent incidents",  # Too verbose
        "doing tasks",  # Simple and good
    ]
    
    test_client = TestOllamaClient(test_responses)
    strategy = LLMCommunicationStrategy("Green", "Crewmate", test_client)
    
    game_state = {'living_players': ['Green', 'Red', 'Blue'], 'dead_players': []}
    discussion_context = {}
    
    print("   Testing response processing:")
    for i in range(len(test_responses)):
        messages = strategy.generate_messages(game_state, discussion_context)
        if messages:
            msg = messages[0]
            original = test_responses[i] if i < len(test_responses) else "fallback"
            print(f"      Original: '{original}'")
            print(f"      Processed: '{msg.content}' (len: {len(msg.content)})")
            print(f"      Valid: {'✅' if len(msg.content) <= 50 and len(msg.content.split()) <= 8 else '❌'}")
            print()
    
    print("✅ Message Validation Test Complete!")

def main():
    print("🚀 Testing Improved Among Us LLM Communication")
    print("=" * 60)
    
    test_llm_communication()
    test_message_validation()
    
    print("\n" + "=" * 60)
    print("🎉 All LLM Communication Tests Complete!")
    print("\n📋 Summary of Improvements:")
    print("✅ 50-character limit enforced")
    print("✅ Among Us-specific vocabulary")
    print("✅ Realistic task and location names")
    print("✅ Fallback messages for reliability")
    print("✅ Role-appropriate responses")
    print("✅ Simple, authentic Among Us chat style")

if __name__ == "__main__":
    main()
