from core.room import Room
from core.task import Task

# === Room Initialization ===
cafeteria = Room("Cafeteria", has_emergency_button=True)
weapons = Room("Weapons")
navigation = Room("Navigation")
o2 = Room("O2", can_fix_oxygen=True)
shields = Room("Shields")
admin = Room("Admin", has_admin_table=True, can_fix_oxygen=True)
storage = Room("Storage")
electrical = Room("Electrical", can_fix_lights=True)
communications = Room("Communications", can_fix_comms=True)
medbay = Room("MedBay", has_vitals=True)
upper_engine = Room("Upper Engine")
lower_engine = Room("Lower Engine")
reactor = Room("Reactor", can_fix_reactor=True)
security = Room("Security", has_security_cameras=True)
hallway = Room("Hallway")  # Used for vent system only, not walkable

# === Map Connections ===
# Left Side
upper_engine.connect(reactor)
upper_engine.connect(security)
upper_engine.connect(medbay)

lower_engine.connect(reactor)
lower_engine.connect(electrical)

electrical.connect(storage)
security.connect(reactor)

# Middle Area
cafeteria.connect(weapons)
cafeteria.connect(medbay)
cafeteria.connect(admin)
cafeteria.connect(storage)

admin.connect(storage)

# Right Side
weapons.connect(o2)
o2.connect(navigation)
navigation.connect(shields)
shields.connect(communications)
communications.connect(storage)

# === Vent Group Helper ===
def connect_vent_group(*rooms):
    for i in range(len(rooms)):
        for j in range(i + 1, len(rooms)):
            rooms[i].add_vent(rooms[j])

# === Vent Systems ===

# System 1: Reactor-North, Upper Engine | Reactor-South, Lower Engine
connect_vent_group(reactor, upper_engine)
connect_vent_group(reactor, lower_engine)

# System 2: Electrical, MedBay, Security
connect_vent_group(electrical, medbay, security)

# System 3: Admin, Cafeteria, Hallway (imaginary location)
connect_vent_group(admin, cafeteria, hallway)

# System 4: Navigation-North, Weapons | Navigation-South, Shields
connect_vent_group(navigation, weapons)
connect_vent_group(navigation, shields)

# === Security Camera Setup ===
# Security cameras can view these rooms
security.add_camera_feed("Cafeteria")
security.add_camera_feed("Admin")
security.add_camera_feed("MedBay")
security.add_camera_feed("Navigation")

# === Room Dictionary for Game Use ===
def get_skeld_rooms():
    return {
        room.name: room
        for room in [
            cafeteria, weapons, navigation, o2, shields, admin, storage,
            electrical, communications, medbay, upper_engine, lower_engine,
            reactor, security, hallway
        ]
    }

def get_skeld_task_templates():
    return [
        # === Common Tasks ===
        Task("Fix Wiring", "common", ["Admin", "Cafeteria", "Electrical", "Navigation", "Storage", "Security"], 2,
             is_common=True, steps_required=3, randomized_order=True),
        Task("Swipe Card", "common", ["Admin"], 2, is_common=True),

        # === Short Tasks ===
        Task("Align Engine Output", "short", ["Upper Engine", "Lower Engine"], 2),
        Task("Calibrate Distributor", "short", ["Electrical"], 2),
        Task("Chart Course", "short", ["Navigation"], 2),
        Task("Clean O2 Filter", "short", ["O2"], 2),
        Task("Clear Asteroids", "short", ["Weapons"], 2, is_visual=True),
        Task("Empty Garbage", "short", ["Cafeteria", "Storage"], 2, is_visual=True),
        Task("Prime Shields", "short", ["Shields"], 2, is_visual=True),
        Task("Stabilize Steering", "short", ["Navigation"], 2),
        Task("Unlock Manifolds", "short", ["Reactor"], 2),

        # === Long Tasks ===
        Task("Fuel Engines", "long", ["Storage", "Upper Engine", "Storage", "Lower Engine"], 2, steps_required=4),
        Task("Inspect Sample", "long", ["MedBay"], 4),
        Task("Start Reactor", "long", ["Reactor"], 4),
        Task("Submit Scan", "long", ["MedBay"], 4, is_visual=True),
        Task("Upload Data", "long", ["Cafeteria", "Communications", "Electrical", "Navigation", "Weapons", "Admin"], 2,
             steps_required=2, randomized_order=True),
        Task("Divert Power", "long", ["Electrical", "Shields", "Navigation", "O2", "Communications", "Security", "Weapons"], 2,
             steps_required=2, randomized_order=True)
    ]

def create_skeld_map():
    return get_skeld_rooms()