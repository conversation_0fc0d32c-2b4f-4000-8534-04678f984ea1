"""
Among Us Color Configuration
All 18 official player colors with hex codes and display information
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class PlayerColor:
    """Represents an Among Us player color"""
    name: str
    hex_code: str
    rgb: Tuple[int, int, int]
    is_dark: bool  # Whether text should be light on this color
    rarity: str    # Common, uncommon, rare (for training variety)

# All 18 official Among Us colors with accurate hex codes
AMONG_US_COLORS = {
    "Red": PlayerColor(
        name="Red",
        hex_code="#C51111",
        rgb=(197, 17, 17),
        is_dark=True,
        rarity="common"
    ),
    "Blue": PlayerColor(
        name="Blue", 
        hex_code="#132ED1",
        rgb=(19, 46, 209),
        is_dark=True,
        rarity="common"
    ),
    "Green": PlayerColor(
        name="Green",
        hex_code="#117F2D",
        rgb=(17, 127, 45),
        is_dark=True,
        rarity="common"
    ),
    "Pink": PlayerColor(
        name="Pink",
        hex_code="#ED54BA",
        rgb=(237, 84, 186),
        is_dark=False,
        rarity="common"
    ),
    "Orange": PlayerColor(
        name="Orange",
        hex_code="#F07613",
        rgb=(240, 118, 19),
        is_dark=False,
        rarity="common"
    ),
    "Yellow": PlayerColor(
        name="Yellow",
        hex_code="#F5F557",
        rgb=(245, 245, 87),
        is_dark=False,
        rarity="common"
    ),
    "Black": PlayerColor(
        name="Black",
        hex_code="#3F474E",
        rgb=(63, 71, 78),
        is_dark=True,
        rarity="common"
    ),
    "White": PlayerColor(
        name="White",
        hex_code="#D6E0F0",
        rgb=(214, 224, 240),
        is_dark=False,
        rarity="common"
    ),
    "Purple": PlayerColor(
        name="Purple",
        hex_code="#6B2FBB",
        rgb=(107, 47, 187),
        is_dark=True,
        rarity="uncommon"
    ),
    "Brown": PlayerColor(
        name="Brown",
        hex_code="#71491E",
        rgb=(113, 73, 30),
        is_dark=True,
        rarity="uncommon"
    ),
    "Cyan": PlayerColor(
        name="Cyan",
        hex_code="#38FEDC",
        rgb=(56, 254, 220),
        is_dark=False,
        rarity="uncommon"
    ),
    "Lime": PlayerColor(
        name="Lime",
        hex_code="#50EF39",
        rgb=(80, 239, 57),
        is_dark=False,
        rarity="uncommon"
    ),
    "Maroon": PlayerColor(
        name="Maroon",
        hex_code="#6A2C3E",
        rgb=(106, 44, 62),
        is_dark=True,
        rarity="rare"
    ),
    "Rose": PlayerColor(
        name="Rose",
        hex_code="#FFBDCE",
        rgb=(255, 189, 206),
        is_dark=False,
        rarity="rare"
    ),
    "Banana": PlayerColor(
        name="Banana",
        hex_code="#FFFF99",
        rgb=(255, 255, 153),
        is_dark=False,
        rarity="rare"
    ),
    "Gray": PlayerColor(
        name="Gray",
        hex_code="#787878",
        rgb=(120, 120, 120),
        is_dark=True,
        rarity="rare"
    ),
    "Tan": PlayerColor(
        name="Tan",
        hex_code="#8C7853",
        rgb=(140, 120, 83),
        is_dark=True,
        rarity="rare"
    ),
    "Coral": PlayerColor(
        name="Coral",
        hex_code="#F07568",
        rgb=(240, 117, 104),
        is_dark=False,
        rarity="rare"
    )
}

def get_all_color_names() -> List[str]:
    """Get list of all color names"""
    return list(AMONG_US_COLORS.keys())

def get_colors_by_rarity(rarity: str) -> List[str]:
    """Get colors filtered by rarity"""
    return [name for name, color in AMONG_US_COLORS.items() if color.rarity == rarity]

def get_common_colors() -> List[str]:
    """Get the 8 most common colors (original Among Us colors)"""
    return get_colors_by_rarity("common")

def get_random_colors(count: int, prefer_common: bool = True) -> List[str]:
    """Get random selection of colors"""
    import random
    
    if prefer_common and count <= 8:
        # Use common colors first for smaller lobbies
        colors = get_common_colors()
        if count <= len(colors):
            return random.sample(colors, count)
    
    # Use all colors for larger lobbies or when not preferring common
    all_colors = get_all_color_names()
    return random.sample(all_colors, min(count, len(all_colors)))

def get_lobby_colors(lobby_size: int, randomize: bool = False) -> List[str]:
    """Get appropriate colors for a lobby of given size"""
    
    if randomize:
        return get_random_colors(lobby_size, prefer_common=True)
    
    # Use colors in order of commonality
    all_colors = get_all_color_names()
    
    # Prioritize common colors, then uncommon, then rare
    common = get_colors_by_rarity("common")
    uncommon = get_colors_by_rarity("uncommon") 
    rare = get_colors_by_rarity("rare")
    
    ordered_colors = common + uncommon + rare
    
    return ordered_colors[:lobby_size]

def get_color_info(color_name: str) -> PlayerColor:
    """Get detailed information about a color"""
    return AMONG_US_COLORS.get(color_name)

def is_valid_color(color_name: str) -> bool:
    """Check if a color name is valid"""
    return color_name in AMONG_US_COLORS

def get_contrasting_colors(avoid_similar: bool = True) -> List[List[str]]:
    """Get color combinations that are visually distinct"""
    
    if not avoid_similar:
        return [get_all_color_names()]
    
    # Group colors that might be confused
    similar_groups = [
        ["Red", "Maroon", "Rose", "Coral"],
        ["Blue", "Cyan", "Purple"],
        ["Green", "Lime"],
        ["Yellow", "Banana"],
        ["Pink", "Rose"],
        ["Brown", "Tan", "Maroon"],
        ["Black", "Gray"],
        ["White", "Rose", "Banana"]
    ]
    
    # Create combinations avoiding similar colors
    distinct_combinations = []
    
    # Small lobby (6 players) - most distinct colors
    distinct_combinations.append(["Red", "Blue", "Green", "Yellow", "Pink", "White"])
    
    # Medium lobby (10 players) - add contrasting colors
    distinct_combinations.append([
        "Red", "Blue", "Green", "Yellow", "Pink", "White", 
        "Purple", "Orange", "Cyan", "Black"
    ])
    
    # Large lobby (15 players) - all colors with good contrast
    distinct_combinations.append([
        "Red", "Blue", "Green", "Yellow", "Pink", "White",
        "Purple", "Orange", "Cyan", "Black", "Brown", "Lime",
        "Gray", "Coral", "Tan"
    ])
    
    return distinct_combinations

# Lobby size recommendations
RECOMMENDED_LOBBY_SIZES = {
    "small": 6,      # Classic Among Us
    "medium": 10,    # Standard public lobbies
    "large": 15,     # Maximum for good gameplay
}

def get_recommended_impostors(lobby_size: int) -> int:
    """Get recommended number of impostors for lobby size"""
    if lobby_size <= 6:
        return 1 if lobby_size <= 4 else 2
    elif lobby_size <= 9:
        return 2
    elif lobby_size <= 12:
        return 3
    else:
        return 3  # Max 3 impostors in Among Us

def validate_lobby_configuration(lobby_size: int, num_impostors: int) -> Tuple[bool, str]:
    """Validate lobby configuration"""
    
    if lobby_size < 4:
        return False, "Minimum 4 players required"
    
    if lobby_size > 15:
        return False, "Maximum 15 players supported"
    
    if num_impostors < 1:
        return False, "At least 1 impostor required"
    
    if num_impostors > 3:
        return False, "Maximum 3 impostors allowed"
    
    if num_impostors >= lobby_size:
        return False, "Impostors must be less than total players"
    
    # Check impostor ratio (should be reasonable)
    ratio = num_impostors / lobby_size
    if ratio > 0.4:  # More than 40% impostors is unbalanced
        return False, f"Too many impostors for lobby size (ratio: {ratio:.1%})"
    
    return True, "Configuration valid"

# Export commonly used functions
__all__ = [
    'AMONG_US_COLORS',
    'PlayerColor',
    'get_all_color_names',
    'get_common_colors', 
    'get_random_colors',
    'get_lobby_colors',
    'get_color_info',
    'is_valid_color',
    'get_contrasting_colors',
    'get_recommended_impostors',
    'validate_lobby_configuration',
    'RECOMMENDED_LOBBY_SIZES'
]
