#!/usr/bin/env python3
"""
Enhanced Among Us simulation with LLM-powered communication.
"""

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.scripted_agent import ScriptedAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS
import requests

def check_ollama_availability():
    """Check if Ollama is available and return available models"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            return True, [model["name"] for model in models]
        return False, []
    except requests.exceptions.RequestException:
        return False, []

def main():
    print("🚀 Enhanced Among Us Simulation with LLM Communication")
    print("=" * 60)
    
    # Check Ollama availability
    ollama_available, models = check_ollama_availability()
    
    if ollama_available:
        print(f"✅ Ollama detected with {len(models)} models")
        if models:
            print(f"   Available models: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")
        
        # Ask user if they want to use LLM
        try:
            use_llm = input("\n🤖 Use LLM for communication? (y/n, default=y): ").lower().strip()
            use_llm = use_llm != 'n'
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return
    else:
        print("❌ Ollama not detected - using scripted communication")
        print("   To use LLM communication:")
        print("   1. Install Ollama: https://ollama.ai/")
        print("   2. Start Ollama: ollama serve")
        print("   3. Install a model: ollama pull llama3.2")
        use_llm = False
    
    # === ENHANCED SETTINGS ===
    settings = DEFAULT_SETTINGS.copy()
    settings["enable_llm_communication"] = use_llm
    settings["enable_sabotages"] = True
    settings["enable_security_systems"] = True
    settings["enable_advanced_roles"] = False
    
    # LLM settings
    if use_llm:
        settings["ollama_model"] = "llama3.2"  # Change to your preferred model
        settings["llm_max_tokens"] = 80
        settings["llm_fallback_to_scripted"] = True
    
    num_players = 6
    num_impostors = 2
    colors = ["Red", "Blue", "Green", "Yellow", "Pink", "Cyan"][:num_players]
    
    # === SETUP GAME WITH DIVERSE AGENTS ===
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    players = []
    
    # Assign roles
    import random
    impostor_colors = random.sample(colors, num_impostors)
    
    # Create diverse agent strategies
    impostor_strategies = ["aggressive_impostor", "sneaky_impostor"]
    crewmate_strategies = ["detective_crewmate", "task_focused_crewmate", "balanced"]
    
    impostor_strategy_idx = 0
    crewmate_strategy_idx = 0

    for color in colors:
        if color in impostor_colors:
            role = IMPOSTOR
            strategy = impostor_strategies[impostor_strategy_idx % len(impostor_strategies)]
            impostor_strategy_idx += 1
        else:
            role = CREWMATE
            strategy = crewmate_strategies[crewmate_strategy_idx % len(crewmate_strategies)]
            crewmate_strategy_idx += 1
            
        agent = ScriptedAgent(color, strategy=strategy)
        player = Player(color, role, cafeteria, agent)
        players.append(player)

    game = SimulatedGame(players, settings)

    # === RUN ENHANCED GAME ===
    print(f"\n🎮 Game Configuration:")
    print(f"   Players: {num_players}, Impostors: {num_impostors}")
    print(f"   Communication: {'🤖 LLM-Powered' if use_llm else '📝 Scripted'}")
    print(f"   Sabotages: {'✅' if settings['enable_sabotages'] else '❌'}")
    print(f"   Security Systems: {'✅' if settings['enable_security_systems'] else '❌'}")
    
    print("\n" + "="*60)
    
    max_ticks = 50  # Prevent very long games
    tick = 0
    
    try:
        while not game.is_game_over() and tick < max_ticks:
            tick += 1
            print(f"\n=== TICK {tick} ===")
            
            # Show condensed game state
            print(f"Tasks: {game.completed_tasks}/{game.total_tasks} | "
                  f"Living: {len(game.living_players)} | "
                  f"Sabotages: {len(game.sabotage_manager.get_active_sabotages())}")
            
            game.run_tick()
            
            # Show player states every 10 ticks or when important events happen
            if tick % 10 == 0 or game.is_game_over():
                print("\n--- Player States ---")
                for p in players:
                    status = "👻" if not p.alive else "🟢" if p.role.name == "Crewmate" else "🔴"
                    tasks_done = len([t for t in p.tasks if t.is_complete(p.color)]) if p.tasks else 0
                    total_tasks = len(p.tasks) if p.tasks else 0
                    comm_type = "🤖" if use_llm else "📝"
                    print(f"{status} {p.color} ({p.agent.strategy}) {comm_type} - {p.current_room.name} - Tasks: {tasks_done}/{total_tasks}")
    
    except KeyboardInterrupt:
        print("\n\n⏸️  Game interrupted by user")
    
    # === GAME RESULTS ===
    print("\n" + "="*60)
    if game.is_game_over():
        print(f"🎉 Game Over! Winner: {game.get_winner()}")
        print(f"Game lasted {tick} ticks")
        print(f"Final task completion: {game.completed_tasks}/{game.total_tasks}")
        
        if use_llm:
            print(f"\n🤖 LLM Communication Statistics:")
            print(f"   Communication type: Natural language powered by Ollama")
            print(f"   Model used: {settings['ollama_model']}")
            print(f"   The discussions you saw were generated by AI!")
    else:
        print(f"⏰ Game reached maximum ticks ({max_ticks})")
    
    print(f"\n--- Final Player States ---")
    for p in players:
        status = "👻" if not p.alive else "🟢" if p.role.name == "Crewmate" else "🔴"
        tasks_done = len([t for t in p.tasks if t.is_complete(p.color)]) if p.tasks else 0
        total_tasks = len(p.tasks) if p.tasks else 0
        comm_type = "🤖" if use_llm else "📝"
        print(f"{status} {p.color} ({p.agent.strategy}) {comm_type} - {p.current_room.name} - Tasks: {tasks_done}/{total_tasks}")
    
    if use_llm:
        print(f"\n🌟 Thanks for trying LLM-powered Among Us simulation!")
        print(f"   The natural language discussions make the game much more realistic!")
    else:
        print(f"\n💡 Try running with Ollama for even more realistic communication!")

if __name__ == "__main__":
    main()
