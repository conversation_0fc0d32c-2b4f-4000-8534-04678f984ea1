from core.sim_env import SimulatedGame
from config.skeld import create_skeld_map
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from agents.scripted_agent import ScriptedAgent
import random
from config.settings import DEFAULT_SETTINGS

def main():
    # === ENHANCED SETTINGS ===
    settings = DEFAULT_SETTINGS.copy()
    settings["enable_sabotages"] = True
    settings["enable_security_systems"] = True
    settings["enable_advanced_roles"] = False  # Start with basic roles

    num_players = 8
    num_impostors = 2
    colors = ["Red", "Blue", "Green", "Yellow", "Pink", "<PERSON>an", "<PERSON>", "Purple"][:num_players]

    # === SETUP GAME WITH DIVERSE AGENTS ===
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    players = []

    # Assign roles
    impostor_colors = random.sample(colors, num_impostors)

    # Create diverse agent strategies
    impostor_strategies = ["aggressive_impostor", "sneaky_impostor"]
    crewmate_strategies = ["detective_crewmate", "task_focused_crewmate", "balanced"]

    impostor_strategy_idx = 0
    crewmate_strategy_idx = 0

    for color in colors:
        if color in impostor_colors:
            role = IMPOSTOR
            strategy = impostor_strategies[impostor_strategy_idx % len(impostor_strategies)]
            impostor_strategy_idx += 1
        else:
            role = CREWMATE
            strategy = crewmate_strategies[crewmate_strategy_idx % len(crewmate_strategies)]
            crewmate_strategy_idx += 1

        agent = ScriptedAgent(color, strategy=strategy)
        player = Player(color, role, cafeteria, agent)
        players.append(player)

    game = SimulatedGame(players, settings)

    # === RUN ENHANCED GAME ===
    print("🚀 Starting Enhanced Among Us Simulation")
    print(f"Players: {num_players}, Impostors: {num_impostors}")
    print(f"Sabotages: {settings['enable_sabotages']}, Security: {settings['enable_security_systems']}")
    print("\n" + "="*50)

    max_ticks = 100  # Prevent infinite games
    tick = 0

    while not game.is_game_over() and tick < max_ticks:
        tick += 1
        print(f"\n=== TICK {tick} ===")

        # Show game state
        print(f"Tasks: {game.completed_tasks}/{game.total_tasks}")
        print(f"Living: {len(game.living_players)} players")
        print(f"Active sabotages: {game.sabotage_manager.get_active_sabotages()}")

        game.run_tick()

        # Show player states every 5 ticks or when important events happen
        if tick % 5 == 0 or game.is_game_over():
            print("\n--- Player States ---")
            for p in players:
                status = "👻" if not p.alive else "🟢" if p.role.name == "Crewmate" else "🔴"
                tasks_done = len([t for t in p.tasks if t.is_complete(p.color)]) if p.tasks else 0
                total_tasks = len(p.tasks) if p.tasks else 0
                print(f"{status} {p.color} ({p.agent.strategy}) - {p.current_room.name} - Tasks: {tasks_done}/{total_tasks}")

    # === GAME RESULTS ===
    print("\n" + "="*50)
    if game.is_game_over():
        print(f"🎉 Game Over! Winner: {game.get_winner()}")
        print(f"Game lasted {tick} ticks")
        print(f"Final task completion: {game.completed_tasks}/{game.total_tasks}")
    else:
        print(f"⏰ Game reached maximum ticks ({max_ticks})")

    print("\n--- Final Player States ---")
    for p in players:
        status = "👻" if not p.alive else "🟢" if p.role.name == "Crewmate" else "🔴"
        tasks_done = len([t for t in p.tasks if t.is_complete(p.color)]) if p.tasks else 0
        total_tasks = len(p.tasks) if p.tasks else 0
        print(f"{status} {p.color} ({p.agent.strategy}) - {p.current_room.name} - Tasks: {tasks_done}/{total_tasks}")

if __name__ == "__main__":
    main()