from typing import List, Optional, Dict
from core.role import Role
from core.task import Task
from core.room import Room

class Player:
    def __init__(self, color: str, role: Role, starting_room: Room, agent=None):
        self.color = color
        self.role = role
        self.current_room = starting_room
        self.agent = agent
        self.alive = True

        self.tasks: List[Task] = []
        self.fake_tasks: List[Task] = []
        self.emergency_used = False
        self.kill_cooldown = 0  # turns until next kill (if impostor)
        self.meeting_cooldown = 0 # optional future use (if needed)
        self.task_progress = 0  # Number of tasks completed

        # Advanced role abilities
        self.shapeshift_cooldown = 0
        self.shapeshift_duration = 0
        self.shapeshifted_as: Optional[str] = None  # Color of player being impersonated
        self.protect_cooldown = 0
        self.protected_player: Optional[str] = None
        self.track_cooldown = 0
        self.tracked_player: Optional[str] = None

        # Suspicion and social deduction
        self.suspicion_levels: Dict[str, float] = {}  # How suspicious this player finds others
        self.seen_with: Dict[str, int] = {}  # Last time seen with each player
        self.last_seen_alive: Dict[str, int] = {}  # Last time each player was seen alive
        self.alibi_tasks: List[str] = []  # Tasks this player was seen doing

        # Vision and information
        self.vision_range = 1.0 * role.vision_modifier
        self.can_see_in_dark = role.name == "Impostor"

    def can_vent(self) -> bool:
        return self.role.can_vent and self.alive

    def can_kill(self) -> bool:
        return self.role.name == "Impostor" and self.kill_cooldown == 0 and self.alive

    def move_to(self, target_room: Room) -> bool:
        if target_room in self.current_room.connected_rooms:
            self.current_room = target_room
            return True
        return False

    def vent_to(self, target_room: Room) -> bool:
        if self.can_vent() and target_room in self.current_room.vents_to:
            self.current_room = target_room
            return True
        return False
    
    def get_current_task(self):
        """Return the next incomplete task available in this room, if any."""
        for task in self.tasks:
            current_location = task.get_current_location(self.color)
            if (current_location == self.current_room.name and
                not task.is_complete(self.color)):
                return task
        return None

    def can_call_meeting(self) -> bool:
        return not self.emergency_used and self.alive

    def can_shapeshift(self) -> bool:
        return ("shapeshift" in self.role.special_abilities and
                self.shapeshift_cooldown <= 0 and self.alive)

    def can_protect(self) -> bool:
        return ("protect" in self.role.special_abilities and
                self.protect_cooldown <= 0 and not self.alive)  # Guardian Angel works when dead

    def can_track(self) -> bool:
        return ("track_player" in self.role.special_abilities and
                self.track_cooldown <= 0 and self.alive)

    def shapeshift_into(self, target_color: str, duration: int = 10):
        """Shapeshift into another player"""
        if self.can_shapeshift():
            self.shapeshifted_as = target_color
            self.shapeshift_duration = duration
            self.shapeshift_cooldown = 30

    def get_apparent_color(self) -> str:
        """Get the color others see (accounting for shapeshifting)"""
        if self.shapeshifted_as and self.shapeshift_duration > 0:
            return self.shapeshifted_as
        return self.color

    def update_suspicion(self, target_color: str, change: float):
        """Update suspicion level for another player"""
        if target_color not in self.suspicion_levels:
            self.suspicion_levels[target_color] = 0.0
        self.suspicion_levels[target_color] = max(0.0, min(1.0,
            self.suspicion_levels[target_color] + change))

    def update_cooldowns(self):
        """Update all cooldowns"""
        if self.kill_cooldown > 0:
            self.kill_cooldown -= 1
        if self.shapeshift_cooldown > 0:
            self.shapeshift_cooldown -= 1
        if self.shapeshift_duration > 0:
            self.shapeshift_duration -= 1
            if self.shapeshift_duration <= 0:
                self.shapeshifted_as = None
        if self.protect_cooldown > 0:
            self.protect_cooldown -= 1
        if self.track_cooldown > 0:
            self.track_cooldown -= 1

    def __repr__(self):
        role_tag = "🟢" if self.role.name == "Crewmate" else "🔴"
        apparent_color = self.get_apparent_color()
        status = 'Alive' if self.alive else 'Dead'
        if apparent_color != self.color:
            return f"{role_tag} {self.color} (as {apparent_color}) - {self.current_room.name} - {status}"
        return f"{role_tag} {self.color} - {self.current_room.name} - {status}"