#!/usr/bin/env python3
"""
Train RL agents for Among Us simulation.
"""

import torch
import argparse
from training.rl_trainer import AmongUsTrainer

def get_training_config():
    """Get training configuration"""
    return {
        # Training parameters
        "num_episodes": 1000,
        "max_steps_per_episode": 100,
        "eval_frequency": 50,
        "save_frequency": 100,
        
        # Game parameters
        "num_players": 6,
        "num_impostors": 2,
        "rl_agent_colors": ["Red", "Blue"],  # Which players are RL agents
        
        # RL parameters
        "learning_rate": 1e-4,
        "epsilon": 0.3,  # Start with higher exploration
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        
        # Game settings
        "game_settings": {
            "enable_llm_communication": False,  # Start without LLM for faster training
            "enable_sabotages": True,
            "enable_security_systems": True,
            "enable_advanced_roles": False,
        },
        
        # Save settings
        "save_dir": "models/among_us_rl",
    }

def main():
    parser = argparse.ArgumentParser(description="Train Among Us RL agents")
    parser.add_argument("--episodes", type=int, default=1000, help="Number of training episodes")
    parser.add_argument("--device", type=str, default="auto", help="Device to use (cpu/cuda/auto)")
    parser.add_argument("--lr", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--epsilon", type=float, default=0.3, help="Initial epsilon for exploration")
    parser.add_argument("--players", type=int, default=6, help="Number of players")
    parser.add_argument("--impostors", type=int, default=2, help="Number of impostors")
    parser.add_argument("--llm", action="store_true", help="Enable LLM communication")
    parser.add_argument("--save-dir", type=str, default="models/among_us_rl", help="Save directory")
    
    args = parser.parse_args()
    
    # Create config
    config = get_training_config()
    
    # Update config with command line arguments
    config["num_episodes"] = args.episodes
    config["learning_rate"] = args.lr
    config["epsilon"] = args.epsilon
    config["num_players"] = args.players
    config["num_impostors"] = args.impostors
    config["save_dir"] = args.save_dir
    config["game_settings"]["enable_llm_communication"] = args.llm
    
    if args.device == "auto":
        config["device"] = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        config["device"] = args.device
    
    print("🎮 Among Us RL Training Configuration:")
    print(f"   Episodes: {config['num_episodes']}")
    print(f"   Device: {config['device']}")
    print(f"   Learning Rate: {config['learning_rate']}")
    print(f"   Players: {config['num_players']} ({config['num_impostors']} impostors)")
    print(f"   RL Agents: {config['rl_agent_colors']}")
    print(f"   LLM Communication: {config['game_settings']['enable_llm_communication']}")
    print(f"   Save Directory: {config['save_dir']}")
    
    # Check PyTorch installation
    print(f"\n🔧 PyTorch Info:")
    print(f"   Version: {torch.__version__}")
    print(f"   CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   CUDA Device: {torch.cuda.get_device_name()}")
    
    # Create and run trainer
    trainer = AmongUsTrainer(config)
    
    try:
        trainer.train()
    except KeyboardInterrupt:
        print("\n⏸️  Training interrupted by user")
        trainer.save_training_plots()
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
