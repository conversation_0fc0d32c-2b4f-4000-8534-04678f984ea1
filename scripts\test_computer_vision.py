#!/usr/bin/env python3
"""
Computer Vision System Test Script.

Tests all components of the computer vision system to ensure
they work correctly before training on the real game.
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from soma.computer_vision.screen_capture import Screen<PERSON>apture
from soma.computer_vision.game_detector import GameStateDetector
from soma.computer_vision.player_detector import PlayerDetector, PlayerColor
from soma.computer_vision.input_controller import InputController, InputAction, InputType
from soma.computer_vision.cv_agent_bridge import CVAgentBridge
from soma.agents.ultimate_rl_agent import UltimateAmongUsAgent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_screen_capture():
    """Test screen capture functionality."""
    print("🖥️  Testing Screen Capture...")
    
    try:
        capture = ScreenCapture(target_fps=5)
        
        # Test window detection
        if capture.find_among_us_window():
            print("✅ Among Us window found")
            print(f"   Window region: {capture.window_region}")
            print(f"   Scale factor: {capture.scale_factor}")
        else:
            print("❌ Among Us window not found")
            print("   Make sure Among Us is running and visible")
            return False
        
        # Test frame capture
        print("   Testing frame capture...")
        frame = capture.capture_frame()
        
        if frame is not None:
            print(f"✅ Frame captured successfully: {frame.shape}")
        else:
            print("❌ Failed to capture frame")
            return False
        
        # Test multiple captures
        print("   Testing continuous capture...")
        successful_captures = 0
        for i in range(5):
            frame = capture.capture_frame()
            if frame is not None:
                successful_captures += 1
            time.sleep(0.2)
        
        print(f"   Captured {successful_captures}/5 frames")
        
        # Show stats
        stats = capture.get_capture_stats()
        print(f"   Capture stats: {stats}")
        
        capture.cleanup()
        return successful_captures >= 3
        
    except Exception as e:
        print(f"❌ Screen capture test failed: {e}")
        return False


def test_game_detection():
    """Test game state detection."""
    print("\n🎮 Testing Game State Detection...")
    
    try:
        capture = ScreenCapture(target_fps=5)
        detector = GameStateDetector()
        
        if not capture.find_among_us_window():
            print("❌ Cannot test without Among Us window")
            return False
        
        print("   Analyzing game states...")
        detections = []
        
        for i in range(5):
            frame = capture.capture_frame()
            if frame is not None:
                state_info = detector.detect_game_state(frame)
                detections.append(state_info)
                print(f"   Frame {i+1}: {state_info.state.value} (confidence: {state_info.confidence:.2f})")
            time.sleep(1.0)
        
        if detections:
            print("✅ Game state detection working")
            
            # Check for state stability
            if detector.is_state_stable(required_duration=2.0):
                print("✅ State detection is stable")
            else:
                print("⚠️  State detection may be unstable")
        else:
            print("❌ No game states detected")
            return False
        
        capture.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Game detection test failed: {e}")
        return False


def test_player_detection():
    """Test player detection."""
    print("\n👥 Testing Player Detection...")
    
    try:
        capture = ScreenCapture(target_fps=5)
        detector = PlayerDetector()
        
        if not capture.find_among_us_window():
            print("❌ Cannot test without Among Us window")
            return False
        
        print("   Detecting players...")
        total_players = 0
        
        for i in range(3):
            frame = capture.capture_frame()
            if frame is not None:
                players = detector.detect_players(frame)
                ui_elements = detector.detect_ui_elements(frame)
                
                print(f"   Frame {i+1}: {len(players)} players, {len(ui_elements)} UI elements")
                
                for player in players:
                    print(f"     - {player.color.value} at {player.position} (confidence: {player.confidence:.2f})")
                
                total_players += len(players)
            
            time.sleep(1.0)
        
        if total_players > 0:
            print("✅ Player detection working")
            
            # Show detection stats
            stats = detector.get_detection_stats()
            print(f"   Detection stats: {stats}")
        else:
            print("⚠️  No players detected (may be normal if in lobby)")
        
        capture.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Player detection test failed: {e}")
        return False


def test_input_controller():
    """Test input controller (safe mode)."""
    print("\n🖱️  Testing Input Controller...")
    
    try:
        controller = InputController(safety_enabled=True)
        
        # Test initialization
        controller.start()
        print("✅ Input controller started")
        
        # Test safe operations
        print("   Testing safe operations...")
        
        # Get current mouse position
        pos = controller.get_mouse_position()
        print(f"   Current mouse position: {pos}")
        
        # Test queue operations
        print("   Testing action queue...")
        
        # Queue a safe move action (small movement)
        controller.move_to((pos[0] + 10, pos[1] + 10), duration=0.5)
        
        # Wait for action to process
        time.sleep(1.0)
        
        # Check queue status
        print(f"   Queue size: {controller.get_queue_size()}")
        print(f"   Queue empty: {controller.is_queue_empty()}")
        
        # Get stats
        stats = controller.get_stats()
        print(f"   Controller stats: {stats}")
        
        controller.stop()
        print("✅ Input controller test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Input controller test failed: {e}")
        return False


def test_cv_agent_bridge():
    """Test CV-Agent bridge integration."""
    print("\n🤖 Testing CV-Agent Bridge...")
    
    try:
        # Create a test agent
        agent = UltimateAmongUsAgent(
            player_color="red",
            role_name="Crewmate",
            device="cpu"
        )
        
        # Create bridge
        bridge = CVAgentBridge(agent, PlayerColor.RED)
        
        print("   Testing bridge initialization...")
        if bridge.initialize():
            print("✅ CV-Agent bridge initialized")
        else:
            print("❌ Bridge initialization failed")
            return False
        
        print("   Testing observation capture...")
        cv_obs = bridge.get_observation()
        
        if cv_obs:
            print("✅ Observation captured")
            print(f"   Game state: {cv_obs.game_state.value}")
            print(f"   Players detected: {len(cv_obs.players)}")
            print(f"   UI elements: {len(cv_obs.ui_elements)}")
            print(f"   Own position: {cv_obs.own_position}")
        else:
            print("❌ Failed to capture observation")
            return False
        
        print("   Testing observation conversion...")
        sim_obs = bridge.convert_cv_to_sim_observation(cv_obs)
        
        if sim_obs:
            print("✅ Observation converted to simulation format")
            print(f"   Converted keys: {list(sim_obs.keys())}")
        else:
            print("❌ Failed to convert observation")
            return False
        
        # Get performance stats
        stats = bridge.get_performance_stats()
        print(f"   Bridge stats: {stats}")
        
        bridge.cleanup()
        print("✅ CV-Agent bridge test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ CV-Agent bridge test failed: {e}")
        return False


def test_system_integration():
    """Test full system integration."""
    print("\n🔧 Testing System Integration...")
    
    try:
        print("   Testing component interactions...")
        
        # Test that all components can work together
        capture = ScreenCapture(target_fps=3)
        game_detector = GameStateDetector()
        player_detector = PlayerDetector()
        input_controller = InputController(safety_enabled=True)
        
        if not capture.find_among_us_window():
            print("❌ Cannot test integration without Among Us window")
            return False
        
        input_controller.start()
        
        # Run integrated test
        for i in range(3):
            print(f"   Integration test {i+1}/3...")
            
            # Capture frame
            frame = capture.capture_frame()
            if frame is None:
                continue
            
            # Detect game state
            game_state = game_detector.detect_game_state(frame)
            
            # Detect players
            players = player_detector.detect_players(frame)
            
            print(f"     State: {game_state.state.value}, Players: {len(players)}")
            
            time.sleep(1.0)
        
        # Cleanup
        input_controller.stop()
        capture.cleanup()
        
        print("✅ System integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ System integration test failed: {e}")
        return False


def main():
    """Run all computer vision tests."""
    print("🧪 Among Us Computer Vision System Tests")
    print("=" * 50)
    
    # Check if Among Us is running
    print("📋 Pre-flight checks:")
    print("   - Make sure Among Us is running and visible")
    print("   - Ensure the game window is not minimized")
    print("   - Close any overlapping windows")
    print()
    
    input("Press Enter when ready to start tests...")
    print()
    
    # Run tests
    tests = [
        ("Screen Capture", test_screen_capture),
        ("Game Detection", test_game_detection),
        ("Player Detection", test_player_detection),
        ("Input Controller", test_input_controller),
        ("CV-Agent Bridge", test_cv_agent_bridge),
        ("System Integration", test_system_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Show summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Your computer vision system is ready for training.")
        print("\nNext steps:")
        print("1. Run: python scripts/train_computer_vision.py --calibrate-only")
        print("2. Start with: python scripts/train_computer_vision.py --mode observation_only")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above before training.")
        print("\nCommon solutions:")
        print("- Make sure Among Us is running and visible")
        print("- Check that all dependencies are installed")
        print("- Try running tests individually to isolate issues")


if __name__ == "__main__":
    main()
