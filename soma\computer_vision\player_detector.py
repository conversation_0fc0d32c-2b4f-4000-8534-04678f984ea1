"""
Player and UI element detection for Among Us computer vision.

Identifies players, their colors, positions, and various UI elements
from game screenshots for AI decision making.
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class PlayerColor(Enum):
    """Among Us player colors."""
    RED = "red"
    BLUE = "blue"
    GREEN = "green"
    PINK = "pink"
    ORANGE = "orange"
    YELLOW = "yellow"
    BLACK = "black"
    WHITE = "white"
    PURPLE = "purple"
    BROWN = "brown"
    CYAN = "cyan"
    LIME = "lime"
    MAROON = "maroon"
    ROSE = "rose"
    BANANA = "banana"
    GRAY = "gray"
    TAN = "tan"
    CORAL = "coral"


@dataclass
class PlayerInfo:
    """Information about a detected player."""
    color: PlayerColor
    position: Tuple[int, int]
    confidence: float
    size: int
    is_dead: bool = False
    is_moving: bool = False
    has_hat: bool = False
    has_pet: bool = False


@dataclass
class UIElement:
    """Information about a detected UI element."""
    element_type: str
    position: Tuple[int, int]
    size: Tuple[int, int]
    confidence: float
    clickable: bool = False
    text: Optional[str] = None


class PlayerDetector:
    """Detects players and UI elements in Among Us screenshots."""
    
    def __init__(self):
        """Initialize player detector."""
        self.color_ranges = self._initialize_color_ranges()
        self.ui_templates = {}
        self.detection_history = []
        
        # Detection parameters
        self.min_player_area = 200
        self.max_player_area = 5000
        self.confidence_threshold = 0.6
        
        # UI element detection parameters
        self.ui_elements = {
            'use_button': {
                'color_range': [(200, 200, 200), (255, 255, 255)],
                'size_range': (50, 200),
                'shape': 'rectangular'
            },
            'emergency_button': {
                'color_range': [(0, 0, 150), (50, 50, 255)],
                'size_range': (500, 5000),
                'shape': 'circular'
            },
            'vent': {
                'color_range': [(50, 50, 50), (100, 100, 100)],
                'size_range': (300, 2000),
                'shape': 'irregular'
            },
            'task_arrow': {
                'color_range': [(200, 200, 0), (255, 255, 100)],
                'size_range': (100, 1000),
                'shape': 'triangular'
            }
        }
    
    def _initialize_color_ranges(self) -> Dict[PlayerColor, Tuple[np.ndarray, np.ndarray]]:
        """Initialize HSV color ranges for each player color."""
        return {
            PlayerColor.RED: (np.array([0, 120, 120]), np.array([10, 255, 255])),
            PlayerColor.BLUE: (np.array([100, 120, 120]), np.array([130, 255, 255])),
            PlayerColor.GREEN: (np.array([40, 120, 120]), np.array([80, 255, 255])),
            PlayerColor.PINK: (np.array([160, 120, 120]), np.array([180, 255, 255])),
            PlayerColor.ORANGE: (np.array([10, 120, 120]), np.array([20, 255, 255])),
            PlayerColor.YELLOW: (np.array([20, 120, 120]), np.array([30, 255, 255])),
            PlayerColor.BLACK: (np.array([0, 0, 0]), np.array([180, 255, 50])),
            PlayerColor.WHITE: (np.array([0, 0, 200]), np.array([180, 30, 255])),
            PlayerColor.PURPLE: (np.array([130, 120, 120]), np.array([160, 255, 255])),
            PlayerColor.BROWN: (np.array([10, 100, 100]), np.array([20, 255, 200])),
            PlayerColor.CYAN: (np.array([80, 120, 120]), np.array([100, 255, 255])),
            PlayerColor.LIME: (np.array([60, 120, 120]), np.array([80, 255, 255])),
            PlayerColor.MAROON: (np.array([0, 120, 80]), np.array([10, 255, 150])),
            PlayerColor.ROSE: (np.array([170, 100, 150]), np.array([180, 255, 255])),
            PlayerColor.BANANA: (np.array([25, 120, 180]), np.array([35, 255, 255])),
            PlayerColor.GRAY: (np.array([0, 0, 80]), np.array([180, 50, 150])),
            PlayerColor.TAN: (np.array([15, 80, 120]), np.array([25, 200, 200])),
            PlayerColor.CORAL: (np.array([5, 120, 150]), np.array([15, 255, 255])),
        }
    
    def detect_players(self, frame: np.ndarray) -> List[PlayerInfo]:
        """
        Detect all players in the frame.
        
        Args:
            frame: Input frame (BGR format)
            
        Returns:
            List[PlayerInfo]: List of detected players
        """
        try:
            players = []
            hsv_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Detect each player color
            for color, (lower, upper) in self.color_ranges.items():
                color_players = self._detect_color_players(frame, hsv_frame, color, lower, upper)
                players.extend(color_players)
            
            # Filter and refine detections
            players = self._filter_detections(players)
            players = self._refine_detections(frame, players)
            
            # Update detection history
            self.detection_history.append(players)
            if len(self.detection_history) > 30:  # Keep last 30 frames
                self.detection_history.pop(0)
            
            return players
            
        except Exception as e:
            logger.error(f"Error detecting players: {e}")
            return []
    
    def _detect_color_players(self, frame: np.ndarray, hsv_frame: np.ndarray, 
                            color: PlayerColor, lower: np.ndarray, upper: np.ndarray) -> List[PlayerInfo]:
        """Detect players of a specific color."""
        try:
            players = []
            
            # Create color mask
            mask = cv2.inRange(hsv_frame, lower, upper)
            
            # Morphological operations to clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                
                # Filter by size
                if self.min_player_area <= area <= self.max_player_area:
                    # Calculate center position
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        # Calculate confidence based on shape and size
                        confidence = self._calculate_player_confidence(contour, area)
                        
                        if confidence >= self.confidence_threshold:
                            # Check if player is dead (simplified detection)
                            is_dead = self._is_player_dead(frame, (cx, cy), contour)
                            
                            player = PlayerInfo(
                                color=color,
                                position=(cx, cy),
                                confidence=confidence,
                                size=int(area),
                                is_dead=is_dead
                            )
                            
                            players.append(player)
            
            return players
            
        except Exception as e:
            logger.error(f"Error detecting {color} players: {e}")
            return []
    
    def _calculate_player_confidence(self, contour: np.ndarray, area: int) -> float:
        """Calculate confidence score for player detection."""
        try:
            confidence = 0.0
            
            # Size-based confidence
            ideal_area = 1000  # Ideal player size
            size_confidence = 1.0 - abs(area - ideal_area) / ideal_area
            confidence += max(0, size_confidence) * 0.4
            
            # Shape-based confidence (players are roughly circular/oval)
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                shape_confidence = min(circularity * 2, 1.0)  # Scale circularity
                confidence += shape_confidence * 0.4
            
            # Aspect ratio confidence
            rect = cv2.minAreaRect(contour)
            width, height = rect[1]
            if height > 0:
                aspect_ratio = width / height
                # Players should have aspect ratio close to 1 (roughly square)
                aspect_confidence = 1.0 - abs(aspect_ratio - 1.0)
                confidence += max(0, aspect_confidence) * 0.2
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating player confidence: {e}")
            return 0.0
    
    def _is_player_dead(self, frame: np.ndarray, position: Tuple[int, int], contour: np.ndarray) -> bool:
        """Check if player appears to be dead (simplified detection)."""
        try:
            # Dead players are typically darker and may have different shape
            x, y = position
            
            # Extract region around player
            margin = 20
            y1, y2 = max(0, y - margin), min(frame.shape[0], y + margin)
            x1, x2 = max(0, x - margin), min(frame.shape[1], x + margin)
            
            player_region = frame[y1:y2, x1:x2]
            
            if player_region.size == 0:
                return False
            
            # Check average brightness (dead players are typically darker)
            gray_region = cv2.cvtColor(player_region, cv2.COLOR_BGR2GRAY)
            avg_brightness = np.mean(gray_region)
            
            # Dead players are typically darker
            if avg_brightness < 80:
                return True
            
            # Check for bone/skeleton patterns (more advanced detection would be needed)
            # This is a simplified check
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if player is dead: {e}")
            return False
    
    def _filter_detections(self, players: List[PlayerInfo]) -> List[PlayerInfo]:
        """Filter out duplicate and low-confidence detections."""
        try:
            if not players:
                return players
            
            # Sort by confidence
            players.sort(key=lambda p: p.confidence, reverse=True)
            
            # Remove duplicates (players too close to each other)
            filtered_players = []
            min_distance = 50  # Minimum distance between players
            
            for player in players:
                is_duplicate = False
                
                for existing_player in filtered_players:
                    distance = np.sqrt(
                        (player.position[0] - existing_player.position[0]) ** 2 +
                        (player.position[1] - existing_player.position[1]) ** 2
                    )
                    
                    if distance < min_distance:
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    filtered_players.append(player)
            
            return filtered_players
            
        except Exception as e:
            logger.error(f"Error filtering detections: {e}")
            return players
    
    def _refine_detections(self, frame: np.ndarray, players: List[PlayerInfo]) -> List[PlayerInfo]:
        """Refine player detections with additional analysis."""
        try:
            refined_players = []
            
            for player in players:
                # Check for movement (compare with previous frames)
                is_moving = self._detect_movement(player)
                player.is_moving = is_moving
                
                # Check for accessories (hats, pets) - simplified
                has_hat, has_pet = self._detect_accessories(frame, player)
                player.has_hat = has_hat
                player.has_pet = has_pet
                
                refined_players.append(player)
            
            return refined_players
            
        except Exception as e:
            logger.error(f"Error refining detections: {e}")
            return players
    
    def _detect_movement(self, player: PlayerInfo) -> bool:
        """Detect if player is moving based on position history."""
        try:
            if len(self.detection_history) < 3:
                return False
            
            # Look for same color player in previous frames
            movement_threshold = 10  # pixels
            
            for i in range(min(3, len(self.detection_history))):
                prev_frame_players = self.detection_history[-(i+1)]
                
                for prev_player in prev_frame_players:
                    if prev_player.color == player.color:
                        distance = np.sqrt(
                            (player.position[0] - prev_player.position[0]) ** 2 +
                            (player.position[1] - prev_player.position[1]) ** 2
                        )
                        
                        if distance > movement_threshold:
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting movement: {e}")
            return False
    
    def _detect_accessories(self, frame: np.ndarray, player: PlayerInfo) -> Tuple[bool, bool]:
        """Detect if player has hat or pet (simplified detection)."""
        try:
            # This would require more sophisticated detection
            # For now, return False for both
            return False, False
            
        except Exception as e:
            logger.error(f"Error detecting accessories: {e}")
            return False, False

    def detect_ui_elements(self, frame: np.ndarray) -> List[UIElement]:
        """
        Detect UI elements in the frame.

        Args:
            frame: Input frame (BGR format)

        Returns:
            List[UIElement]: List of detected UI elements
        """
        try:
            ui_elements = []

            # Detect each type of UI element
            for element_type, params in self.ui_elements.items():
                elements = self._detect_ui_element_type(frame, element_type, params)
                ui_elements.extend(elements)

            return ui_elements

        except Exception as e:
            logger.error(f"Error detecting UI elements: {e}")
            return []

    def _detect_ui_element_type(self, frame: np.ndarray, element_type: str, params: Dict) -> List[UIElement]:
        """Detect specific type of UI element."""
        try:
            elements = []

            # Convert to appropriate color space
            if element_type in ['use_button', 'emergency_button']:
                # Use HSV for color-based detection
                hsv_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                detection_frame = hsv_frame
            else:
                # Use grayscale for shape-based detection
                detection_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # Create mask based on color range
            if 'color_range' in params:
                lower, upper = params['color_range']
                if element_type in ['use_button', 'emergency_button']:
                    # HSV color detection
                    lower_hsv = np.array([lower[0]//2, lower[1], lower[2]])  # Convert BGR to HSV ranges
                    upper_hsv = np.array([upper[0]//2, upper[1], upper[2]])
                    mask = cv2.inRange(detection_frame, lower_hsv, upper_hsv)
                else:
                    # Grayscale threshold
                    mask = cv2.inRange(detection_frame, lower[0], upper[0])
            else:
                # Default grayscale threshold
                _, mask = cv2.threshold(detection_frame, 127, 255, cv2.THRESH_BINARY)

            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter contours by size and shape
            size_range = params.get('size_range', (100, 10000))
            shape = params.get('shape', 'rectangular')

            for contour in contours:
                area = cv2.contourArea(contour)

                if size_range[0] <= area <= size_range[1]:
                    # Calculate confidence based on shape
                    confidence = self._calculate_ui_confidence(contour, area, shape)

                    if confidence >= self.confidence_threshold:
                        # Get bounding box
                        x, y, w, h = cv2.boundingRect(contour)

                        # Calculate center position
                        center_x = x + w // 2
                        center_y = y + h // 2

                        element = UIElement(
                            element_type=element_type,
                            position=(center_x, center_y),
                            size=(w, h),
                            confidence=confidence,
                            clickable=element_type in ['use_button', 'emergency_button']
                        )

                        elements.append(element)

            return elements

        except Exception as e:
            logger.error(f"Error detecting {element_type}: {e}")
            return []

    def _calculate_ui_confidence(self, contour: np.ndarray, area: int, expected_shape: str) -> float:
        """Calculate confidence score for UI element detection."""
        try:
            confidence = 0.0

            # Size-based confidence
            confidence += 0.3

            # Shape-based confidence
            if expected_shape == 'circular':
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    confidence += min(circularity * 2, 1.0) * 0.5

            elif expected_shape == 'rectangular':
                rect = cv2.minAreaRect(contour)
                box = cv2.boxPoints(rect)
                box_area = cv2.contourArea(box)
                if box_area > 0:
                    rectangularity = area / box_area
                    confidence += rectangularity * 0.5

            else:  # irregular or other shapes
                confidence += 0.4

            # Solidity (filled vs hollow)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            if hull_area > 0:
                solidity = area / hull_area
                confidence += solidity * 0.2

            return min(confidence, 1.0)

        except Exception as e:
            logger.error(f"Error calculating UI confidence: {e}")
            return 0.0

    def get_players_by_color(self, players: List[PlayerInfo], color: PlayerColor) -> List[PlayerInfo]:
        """Get all players of a specific color."""
        return [p for p in players if p.color == color]

    def get_closest_player(self, players: List[PlayerInfo], position: Tuple[int, int]) -> Optional[PlayerInfo]:
        """Get the closest player to a given position."""
        if not players:
            return None

        min_distance = float('inf')
        closest_player = None

        for player in players:
            distance = np.sqrt(
                (player.position[0] - position[0]) ** 2 +
                (player.position[1] - position[1]) ** 2
            )

            if distance < min_distance:
                min_distance = distance
                closest_player = player

        return closest_player

    def get_ui_elements_by_type(self, ui_elements: List[UIElement], element_type: str) -> List[UIElement]:
        """Get all UI elements of a specific type."""
        return [e for e in ui_elements if e.element_type == element_type]

    def is_position_near_player(self, position: Tuple[int, int], players: List[PlayerInfo],
                              threshold: int = 50) -> bool:
        """Check if a position is near any player."""
        for player in players:
            distance = np.sqrt(
                (player.position[0] - position[0]) ** 2 +
                (player.position[1] - position[1]) ** 2
            )
            if distance <= threshold:
                return True
        return False

    def get_detection_stats(self) -> Dict[str, Any]:
        """Get detection statistics."""
        if not self.detection_history:
            return {'total_frames': 0, 'avg_players_per_frame': 0}

        total_frames = len(self.detection_history)
        total_players = sum(len(frame_players) for frame_players in self.detection_history)
        avg_players = total_players / total_frames if total_frames > 0 else 0

        return {
            'total_frames': total_frames,
            'total_players_detected': total_players,
            'avg_players_per_frame': avg_players
        }
