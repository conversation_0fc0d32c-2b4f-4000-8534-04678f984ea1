#!/usr/bin/env python3
"""
SOMA Among Us AI Training System
Main training entry point
"""

import sys
import os
import subprocess

def main():
    """Main training entry point with proper path handling"""

    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Path to the actual training script
    train_script = os.path.join(script_dir, "simple_train.py")

    # Check if the script exists
    if not os.path.exists(train_script):
        print("❌ Training script not found!")
        print(f"   Looking for: {train_script}")
        return 1

    # Pass all arguments to the training script
    args = sys.argv[1:]  # Skip the script name

    try:
        # Run the training script
        result = subprocess.run([sys.executable, train_script] + args)
        return result.returncode
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
