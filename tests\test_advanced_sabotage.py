#!/usr/bin/env python3
"""
Test advanced sabotage mechanics including crisis timers and multi-person fixes.
"""

from core.sim_env import SimulatedGame
from core.role import CREWMATE, IMPOSTOR
from core.player import Player
from core.action import Action
from core.sabotage import <PERSON><PERSON>ageMana<PERSON>, SabotageType
from agents.base_agent import BaseAgent
from config.skeld import create_skeld_map
from config.settings import DEFAULT_SETTINGS

class TestAgent(BaseAgent):
    def __init__(self, player_color: str, test_actions: list = None):
        super().__init__(player_color)
        self.test_actions = test_actions or ["idle"]
        self.action_index = 0
        
    def choose_action(self, observation: dict) -> Action:
        if self.action_index < len(self.test_actions):
            action = self.test_actions[self.action_index]
            self.action_index += 1
            return Action(action)
        return Action("idle")

def test_crisis_timer_impostor_victory():
    """Test that impostors win if crisis sabotages aren't fixed in time"""
    print("🧪 Test 1: Crisis timer impostor victory")
    
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    cafeteria = rooms["Cafeteria"]
    
    # Create players
    impostor_agent = TestAgent("Red", ["sabotage_oxygen"])
    crewmate_agent = TestAgent("Blue", ["idle"])  # Don't fix it
    
    impostor_player = Player("Red", IMPOSTOR, cafeteria, impostor_agent)
    crewmate_player = Player("Blue", CREWMATE, cafeteria, crewmate_agent)
    
    players = [impostor_player, crewmate_player]
    game = SimulatedGame(players, settings)
    
    print("   Activating oxygen sabotage...")
    game.run_tick()  # Should activate oxygen sabotage
    
    active_sabotages = game.sabotage_manager.get_active_sabotages()
    print(f"   Active sabotages: {[s.value for s in active_sabotages]}")
    
    if SabotageType.OXYGEN in active_sabotages:
        crisis_timer = game.sabotage_manager.get_crisis_timer(SabotageType.OXYGEN)
        print(f"   Crisis timer: {crisis_timer} ticks")
        
        # Let timer expire
        for tick in range(35):  # More than 30 tick timer
            if game.is_game_over():
                break
            game._update_cooldowns()
        
        print(f"   Game over: {game.is_game_over()}")
        print(f"   Winner: {game.get_winner()}")
        
        assert game.is_game_over(), "❌ Game should be over after crisis timer expires!"
        assert game.get_winner() == "Impostors", "❌ Impostors should win when crisis timer expires!"
        print("   ✅ Test passed: Crisis timer causes impostor victory")
    else:
        print("   ⚠️  Oxygen sabotage not activated, skipping test")

def test_reactor_multi_person_fix():
    """Test that reactor requires 2 people at the same location"""
    print("\n🧪 Test 2: Reactor multi-person fix")

    sabotage_manager = SabotageManager()

    # Wait for initial cooldown to expire
    for _ in range(15):  # Wait 15 ticks to clear initial cooldown
        sabotage_manager.tick()

    # Activate reactor sabotage
    success = sabotage_manager.activate_sabotage(SabotageType.REACTOR)
    print(f"   Reactor sabotage activated: {success}")
    assert success, "❌ Should be able to activate reactor sabotage!"
    
    # One person tries to fix - should not work
    fixed1 = sabotage_manager.start_fixing_sabotage(SabotageType.REACTOR, "Red", "Reactor")
    print(f"   One person fixing: {fixed1}")
    assert not fixed1, "❌ One person should not be able to fix reactor!"
    
    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   Still active: {len(active_sabotages)} sabotages")
    assert len(active_sabotages) == 1, "❌ Reactor should still be active!"
    
    # Second person joins - should fix it
    fixed2 = sabotage_manager.start_fixing_sabotage(SabotageType.REACTOR, "Blue", "Reactor")
    print(f"   Two people fixing: {fixed2}")
    assert fixed2, "❌ Two people should be able to fix reactor!"
    
    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   After fix: {len(active_sabotages)} sabotages")
    assert len(active_sabotages) == 0, "❌ Reactor should be fixed!"
    
    print("   ✅ Test passed: Reactor requires 2 people to fix")

def test_oxygen_dual_location_fix():
    """Test that oxygen requires PIN codes at both O2 and Admin (per wiki)"""
    print("\n🧪 Test 3: Oxygen dual-location fix (wiki-accurate)")

    sabotage_manager = SabotageManager()

    # Wait for initial cooldown to expire
    for _ in range(15):  # Wait 15 ticks to clear initial cooldown
        sabotage_manager.tick()

    # Activate oxygen sabotage
    success = sabotage_manager.activate_sabotage(SabotageType.OXYGEN)
    print(f"   Oxygen sabotage activated: {success}")
    assert success, "❌ Should be able to activate oxygen sabotage!"

    # Enter PIN at O2 - should make partial progress
    fixed1 = sabotage_manager.fix_sabotage(SabotageType.OXYGEN, "Red", "O2")
    print(f"   PIN entered at O2: {fixed1}")
    assert not fixed1, "❌ Entering PIN at O2 alone should not complete the fix!"

    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   After O2 PIN: {len(active_sabotages)} sabotages still active")
    assert len(active_sabotages) == 1, "❌ Oxygen should still be active after O2 PIN!"

    # Enter PIN at Admin - should complete the fix
    fixed2 = sabotage_manager.fix_sabotage(SabotageType.OXYGEN, "Red", "Admin")
    print(f"   PIN entered at Admin: {fixed2}")

    # Check final state
    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   After both PINs entered: {len(active_sabotages)} sabotages")

    # According to wiki: Oxygen requires PIN codes at both O2 AND Admin (can be same person)
    assert fixed2, "✅ Oxygen should be fixed when PINs entered at both locations!"
    assert len(active_sabotages) == 0, "✅ Oxygen should be fixed after both PINs!"

    print("   ✅ Test passed: Oxygen requires PIN codes at both O2 and Admin")

def test_single_person_fixes():
    """Test that lights and communications can be fixed by single person"""
    print("\n🧪 Test 4: Single-person fixes")

    sabotage_manager = SabotageManager()

    # Wait for initial cooldown to expire
    for _ in range(15):  # Wait 15 ticks to clear initial cooldown
        sabotage_manager.tick()

    # Test lights
    sabotage_manager.activate_sabotage(SabotageType.LIGHTS)
    fixed_lights = sabotage_manager.fix_sabotage(SabotageType.LIGHTS, "Red", "Electrical")
    print(f"   Lights fixed by one person: {fixed_lights}")
    assert fixed_lights, "❌ Lights should be fixable by one person!"

    # Wait for cooldown after first sabotage
    for _ in range(35):  # Wait 35 ticks for cooldown
        sabotage_manager.tick()

    # Test communications
    sabotage_manager.activate_sabotage(SabotageType.COMMUNICATIONS)
    fixed_comms = sabotage_manager.fix_sabotage(SabotageType.COMMUNICATIONS, "Blue", "Communications")
    print(f"   Communications fixed by one person: {fixed_comms}")
    assert fixed_comms, "❌ Communications should be fixable by one person!"

    print("   ✅ Test passed: Single-person fixes work correctly")

def test_oxygen_single_person_fix():
    """Test that one person can fix oxygen by visiting both locations"""
    print("\n🧪 Test 4.5: One person can fix oxygen by visiting both locations")

    sabotage_manager = SabotageManager()

    # Wait for initial cooldown to expire
    for _ in range(15):
        sabotage_manager.tick()

    # Activate oxygen sabotage
    success = sabotage_manager.activate_sabotage(SabotageType.OXYGEN)
    print(f"   Oxygen sabotage activated: {success}")
    assert success, "❌ Should be able to activate oxygen sabotage!"

    # Same person enters PIN at O2
    fixed1 = sabotage_manager.fix_sabotage(SabotageType.OXYGEN, "Red", "O2")
    print(f"   Red enters PIN at O2: {fixed1}")
    assert not fixed1, "❌ Should not be complete after just O2!"

    # Same person runs to Admin and enters PIN there
    fixed2 = sabotage_manager.fix_sabotage(SabotageType.OXYGEN, "Red", "Admin")
    print(f"   Red enters PIN at Admin: {fixed2}")
    assert fixed2, "✅ Should be fixed after Red enters both PINs!"

    active_sabotages = sabotage_manager.get_active_sabotages()
    assert len(active_sabotages) == 0, "✅ Oxygen should be completely fixed!"

    print("   ✅ Test passed: One person can fix oxygen by visiting both locations")

def test_door_sabotage_mechanics():
    """Test door sabotage mechanics (mutually exclusive with other sabotages on Skeld)"""
    print("\n🧪 Test 4.6: Door sabotage mechanics (Skeld rules)")

    sabotage_manager = SabotageManager()

    # Wait for initial cooldown to expire
    for _ in range(15):
        sabotage_manager.tick()

    # Activate door sabotage
    success1 = sabotage_manager.activate_sabotage(SabotageType.DOORS, affected_rooms=["Cafeteria"])
    print(f"   Door sabotage activated: {success1}")
    assert success1, "❌ Should be able to activate door sabotage!"

    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   Active sabotages after door: {[s.value for s in active_sabotages]}")

    # Try to activate lights while doors are active - should fail on Skeld
    success2 = sabotage_manager.activate_sabotage(SabotageType.LIGHTS)
    print(f"   Lights sabotage while doors active: {success2}")
    assert not success2, "❌ Should NOT be able to sabotage lights while doors are active on Skeld!"

    # Wait for doors to expire (10 seconds)
    for _ in range(12):  # Wait for doors to expire and cooldown
        sabotage_manager.tick()

    active_sabotages = sabotage_manager.get_active_sabotages()
    print(f"   Active sabotages after doors expire: {[s.value for s in active_sabotages]}")

    # Now should be able to activate lights
    success3 = sabotage_manager.activate_sabotage(SabotageType.LIGHTS)
    print(f"   Lights sabotage after doors expire: {success3}")
    assert success3, "✅ Should be able to sabotage lights after doors expire!"

    # Try to activate doors while lights are active - should fail
    for _ in range(35):  # Wait for cooldown
        sabotage_manager.tick()

    success4 = sabotage_manager.activate_sabotage(SabotageType.DOORS, affected_rooms=["Electrical"])
    print(f"   Door sabotage while lights active: {success4}")
    assert not success4, "❌ Should NOT be able to sabotage doors while lights are active on Skeld!"

    print("   ✅ Test passed: Doors and other sabotages are mutually exclusive on Skeld")

def test_integrated_crisis_scenario():
    """Test integrated scenario with crisis and multi-person fix"""
    print("\n🧪 Test 5: Integrated crisis scenario")
    
    settings = DEFAULT_SETTINGS.copy()
    rooms = create_skeld_map()
    reactor_room = rooms["Reactor"]
    
    # Create players in reactor room
    impostor_agent = TestAgent("Red", ["sabotage_reactor"])
    crewmate1_agent = TestAgent("Blue", ["fix_reactor", "idle", "idle"])
    crewmate2_agent = TestAgent("Green", ["idle", "fix_reactor", "idle"])
    
    impostor_player = Player("Red", IMPOSTOR, reactor_room, impostor_agent)
    crewmate1_player = Player("Blue", CREWMATE, reactor_room, crewmate1_agent)
    crewmate2_player = Player("Green", CREWMATE, reactor_room, crewmate2_agent)
    
    players = [impostor_player, crewmate1_player, crewmate2_player]
    game = SimulatedGame(players, settings)
    
    print("   Running integrated scenario...")
    
    # Tick 1: Impostor sabotages reactor
    game.run_tick()
    active_sabotages = game.sabotage_manager.get_active_sabotages()
    print(f"   After tick 1: {[s.value for s in active_sabotages]} active")
    
    # Tick 2: First crewmate tries to fix (should not work alone)
    game.run_tick()
    active_sabotages = game.sabotage_manager.get_active_sabotages()
    print(f"   After tick 2: {[s.value for s in active_sabotages]} active")
    
    # Tick 3: Second crewmate joins fix (should work)
    game.run_tick()
    active_sabotages = game.sabotage_manager.get_active_sabotages()
    print(f"   After tick 3: {[s.value for s in active_sabotages]} active")
    
    # Should be fixed now
    assert len(active_sabotages) == 0, "❌ Reactor should be fixed by two people!"
    assert not game.is_game_over(), "❌ Game should continue after successful fix!"
    
    print("   ✅ Test passed: Integrated crisis scenario works correctly")

def main():
    print("🔧 Testing Advanced Sabotage Mechanics")
    print("=" * 50)
    
    try:
        test_crisis_timer_impostor_victory()
        test_reactor_multi_person_fix()
        test_oxygen_dual_location_fix()
        test_single_person_fixes()
        test_oxygen_single_person_fix()
        test_door_sabotage_mechanics()
        test_integrated_crisis_scenario()
        
        print("\n" + "=" * 50)
        print("🎉 All advanced sabotage tests passed!")
        print("✅ Crisis timers cause impostor victory")
        print("✅ Reactor requires 2 people at same location")
        print("✅ Oxygen requires PIN codes at both O2 and Admin")
        print("✅ Single-person fixes work correctly")
        print("✅ One person can fix oxygen by visiting both locations")
        print("✅ Doors and other sabotages are mutually exclusive on Skeld")
        print("✅ Integrated scenarios work properly")
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
