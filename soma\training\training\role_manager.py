"""
Advanced Role Management for Among Us AI Training
Integrates comprehensive role system with existing training
"""

import random
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from core.role import (
    CREWMATE, IMPOSTOR, ENGINEER, SCIENTIST, G<PERSON><PERSON>DIAN_ANGEL, 
    NOISEMAKER, TRACKER, SHAPESHIFTER, PHANTOM
)
from core.roles import ALL_ROLES, RoleType, RoleRarity, create_role_distribution

class RoleComplexity(Enum):
    """Role complexity levels for training progression"""
    BASIC = "basic"           # Crewmate, Impostor only
    INTERMEDIATE = "intermediate"  # Add Engineer, Scientist, Shapeshifter
    ADVANCED = "advanced"     # Add Guardian Angel, Tracker, Phantom
    EXPERT = "expert"         # All roles including modded
    CUSTOM = "custom"         # User-defined role set

@dataclass
class RoleConfig:
    """Configuration for role distribution in training"""
    complexity: RoleComplexity
    enable_special_crewmates: bool = True
    enable_special_impostors: bool = True
    enable_neutral_roles: bool = False
    role_probabilities: Optional[Dict[str, float]] = None
    max_special_roles_per_game: int = 4
    
    def __post_init__(self):
        if self.role_probabilities is None:
            self.role_probabilities = self._get_default_probabilities()
    
    def _get_default_probabilities(self) -> Dict[str, float]:
        """Get default role probabilities based on complexity"""
        
        if self.complexity == RoleComplexity.BASIC:
            return {"Crewmate": 1.0, "Impostor": 1.0}
        
        elif self.complexity == RoleComplexity.INTERMEDIATE:
            return {
                "Crewmate": 0.6, "Impostor": 0.7,
                "Engineer": 0.3, "Scientist": 0.3,
                "Shapeshifter": 0.3
            }
        
        elif self.complexity == RoleComplexity.ADVANCED:
            return {
                "Crewmate": 0.4, "Impostor": 0.5,
                "Engineer": 0.25, "Scientist": 0.25, "Guardian Angel": 0.2,
                "Noisemaker": 0.15, "Tracker": 0.2,
                "Shapeshifter": 0.3, "Phantom": 0.2
            }
        
        elif self.complexity == RoleComplexity.EXPERT:
            return {
                "Crewmate": 0.3, "Impostor": 0.4,
                "Engineer": 0.2, "Scientist": 0.2, "Guardian Angel": 0.15,
                "Noisemaker": 0.1, "Tracker": 0.15,
                "Shapeshifter": 0.25, "Phantom": 0.15,
                "Jester": 0.05, "Survivor": 0.05
            }
        
        else:  # CUSTOM
            return {}

class RoleManager:
    """Manages role assignment and progression for training"""
    
    def __init__(self, config: RoleConfig):
        self.config = config
        self.role_usage_stats = {}
        self.role_performance_stats = {}
        
        # Map role names to legacy role objects
        self.legacy_role_map = {
            "Crewmate": CREWMATE,
            "Impostor": IMPOSTOR,
            "Engineer": ENGINEER,
            "Scientist": SCIENTIST,
            "Guardian Angel": GUARDIAN_ANGEL,
            "Noisemaker": NOISEMAKER,
            "Tracker": TRACKER,
            "Shapeshifter": SHAPESHIFTER,
            "Phantom": PHANTOM
        }
    
    def assign_roles(self, num_players: int, num_impostors: int) -> List:
        """Assign roles to players based on configuration"""
        
        if self.config.complexity == RoleComplexity.BASIC:
            return self._assign_basic_roles(num_players, num_impostors)
        else:
            return self._assign_advanced_roles(num_players, num_impostors)
    
    def _assign_basic_roles(self, num_players: int, num_impostors: int) -> List:
        """Assign only basic Crewmate and Impostor roles"""
        
        roles = []
        
        # Assign impostors
        for _ in range(num_impostors):
            roles.append(IMPOSTOR)
        
        # Assign crewmates
        for _ in range(num_players - num_impostors):
            roles.append(CREWMATE)
        
        random.shuffle(roles)
        return roles
    
    def _assign_advanced_roles(self, num_players: int, num_impostors: int) -> List:
        """Assign roles with special abilities"""
        
        roles = []
        probabilities = self.config.role_probabilities
        
        # Assign impostor roles
        impostor_roles = ["Impostor"]
        if self.config.enable_special_impostors:
            impostor_roles.extend(["Shapeshifter", "Phantom"])
        
        for _ in range(num_impostors):
            # Choose impostor role based on probabilities
            available_roles = [r for r in impostor_roles if probabilities.get(r, 0) > 0]
            if available_roles:
                weights = [probabilities.get(r, 0.1) for r in available_roles]
                chosen_role = random.choices(available_roles, weights=weights)[0]
                roles.append(self.legacy_role_map[chosen_role])
            else:
                roles.append(IMPOSTOR)
        
        # Assign crewmate roles
        num_crewmates = num_players - num_impostors
        crewmate_roles = ["Crewmate"]
        
        if self.config.enable_special_crewmates:
            crewmate_roles.extend([
                "Engineer", "Scientist", "Guardian Angel", 
                "Noisemaker", "Tracker"
            ])
        
        special_roles_assigned = 0
        max_special = min(self.config.max_special_roles_per_game, num_crewmates)
        
        for _ in range(num_crewmates):
            # Decide if this should be a special role
            if (special_roles_assigned < max_special and 
                random.random() < 0.6 and  # 60% chance for special role
                self.config.enable_special_crewmates):
                
                # Choose special crewmate role
                special_roles = [r for r in crewmate_roles[1:] if probabilities.get(r, 0) > 0]
                if special_roles:
                    weights = [probabilities.get(r, 0.1) for r in special_roles]
                    chosen_role = random.choices(special_roles, weights=weights)[0]
                    roles.append(self.legacy_role_map[chosen_role])
                    special_roles_assigned += 1
                else:
                    roles.append(CREWMATE)
            else:
                roles.append(CREWMATE)
        
        random.shuffle(roles)
        return roles
    
    def get_role_abilities(self, role_name: str) -> List[str]:
        """Get abilities for a role"""
        role_info = ALL_ROLES.get(role_name)
        if role_info:
            return [ability.name for ability in role_info.abilities]
        return []
    
    def can_role_perform_action(self, role_name: str, action: str) -> bool:
        """Check if role can perform specific action"""
        legacy_role = self.legacy_role_map.get(role_name)
        if not legacy_role:
            return False
        
        if action == "kill":
            return "kill" in getattr(legacy_role, 'special_abilities', [])
        elif action == "vent":
            return legacy_role.can_vent
        elif action == "sabotage":
            return legacy_role.can_sabotage
        elif action == "task":
            return legacy_role.has_real_tasks
        
        return action in getattr(legacy_role, 'special_abilities', [])
    
    def record_role_performance(self, role_name: str, won: bool, performance_score: float):
        """Record performance statistics for a role"""
        
        if role_name not in self.role_performance_stats:
            self.role_performance_stats[role_name] = {
                'games_played': 0,
                'wins': 0,
                'total_performance': 0.0
            }
        
        stats = self.role_performance_stats[role_name]
        stats['games_played'] += 1
        if won:
            stats['wins'] += 1
        stats['total_performance'] += performance_score
        
        # Track usage
        self.role_usage_stats[role_name] = self.role_usage_stats.get(role_name, 0) + 1
    
    def get_role_statistics(self) -> Dict:
        """Get comprehensive role statistics"""
        
        stats = {}
        
        for role_name, perf_stats in self.role_performance_stats.items():
            win_rate = perf_stats['wins'] / max(perf_stats['games_played'], 1)
            avg_performance = perf_stats['total_performance'] / max(perf_stats['games_played'], 1)
            
            stats[role_name] = {
                'games_played': perf_stats['games_played'],
                'win_rate': win_rate,
                'average_performance': avg_performance,
                'usage_frequency': self.role_usage_stats.get(role_name, 0)
            }
        
        return stats
    
    def should_increase_complexity(self, episode: int, performance_threshold: float = 0.6) -> bool:
        """Determine if role complexity should be increased"""
        
        if self.config.complexity == RoleComplexity.EXPERT:
            return False  # Already at max complexity
        
        # Check if basic roles are being played well
        basic_roles = ["Crewmate", "Impostor"]
        basic_performance = []
        
        for role in basic_roles:
            if role in self.role_performance_stats:
                stats = self.role_performance_stats[role]
                if stats['games_played'] >= 10:  # Minimum games for evaluation
                    win_rate = stats['wins'] / stats['games_played']
                    basic_performance.append(win_rate)
        
        if basic_performance and sum(basic_performance) / len(basic_performance) >= performance_threshold:
            return True
        
        return False
    
    def advance_complexity(self):
        """Advance to next complexity level"""
        
        complexity_progression = [
            RoleComplexity.BASIC,
            RoleComplexity.INTERMEDIATE,
            RoleComplexity.ADVANCED,
            RoleComplexity.EXPERT
        ]
        
        current_idx = complexity_progression.index(self.config.complexity)
        if current_idx < len(complexity_progression) - 1:
            self.config.complexity = complexity_progression[current_idx + 1]
            self.config.role_probabilities = self.config._get_default_probabilities()
            
            print(f"🎭 Role complexity advanced to: {self.config.complexity.value}")
            return True
        
        return False
    
    def get_recommended_roles_for_training(self, stage: str) -> RoleComplexity:
        """Get recommended role complexity for training stage"""
        
        stage_complexity_map = {
            "basic_movement": RoleComplexity.BASIC,
            "task_completion": RoleComplexity.BASIC,
            "sabotage_basics": RoleComplexity.INTERMEDIATE,
            "crisis_management": RoleComplexity.INTERMEDIATE,
            "basic_social": RoleComplexity.ADVANCED,
            "advanced_deduction": RoleComplexity.ADVANCED,
            "master_level": RoleComplexity.EXPERT
        }
        
        return stage_complexity_map.get(stage, RoleComplexity.BASIC)

def create_role_config_for_stage(stage: str, enable_special_roles: bool = True) -> RoleConfig:
    """Create appropriate role configuration for training stage"""
    
    role_manager = RoleManager(RoleConfig(RoleComplexity.BASIC))
    complexity = role_manager.get_recommended_roles_for_training(stage)
    
    return RoleConfig(
        complexity=complexity,
        enable_special_crewmates=enable_special_roles,
        enable_special_impostors=enable_special_roles,
        enable_neutral_roles=complexity == RoleComplexity.EXPERT,
        max_special_roles_per_game=4 if complexity in [RoleComplexity.ADVANCED, RoleComplexity.EXPERT] else 2
    )

def create_balanced_role_distribution(num_players: int, num_impostors: int, 
                                    complexity: RoleComplexity = RoleComplexity.INTERMEDIATE) -> List:
    """Create a balanced role distribution for training"""
    
    config = RoleConfig(complexity=complexity)
    manager = RoleManager(config)
    
    return manager.assign_roles(num_players, num_impostors)

# Export commonly used functions
__all__ = [
    'RoleManager', 'RoleConfig', 'RoleComplexity',
    'create_role_config_for_stage', 'create_balanced_role_distribution'
]
