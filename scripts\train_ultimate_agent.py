#!/usr/bin/env python3
"""
Ultimate Among Us AI Training System
Combines advanced RL, curriculum learning, analytics, and self-play
"""

import os
# Fix OpenMP warning before importing torch
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import argparse
from datetime import datetime
from typing import Dict, List, Optional

from agents.ultimate_rl_agent import UltimateAmongUsAgent
from training.curriculum_learning import AdaptiveCurriculumLearning, CurriculumStage
from analytics.training_analytics import AmongUsTrainingAnalytics
from core.llm_communication import OllamaClient
from core.sim_env import SimulatedGame

class UltimateAmongUsTrainer:
    """Ultimate training system for Among Us AI"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device(config.get("device", "cpu"))
        
        # Initialize LLM client if enabled
        self.ollama_client = None
        if config.get("enable_llm", False):
            try:
                self.ollama_client = OllamaClient()
                print("✅ LLM communication enabled")
            except Exception as e:
                print(f"⚠️  LLM communication disabled: {e}")
        
        # Create main RL agent
        self.agent = UltimateAmongUsAgent(
            player_color="Red",
            role_name="Crewmate",  # Will be dynamically assigned
            learning_rate=config.get("learning_rate", 1e-4),
            device=self.device,
            ollama_client=self.ollama_client
        )
        
        # Initialize curriculum learning
        self.curriculum = AdaptiveCurriculumLearning(self.agent, self.device)
        
        # Initialize analytics
        self.analytics = AmongUsTrainingAnalytics(
            save_dir=config.get("analytics_dir", "training_analytics")
        )
        
        # Training parameters
        self.total_episodes = config.get("total_episodes", 10000)
        self.save_frequency = config.get("save_frequency", 100)
        self.eval_frequency = config.get("eval_frequency", 50)
        self.viz_frequency = config.get("viz_frequency", 200)
        
        # Create save directory
        self.save_dir = config.get("save_dir", f"models/ultimate_agent_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.save_dir, exist_ok=True)
        
        # Save config
        import json
        with open(os.path.join(self.save_dir, "config.json"), "w") as f:
            json.dump(config, f, indent=2)
    
    def train(self):
        """Main training loop with curriculum learning and analytics"""
        
        print("🚀 Starting Ultimate Among Us AI Training")
        print(f"📊 Device: {self.device}")
        print(f"🎯 Total Episodes: {self.total_episodes}")
        print(f"🧠 LLM Enabled: {self.ollama_client is not None}")
        print(f"💾 Save Directory: {self.save_dir}")
        print("=" * 60)
        
        episode = 0
        
        try:
            while episode < self.total_episodes:
                # Get current curriculum configuration
                curriculum_config = self.curriculum.get_current_config()
                
                # Create training game for current curriculum stage
                game = self.curriculum.create_training_game(curriculum_config)
                
                # Update agent's role for this episode
                agent_player = next(p for p in game.players if p.agent == self.agent)
                self.agent.role_name = agent_player.role.name
                
                # Run episode
                episode_data = self._run_training_episode(game, curriculum_config)
                
                # Record episode data
                self.analytics.record_episode(episode_data)
                self.curriculum.record_episode_result(
                    episode_data['won'], 
                    episode_data.get('performance_metrics', {})
                )
                
                # Progress logging
                if episode % 10 == 0:
                    self._log_progress(episode, episode_data)
                
                # Curriculum advancement check
                if self.curriculum.should_advance_stage():
                    self.curriculum.advance_to_next_stage()
                    curriculum_status = self.curriculum.get_curriculum_status()
                    self.analytics.record_curriculum_progress(curriculum_status)
                
                # Periodic saves and evaluations
                if episode % self.save_frequency == 0 and episode > 0:
                    self._save_checkpoint(episode)
                
                if episode % self.eval_frequency == 0 and episode > 0:
                    self._run_evaluation(episode)
                
                if episode % self.viz_frequency == 0 and episode > 0:
                    self._generate_visualizations(episode)
                
                episode += 1
        
        except KeyboardInterrupt:
            print("\n⏸️  Training interrupted by user")
        except Exception as e:
            print(f"\n❌ Training failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._finalize_training(episode)
    
    def _run_training_episode(self, game: SimulatedGame, curriculum_config) -> Dict:
        """Run a single training episode"""
        
        # Track episode data
        episode_start_time = datetime.now()
        actions_taken = []
        communication_data = {}
        performance_metrics = {}
        
        # Get agent player
        agent_player = next(p for p in game.players if p.agent == self.agent)
        initial_observation = game._get_observation(agent_player)
        
        step_count = 0
        max_steps = 200
        
        while not game.is_game_over() and step_count < max_steps:
            # Store previous observation for learning
            prev_observation = game._get_observation(agent_player)
            
            # Run game tick
            game.run_tick()
            
            # Get new observation and calculate reward
            new_observation = game._get_observation(agent_player)
            reward = self._calculate_episode_reward(
                agent_player, game, prev_observation, new_observation, curriculum_config
            )
            
            # Agent learning
            self.agent.learn_from_experience(reward, new_observation, game.is_game_over())
            
            # Track actions
            if hasattr(self.agent, 'last_action') and self.agent.last_action is not None:
                action_name = self.agent.idx_to_action.get(self.agent.last_action, 'unknown')
                actions_taken.append(action_name)
            
            step_count += 1
        
        # Calculate episode results
        winner = game.get_winner() if game.is_game_over() else "None"
        won = self._did_agent_win(agent_player, winner)
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics(
            agent_player, game, actions_taken, step_count
        )
        
        episode_data = {
            'episode_duration': (datetime.now() - episode_start_time).total_seconds(),
            'steps': step_count,
            'role': agent_player.role.name,
            'won': won,
            'winner': winner,
            'reward': self.agent.current_episode_reward,
            'actions_taken': actions_taken,
            'performance_metrics': performance_metrics,
            'curriculum_stage': curriculum_config.stage.value,
            'opponent_types': curriculum_config.opponent_types,
            'game_settings': curriculum_config.game_settings
        }
        
        return episode_data
    
    def _calculate_episode_reward(self, player, game, prev_obs, new_obs, curriculum_config) -> float:
        """Calculate reward for the episode step"""
        
        base_reward = 0.0
        
        # Survival reward
        if player.alive:
            base_reward += 0.1
        
        # Role-specific rewards
        if player.role.name in ["Impostor", "Shapeshifter", "Phantom"]:
            base_reward += self._calculate_impostor_reward(player, game, prev_obs, new_obs)
        else:
            base_reward += self._calculate_crewmate_reward(player, game, prev_obs, new_obs)
        
        # Game outcome rewards
        if game.is_game_over():
            winner = game.get_winner()
            if self._did_agent_win(player, winner):
                base_reward += 10.0
            else:
                base_reward -= 5.0
        
        # Apply curriculum-specific weighting
        game_result = {
            'base_reward': base_reward,
            'survival': 1.0 if player.alive else 0.0,
            'win': 1.0 if game.is_game_over() and self._did_agent_win(player, game.get_winner()) else 0.0
        }
        
        return self.curriculum.calculate_curriculum_reward(base_reward, game_result, curriculum_config)
    
    def _calculate_impostor_reward(self, player, game, prev_obs, new_obs) -> float:
        """Calculate impostor-specific rewards"""
        reward = 0.0
        
        # Kill rewards
        prev_living = prev_obs.get("living_players", 0)
        new_living = new_obs.get("living_players", 0)
        if new_living < prev_living:
            reward += 5.0
        
        # Sabotage rewards
        prev_sabotages = len(prev_obs.get("active_sabotages", []))
        new_sabotages = len(new_obs.get("active_sabotages", []))
        if new_sabotages > prev_sabotages:
            reward += 3.0
        
        # Avoid detection
        if not player.alive and player in game.dead_players:
            reward -= 8.0  # Penalty for being caught
        
        return reward
    
    def _calculate_crewmate_reward(self, player, game, prev_obs, new_obs) -> float:
        """Calculate crewmate-specific rewards"""
        reward = 0.0
        
        # Task completion
        prev_tasks = prev_obs.get("completed_tasks", 0)
        new_tasks = new_obs.get("completed_tasks", 0)
        if new_tasks > prev_tasks:
            reward += 3.0
        
        # Sabotage fixing
        prev_sabotages = len(prev_obs.get("active_sabotages", []))
        new_sabotages = len(new_obs.get("active_sabotages", []))
        if new_sabotages < prev_sabotages:
            reward += 4.0
        
        # Survival bonus
        if player.alive:
            reward += 0.5
        
        return reward
    
    def _did_agent_win(self, agent_player, winner: str) -> bool:
        """Check if the agent won"""
        if winner == "None":
            return False
        
        if agent_player.role.name in ["Impostor", "Shapeshifter", "Phantom"]:
            return winner == "Impostors"
        else:
            return winner == "Crewmates"
    
    def _calculate_performance_metrics(self, player, game, actions, steps) -> Dict:
        """Calculate detailed performance metrics"""
        
        metrics = {
            'survival_time': steps,
            'actions_per_step': len(actions) / max(steps, 1),
            'role_specific_score': 0.0
        }
        
        # Role-specific metrics
        if player.role.name in ["Impostor", "Shapeshifter", "Phantom"]:
            metrics.update({
                'kills_made': actions.count('kill'),
                'sabotages_used': sum(1 for a in actions if 'sabotage' in a),
                'vents_used': actions.count('vent')
            })
        else:
            metrics.update({
                'tasks_attempted': actions.count('task'),
                'sabotages_fixed': sum(1 for a in actions if 'fix' in a),
                'security_usage': actions.count('use_security') + actions.count('use_admin')
            })
        
        return metrics
    
    def _log_progress(self, episode: int, episode_data: Dict):
        """Log training progress"""
        
        curriculum_status = self.curriculum.get_curriculum_status()
        performance_stats = self.agent.get_performance_stats()
        
        print(f"Episode {episode:5d} | "
              f"Stage: {curriculum_status['current_stage']:15s} | "
              f"Role: {episode_data['role']:8s} | "
              f"Won: {'✅' if episode_data['won'] else '❌'} | "
              f"Reward: {episode_data['reward']:6.2f} | "
              f"Steps: {episode_data['steps']:3d} | "
              f"ε: {performance_stats.get('epsilon', 0):.3f}")
    
    def _save_checkpoint(self, episode: int):
        """Save training checkpoint"""
        
        checkpoint_path = os.path.join(self.save_dir, f"checkpoint_episode_{episode}.pt")
        self.agent.save_model(checkpoint_path)
        
        # Save curriculum state
        curriculum_path = os.path.join(self.save_dir, f"curriculum_episode_{episode}.json")
        curriculum_status = self.curriculum.get_curriculum_status()
        
        import json
        with open(curriculum_path, 'w') as f:
            json.dump(curriculum_status, f, indent=2, default=str)
        
        print(f"💾 Checkpoint saved at episode {episode}")
    
    def _run_evaluation(self, episode: int):
        """Run evaluation against fixed opponents"""
        
        print(f"📊 Running evaluation at episode {episode}...")
        
        # Run evaluation games
        eval_results = []
        for _ in range(10):  # 10 evaluation games
            # Create evaluation game with fixed opponents
            curriculum_config = self.curriculum.get_current_config()
            game = self.curriculum.create_training_game(curriculum_config)
            
            # Set agent to evaluation mode
            self.agent.training = False
            
            # Run game
            episode_data = self._run_training_episode(game, curriculum_config)
            eval_results.append(episode_data['won'])
            
            # Reset to training mode
            self.agent.training = True
        
        eval_win_rate = sum(eval_results) / len(eval_results)
        print(f"📈 Evaluation win rate: {eval_win_rate:.3f}")
        
        # Record evaluation results
        self.analytics.record_episode({
            'episode_type': 'evaluation',
            'win_rate': eval_win_rate,
            'episode': episode
        })
    
    def _generate_visualizations(self, episode: int):
        """Generate training visualizations"""
        
        print(f"📊 Generating visualizations at episode {episode}...")
        
        try:
            self.analytics.create_training_visualizations()
            report = self.analytics.generate_comprehensive_report()
            
            print(f"📈 Training report generated with {len(report)} sections")
            
        except Exception as e:
            print(f"⚠️  Visualization generation failed: {e}")
    
    def _finalize_training(self, final_episode: int):
        """Finalize training and save results"""
        
        print(f"\n🏁 Training completed after {final_episode} episodes")
        
        # Save final model
        final_model_path = os.path.join(self.save_dir, "final_model.pt")
        self.agent.save_model(final_model_path)
        
        # Generate final report
        try:
            final_report = self.analytics.generate_comprehensive_report()
            self.analytics.create_training_visualizations()
            
            print("📊 Final training report and visualizations generated")
            
            # Print summary
            performance_stats = self.agent.get_performance_stats()
            curriculum_status = self.curriculum.get_curriculum_status()
            
            print("\n" + "=" * 60)
            print("🎯 TRAINING SUMMARY")
            print("=" * 60)
            print(f"Total Episodes: {final_episode}")
            print(f"Final Curriculum Stage: {curriculum_status['current_stage']}")
            print(f"Stages Completed: {curriculum_status['completed_stages']}/{curriculum_status['total_stages']}")
            
            if 'impostor_win_rate' in performance_stats:
                print(f"Impostor Win Rate: {performance_stats['impostor_win_rate']:.3f}")
            if 'crewmate_win_rate' in performance_stats:
                print(f"Crewmate Win Rate: {performance_stats['crewmate_win_rate']:.3f}")
            
            print(f"Average Reward: {performance_stats.get('average_reward', 0):.2f}")
            print(f"Final Exploration Rate: {performance_stats.get('epsilon', 0):.3f}")
            
            # Print recommendations
            if 'recommendations' in final_report:
                print("\n🔍 RECOMMENDATIONS:")
                for rec in final_report['recommendations']:
                    print(f"  • {rec}")
            
        except Exception as e:
            print(f"⚠️  Final report generation failed: {e}")
        
        print(f"\n💾 All results saved to: {self.save_dir}")
        print("🎉 Training completed successfully!")

def main():
    parser = argparse.ArgumentParser(description="Train Ultimate Among Us AI")
    parser.add_argument("--episodes", type=int, default=5000, help="Total training episodes")
    parser.add_argument("--device", type=str, default="auto", help="Device (cpu/cuda/auto)")
    parser.add_argument("--lr", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--llm", action="store_true", help="Enable LLM communication")
    parser.add_argument("--save-dir", type=str, default=None, help="Save directory")
    parser.add_argument("--analytics-dir", type=str, default="training_analytics", help="Analytics directory")
    
    args = parser.parse_args()
    
    # Create configuration
    config = {
        "total_episodes": args.episodes,
        "learning_rate": args.lr,
        "enable_llm": args.llm,
        "save_frequency": 100,
        "eval_frequency": 50,
        "viz_frequency": 200,
        "analytics_dir": args.analytics_dir
    }
    
    if args.device == "auto":
        config["device"] = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        config["device"] = args.device
    
    if args.save_dir:
        config["save_dir"] = args.save_dir
    
    print("🎮 Ultimate Among Us AI Training Configuration:")
    print(f"   Episodes: {config['total_episodes']}")
    print(f"   Device: {config['device']}")
    print(f"   Learning Rate: {config['learning_rate']}")
    print(f"   LLM Communication: {config['enable_llm']}")
    print(f"   Analytics Directory: {config['analytics_dir']}")
    
    # Create and run trainer
    trainer = UltimateAmongUsTrainer(config)
    trainer.train()

if __name__ == "__main__":
    main()
