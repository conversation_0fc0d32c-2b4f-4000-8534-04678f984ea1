from typing import List, Dict

class Room:
    def __init__(
        self,
        name: str,
        has_admin_table: bool = False,
        has_emergency_button: bool = False,
        has_vitals: bool = False,
        has_security_cameras: bool = False,
        has_doorlog: bool = False,
        can_fix_lights: bool = False,
        can_fix_oxygen: bool = False,
        can_fix_reactor: bool = False,
        can_fix_comms: bool = False
    ):
        self.name = name
        self.tasks: List[str] = []
        self.connected_rooms: List["Room"] = []
        self.vents_to: List["Room"] = []

        # Security and utility features
        self.has_admin_table = has_admin_table
        self.has_emergency_button = has_emergency_button
        self.has_vitals = has_vitals
        self.has_security_cameras = has_security_cameras
        self.has_doorlog = has_doorlog

        # Sabotage fix capabilities
        self.can_fix_lights = can_fix_lights
        self.can_fix_oxygen = can_fix_oxygen
        self.can_fix_reactor = can_fix_reactor
        self.can_fix_comms = can_fix_comms

        # Room state
        self.lights_on = True
        self.doors_open = True
        self.door_log: List[Dict] = []  # Track player movements
        self.camera_feeds: List[str] = []  # Rooms visible from security cameras

    def connect(self, other_room: "Room"):
        """Create a bidirectional hallway connection between rooms."""
        if other_room not in self.connected_rooms:
            self.connected_rooms.append(other_room)
            other_room.connected_rooms.append(self)

    def add_vent(self, other_room: "Room"):
        """Create a bidirectional vent connection between rooms."""
        if other_room not in self.vents_to:
            self.vents_to.append(other_room)
        if self not in other_room.vents_to:
            other_room.vents_to.append(self)

    def add_camera_feed(self, room_name: str):
        """Add a room to the security camera feeds"""
        if room_name not in self.camera_feeds:
            self.camera_feeds.append(room_name)

    def log_player_movement(self, player_color: str, tick: int, action: str):
        """Log player movement for doorlog"""
        self.door_log.append({
            'player': player_color,
            'tick': tick,
            'action': action,
            'room': self.name
        })

        # Keep only recent entries (last 50 movements)
        if len(self.door_log) > 50:
            self.door_log = self.door_log[-50:]

    def get_recent_movements(self, last_n_ticks: int = 10) -> List[Dict]:
        """Get recent player movements"""
        current_tick = max([entry['tick'] for entry in self.door_log], default=0)
        return [entry for entry in self.door_log
                if current_tick - entry['tick'] <= last_n_ticks]

    def close_doors(self):
        """Close doors (sabotage)"""
        self.doors_open = False

    def open_doors(self):
        """Open doors (fix sabotage)"""
        self.doors_open = True

    def turn_off_lights(self):
        """Turn off lights (sabotage)"""
        self.lights_on = False

    def turn_on_lights(self):
        """Turn on lights (fix sabotage)"""
        self.lights_on = True

    def __repr__(self):
        return f"Room({self.name})"