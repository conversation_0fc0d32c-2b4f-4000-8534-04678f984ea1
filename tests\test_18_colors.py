#!/usr/bin/env python3
"""
Test the 18-color Among Us system
"""

from config.colors import (
    AMONG_US_COLORS, get_all_color_names, get_common_colors, 
    get_random_colors, get_lobby_colors, get_contrasting_colors,
    get_recommended_impostors, validate_lobby_configuration,
    RECOMMENDED_LOBBY_SIZES
)
from training.self_play import SelfPlayTrainer, SelfPlayConfig, SelfPlayMode

def test_color_system():
    """Test the 18-color system"""
    print("🎨 Testing 18-Color Among Us System")
    print("=" * 50)
    
    # Test all colors
    all_colors = get_all_color_names()
    print(f"📋 All {len(all_colors)} Colors:")
    for i, color in enumerate(all_colors):
        color_info = AMONG_US_COLORS[color]
        rarity_emoji = {"common": "⚪", "uncommon": "🟡", "rare": "🔴"}[color_info.rarity]
        print(f"   {i+1:2d}. {color:8s} {rarity_emoji} {color_info.hex_code} ({color_info.rarity})")
    
    # Test common colors
    common_colors = get_common_colors()
    print(f"\n⚪ Common Colors ({len(common_colors)}):")
    print(f"   {', '.join(common_colors)}")
    
    # Test lobby configurations
    print(f"\n🎮 Lobby Configurations:")
    for size_name, size in RECOMMENDED_LOBBY_SIZES.items():
        colors = get_lobby_colors(size)
        impostors = get_recommended_impostors(size)
        valid, msg = validate_lobby_configuration(size, impostors)
        
        print(f"   {size_name.title()} ({size} players):")
        print(f"      Colors: {', '.join(colors[:6])}{'...' if len(colors) > 6 else ''}")
        print(f"      Impostors: {impostors}")
        print(f"      Valid: {'✅' if valid else '❌'} {msg}")
    
    # Test random color selection
    print(f"\n🎲 Random Color Selection:")
    for lobby_size in [6, 10, 15]:
        random_colors = get_random_colors(lobby_size, prefer_common=True)
        print(f"   {lobby_size} players: {', '.join(random_colors)}")
    
    # Test contrasting colors
    print(f"\n🌈 Contrasting Color Combinations:")
    contrasting = get_contrasting_colors(avoid_similar=True)
    for i, combo in enumerate(contrasting):
        print(f"   Combo {i+1} ({len(combo)} colors): {', '.join(combo[:8])}{'...' if len(combo) > 8 else ''}")
    
    print("\n✅ Color system working perfectly!")

def test_large_lobby_training():
    """Test training with larger lobbies"""
    print("\n🏟️ Testing Large Lobby Training")
    print("=" * 40)
    
    # Test different lobby sizes
    lobby_configs = [
        {"size": 6, "rl_agents": 6, "name": "Small All-RL"},
        {"size": 10, "rl_agents": 8, "name": "Medium Mixed"},
        {"size": 15, "rl_agents": 12, "name": "Large Lobby"},
        {"size": 18, "rl_agents": 18, "name": "Maximum All-RL"}
    ]
    
    for config in lobby_configs:
        print(f"\n🎯 {config['name']} ({config['size']} players):")
        
        try:
            # Create self-play configuration
            self_play_config = SelfPlayConfig(
                mode=SelfPlayMode.PURE_SELF_PLAY,
                num_rl_agents=config['rl_agents'],
                agent_diversity=0.1
            )
            
            # Create trainer
            trainer = SelfPlayTrainer(self_play_config, device="cpu")
            
            print(f"   ✅ Trainer created with {len(trainer.agent_pool)} RL agents")
            
            # List agent colors
            agent_colors = [agent.player_color for agent in trainer.agent_pool]
            print(f"   🎨 Agent colors: {', '.join(agent_colors[:8])}{'...' if len(agent_colors) > 8 else ''}")
            
            # Test game creation
            game, rl_agents = trainer.create_self_play_game()
            
            print(f"   🎮 Game created:")
            print(f"      Total players: {len(game.players)}")
            print(f"      RL agents: {len(rl_agents)}")
            
            # Count roles
            impostors = sum(1 for p in game.players if p.role.name in ["Impostor", "Shapeshifter", "Phantom"])
            crewmates = len(game.players) - impostors
            
            print(f"      Impostors: {impostors}")
            print(f"      Crewmates: {crewmates}")
            print(f"      Ratio: {impostors/len(game.players):.1%}")
            
            # Validate configuration
            valid, msg = validate_lobby_configuration(len(game.players), impostors)
            print(f"      Valid: {'✅' if valid else '❌'} {msg}")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    print("\n✅ Large lobby training ready!")

def test_color_diversity():
    """Test color diversity in training"""
    print("\n🌈 Testing Color Diversity")
    print("=" * 30)
    
    # Test multiple game generations
    config = SelfPlayConfig(
        mode=SelfPlayMode.MIXED_OPPONENTS,
        num_rl_agents=10,
        num_scripted_agents=5,
        agent_diversity=0.2
    )
    
    trainer = SelfPlayTrainer(config, device="cpu")
    
    print(f"🤖 Created trainer with {len(trainer.agent_pool)} RL agents")
    
    # Generate multiple games to see color variety
    color_usage = {}
    
    for i in range(5):
        game, rl_agents = trainer.create_self_play_game()
        
        game_colors = []
        for p in game.players:
            if hasattr(p, 'color'):
                game_colors.append(p.color)
            elif hasattr(p.agent, 'player_color'):
                game_colors.append(p.agent.player_color)
            elif hasattr(p.agent, 'color'):
                game_colors.append(p.agent.color)
            else:
                game_colors.append(f"Unknown_{len(game_colors)}")
        
        print(f"\n🎮 Game {i+1} colors: {', '.join(game_colors)}")
        
        # Track color usage
        for color in game_colors:
            color_usage[color] = color_usage.get(color, 0) + 1
    
    print(f"\n📊 Color Usage Statistics:")
    sorted_colors = sorted(color_usage.items(), key=lambda x: x[1], reverse=True)
    for color, count in sorted_colors:
        color_info = AMONG_US_COLORS.get(color)
        rarity = color_info.rarity if color_info else "unknown"
        print(f"   {color:8s}: {count} games ({rarity})")
    
    print("\n✅ Color diversity working!")

def test_training_commands():
    """Show training commands with 18-color support"""
    print("\n🚀 Training Commands with 18-Color Support")
    print("=" * 50)
    
    commands = [
        {
            "name": "Small Classic Lobby",
            "command": "python train_advanced.py --episodes 100 --mode self_play --num-rl-agents 6 --lobby-size 6",
            "description": "6 players, classic Among Us experience"
        },
        {
            "name": "Medium Public Lobby", 
            "command": "python train_advanced.py --episodes 200 --mode mixed --num-rl-agents 8 --num-scripted-agents 2 --lobby-size 10",
            "description": "10 players, mixed opponents, public lobby simulation"
        },
        {
            "name": "Large Competitive Lobby",
            "command": "python train_advanced.py --episodes 300 --mode self_play --num-rl-agents 15 --lobby-size 15 --ruleset competitive",
            "description": "15 players, all RL agents, competitive rules"
        },
        {
            "name": "Maximum Chaos Mode",
            "command": "python train_advanced.py --episodes 150 --mode self_play --num-rl-agents 18 --lobby-size 18 --ruleset random --llm",
            "description": "18 players, all colors, random rules, LLM communication"
        },
        {
            "name": "Progressive Training",
            "command": "python train_advanced.py --episodes 500 --mode mixed --lobby-size 12 --ruleset progressive",
            "description": "12 players, difficulty increases over time"
        }
    ]
    
    for cmd in commands:
        print(f"\n🎯 {cmd['name']}:")
        print(f"   Description: {cmd['description']}")
        print(f"   Command: {cmd['command']}")
    
    print(f"\n💡 Benefits of 18-Color System:")
    print(f"✅ Support for larger lobbies (up to 18 players)")
    print(f"✅ More realistic training scenarios")
    print(f"✅ Better color diversity and recognition")
    print(f"✅ Matches official Among Us exactly")
    print(f"✅ Improved visual distinction between players")
    print(f"✅ Support for all official lobby configurations")

def main():
    print("🎨 Among Us 18-Color System Testing")
    print("=" * 60)
    
    try:
        test_color_system()
        test_large_lobby_training()
        test_color_diversity()
        test_training_commands()
        
        print("\n" + "=" * 60)
        print("🎉 18-Color System Fully Operational!")
        print("=" * 60)
        
        print("\n🎮 Key Features:")
        print("✅ All 18 official Among Us colors supported")
        print("✅ Lobby sizes from 4-18 players")
        print("✅ Automatic impostor count balancing")
        print("✅ Color rarity system (common/uncommon/rare)")
        print("✅ Contrasting color combinations")
        print("✅ Large lobby self-play training")
        print("✅ Mixed RL/scripted agent support")
        print("✅ Configuration validation")
        
        print("\n🚀 Ready for advanced Among Us AI training!")
        
    except Exception as e:
        print(f"\n❌ 18-color system test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
